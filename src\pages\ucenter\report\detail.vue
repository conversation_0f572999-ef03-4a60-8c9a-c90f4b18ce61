<template>
  <view class="content">
    <view class="header"></view>
    <view class="body">
      <view class="appoint_list">
        <view class="appoint_list-info">
          <view class="appoint_list-cent">
            <view class="appoint_list-cent-item">
              <view class="patient_info">就诊人</view>
              <view class="patient_infoText">{{ new Array(item.patient_name.length).join('*') + item.patient_name.substr(-1) }}</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">报告时间</view>
              <view class="patient_infoText">{{ options.report_time }}</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">报告名称</view>
              <view class="patient_infoText">{{ options.report_name }}</view>
            </view>
          </view>
        </view>
      </view>

      <view class="detail-title-content">
        <span class="detail-title">结果详情</span>
      </view>

      <view class="report-table">
        <uni-table border stripe emptyText="暂无更多数据">
          <!-- 表头行 -->
          <uni-tr>
            <uni-th align="center">项目</uni-th>
            <uni-th align="center">结果</uni-th>
            <uni-th align="left">参考值</uni-th>
          </uni-tr>
          <!-- 表格数据行 -->
          <uni-tr :data-id="index" :key="index" v-for="(it, index) in item.report_result_list">
            <uni-td>{{ it.lisresult_item_cname }}</uni-td>
            <uni-td
              >{{ it.llisresult_result }}
              <span v-if="it.lisresult_prompt.length > 0">{{ it.lisresult_prompt }}</span></uni-td
            >
            <uni-td
              ><span v-if="it.lisresult_ref_range.length > 0">{{
                it.lisresult_ref_range
              }}</span></uni-td
            >
          </uni-tr>
        </uni-table>
      </view>
    </view>
    <view class="footer"></view>
  </view>
</template>

<script>
  import { getReportDetail,sendGreenEnergy } from '@/api/waiting.js'
  const monitor = require('@/to/alipayLogger.js')
  export default {
    data() {
      return {
        options: {},
        id: '',
        item: {},
      }
    },
    onLoad: function (option) {
      //option为object类型，会序列化上个页⾯传递的参数
      this.options = option
      this.getDetail(option.report_id, option.pat_id)
    },
    methods: {
      async getDetail(report_id, pat_id) {
        /* uni.setStorageSync('userInfo', {
          id: 1917921,
          openid: 'o9U-FtzV8TAwXujFqZcOvgDfYPTI',
          userid: 1917921,
        }) */
        const userInfo = uni.getStorageSync('userInfo')

        const { statusCode, data } = await getReportDetail(null, {
          report_id: report_id,
          pat_id: pat_id,
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '暂无数据',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        this.item = data.data
        monitor.api({api:"报告查询",success:true,c1:"taSR_YL",time:200})
        let query = {
          scene: 'hoinquire',
          order_no: this.options.report_id+"DYTtoET",
          accessToken: uni.getStorageSync('mfrstreCode')||uni.getStorageSync('userInfo').access_token,
        }
        sendGreenEnergy(query).then((ret) => {
          if(ret.data.code == 1){
            uni.showToast({
              image: '/static/ali/ball.png',
              title: '本次预约获得'+ret.data.data.total_energy+'g绿色能量',
              duration: 2000
            });
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .appoint_list {
    border-radius: 10px;
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
    margin: 30rpx;
  }
  .appoint_list-info {
    padding: 10px 15px;
  }
  .appoint_list-cent-item {
    display: flex;
    display: -webkit-flex;
    -webkit-align-items: center;
    justify-content: space-between;
    font-size: 0.9em;
    line-height: 2.5;
  }
  .detail-title-content {
    width: 65%;
    margin: 1em auto 0;
    line-height: 1em;
    font-size: 14px;
    text-align: center;
    border-top: 2px solid #ffffff;
  }
  .detail-title {
    position: relative;
    top: -0.9em;
    padding: 0 0.55em;
    color: #000;
  }
  .report-table {
    margin: 0 30rpx;
  }
</style>
