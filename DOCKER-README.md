# Docker 部署指南

本项目支持使用 Docker 和 docker-compose 进行容器化部署，提供开发和生产两种环境配置。

## 📋 前置要求

- Docker (版本 20.10+)
- Docker Compose (版本 2.0+)

## 🚀 快速开始

### 方法一：使用脚本启动（推荐）

#### Linux/macOS:
```bash
# 给脚本执行权限
chmod +x docker-run.sh

# 启动开发环境
./docker-run.sh dev

# 查看日志
./docker-run.sh logs

# 停止服务
./docker-run.sh stop
```

#### Windows:
```cmd
# 启动开发环境
docker-run.bat dev

# 查看日志
docker-run.bat logs

# 停止服务
docker-run.bat stop
```

### 方法二：直接使用 docker-compose

#### 开发环境
```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up --build -d

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f

# 停止服务
docker-compose -f docker-compose.dev.yml down
```

#### 生产环境
```bash
# 启动生产环境
docker-compose -f docker-compose.prod.yml up --build -d

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f

# 停止服务
docker-compose -f docker-compose.prod.yml down
```

## 🌐 访问地址

### 开发环境
- 主地址: http://localhost:8080
- 带路径: http://localhost:8080/ui/

### 生产环境
- 主地址: http://localhost
- 带路径: http://localhost/ui/

## 📁 文件说明

### Docker 配置文件
- `Dockerfile` - 基础 Docker 镜像配置
- `Dockerfile.dev` - 开发环境专用配置
- `Dockerfile.prod` - 生产环境专用配置
- `docker-compose.yml` - 基础 compose 配置
- `docker-compose.dev.yml` - 开发环境 compose 配置
- `docker-compose.prod.yml` - 生产环境 compose 配置
- `.dockerignore` - Docker 构建忽略文件

### 辅助文件
- `nginx.conf` - 生产环境 Nginx 配置
- `docker-run.sh` - Linux/macOS 启动脚本
- `docker-run.bat` - Windows 启动脚本

## 🔧 环境配置

### 开发环境特性
- ✅ 热重载支持
- ✅ 源码挂载
- ✅ 实时调试
- ✅ 端口: 8080

### 生产环境特性
- ✅ 多阶段构建
- ✅ Nginx 静态服务
- ✅ Gzip 压缩
- ✅ 缓存优化
- ✅ 安全头配置
- ✅ 端口: 80

## 🛠️ 常用命令

```bash
# 查看运行中的容器
docker ps

# 进入容器内部
docker exec -it ertong-uni-app-dev sh

# 查看容器日志
docker logs ertong-uni-app-dev

# 重新构建镜像
docker-compose -f docker-compose.dev.yml up --build --force-recreate

# 清理所有相关资源
docker-compose -f docker-compose.dev.yml down --rmi all --volumes --remove-orphans
```

## 🐛 故障排除

### 端口被占用
```bash
# 查看端口占用
netstat -tulpn | grep :8080

# 或者使用其他端口
docker-compose -f docker-compose.dev.yml up -d --scale uni-app-dev=0
docker run -p 8081:8080 ertong-uni-app-dev
```

### 权限问题
```bash
# Linux/macOS 给脚本执行权限
chmod +x docker-run.sh

# 如果遇到 Docker 权限问题
sudo usermod -aG docker $USER
# 然后重新登录
```

### 构建失败
```bash
# 清理 Docker 缓存
docker system prune -a

# 重新构建
docker-compose -f docker-compose.dev.yml build --no-cache
```

## 📊 性能优化

### 开发环境优化
- 使用 volume 挂载避免重复安装依赖
- 启用文件监听轮询 (CHOKIDAR_USEPOLLING)
- 使用国内镜像源加速下载

### 生产环境优化
- 多阶段构建减小镜像体积
- Nginx 静态文件服务
- Gzip 压缩减少传输大小
- 合理的缓存策略

## 🔒 安全配置

生产环境已配置以下安全措施：
- X-Frame-Options: SAMEORIGIN
- X-XSS-Protection: 1; mode=block
- X-Content-Type-Options: nosniff
- Content-Security-Policy 配置
- Referrer-Policy 配置

## 📝 注意事项

1. 首次启动可能需要较长时间下载依赖
2. 开发环境支持热重载，修改代码会自动刷新
3. 生产环境需要重新构建才能看到代码变更
4. 确保 Docker 有足够的内存分配 (建议 4GB+)
