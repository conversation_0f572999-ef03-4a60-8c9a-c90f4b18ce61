## 3.2.2（2022-05-12）
3.2.2  

修复vue3引入js报错问题；  
增加draw可选项，可在每一阶段绘制前后扩展自定义方法，详见文档draw(options)；  
其他优化。
## 3.2.1（2022-05-09）
3.2.1  

已实现导入临时文件方法`toTempFilePath`；  
已实现保存二维码到本地或相册方法`save`；  
已实现生成完成回调事件`complete`；  
文档补充。
## 3.2.0（2022-05-07）
3.2.0 

适用所有支持canvas的前端应用和Node.js服务端；
微信小程序端切换为canvas2d；
支持绘制背景图片和前景图片，也就是说可以绘制背景和logo了； 
支持对定位角进行样式设置； 
支持对分割图案进行样式设置； 
支持对对齐图案进行样式设置； 
支持对时序图案进行样式设置； 
支持对暗块进行样式设置； 
支持对版本信息进行样式设置； 
解决小块之间出现白线问题。
## 3.0.1（2022-01-05）
3.0.1 gcanvas引用目录调整。
## 3.0.0（2022-01-04）
3.0.0 uQRCode 3.0 全新版本来袭。
## 2.0.4（2021-11-19）
2.0.4 新增绘制模式；新增绘制延时、canvas导入文件延时属性。
## 2.0.3（2021-10-18）
2.0.3 修复在部分安卓设备生成异常；移除延迟绘制；新增批量生成示例。
## 2.0.23（2021-08-09）
 
## 2.0.22（2021-08-09）
 
## 2.0.21（2021-07-28）
 
## 2.0.2（2021-07-28）
2.0.2 新增延迟绘制。
## 2.0.1（2021-07-26）
2.0.1 调整为uni_modules目录规范。
