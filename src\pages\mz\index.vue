<template>
  <view class="mz-index-page">
    <view class="header">
      <view class="head-nav">
        <view :class="navIndex == 0 ? 'activite' : ''" @click="checkIndex(0)">待缴费</view>
        <view :class="navIndex == 1 ? 'activite' : ''" @click="checkIndex(1)">缴费记录</view>
      </view>
    </view>
    <view class="page-content">
      <view class="content">
        <!-- 由于待缴费列表和已缴费列表样式可能不一直分2块展示 -->
        <!-- 待缴费列表 -->
        <view :class="navIndex != 0 ? 'hide' : ''">
          <view class="appoint_list" v-for="(item, index) in unpayList" :key="index">
            <view class="appoint_list-info" @click="goto(`/pages/mz/paydetail?bill_id=${item.bill_id}&pat_id=${item.patient_id}`)">
              <view class="appoint_list-top">
                <view class="appoint_list-cent-item">
                  <view class="patient_info">费用总额</view>
                  <view class="patient_infoText total_price">￥{{ item.total_price }}</view>
                </view>
              </view>
              <view class="appoint_list-cent">
                <view class="appoint_list-cent-item">
                  <view class="patient_info">就诊人</view>
                  <view class="patient_infoText"
                    >{{ new Array(item.patient_name.length).join('*') + item.patient_name.substr(-1) }}(ID:{{ item.jz_card }})</view
                  >
                </view>
                <view class="appoint_list-cent-item">
                  <view class="patient_info">就诊卡号</view>
                  <view class="patient_infoText">{{ item.jz_card }}</view>
                </view>
                <view class="appoint_list-cent-item">
                  <view class="patient_info">就诊院区</view>
                  <view class="patient_infoText">{{ item.hos_name }}</view>
                </view>
                <view class="appoint_list-cent-item">
                  <view class="patient_info">就诊医生</view>
                  <view class="patient_infoText">{{ item.doc_name }}</view>
                </view>

                <view class="appoint_list-cent-item">
                  <view class="patient_info">就诊时间</view>
                  <view class="patient_infoText">{{ item.bill_time }}</view>
                </view>
              </view>
            </view>
            <view class="appoint_list-but">
              <view class="dp-f-1 appoint_but et-color" @click="goto(`/pages/mz/paydetail?bill_id=${item.bill_id}&pat_id=${item.patient_id}&isGT=${item.isGT}`)">立即缴费</view>
            </view>
          </view>
          <!-- <view style="height: 100px"></view> -->
        </view>
        <!-- 已缴费列表 -->
        <view :class="navIndex != 1 ? 'hide' : ''">
          <view class="appoint_list" v-for="(item, index) in payedList" :key="index">
            <view class="appoint_list-info">
              <!-- <view class="appoint_list-top"> </view> -->
              <view class="appoint_list-cent">
                <view class="appoint_list-cent-item">
                  <view class="patient_info">缴费时间</view>
                  <view class="patient_infoText">{{ item.pay_time_str }}</view>
                </view>
                <view class="appoint_list-cent-item">
                  <view class="patient_info">费用总额</view>
                  <view class="patient_infoText font-weight: bold;">￥{{ item.total_price }}</view>
                </view>
                <view class="appoint_list-cent-item">
                  <view class="patient_info">就诊人</view>
                  <view class="patient_infoText"
                    >{{ new Array(item.patient_name.length).join('*') + item.patient_name.substr(-1)}}(ID:{{ item.jz_card }})</view
                  >
                </view>
                <view class="appoint_list-cent-item">
                  <view class="patient_info">就诊院区</view>
                  <view class="patient_infoText">{{ item.hos_name }}</view>
                </view>
                <view class="appoint_list-cent-item">
                  <view class="patient_info">就诊科室</view>
                  <view class="patient_infoText">{{ item.dep_name }}</view>
                </view>
                <view class="appoint_list-cent-item">
                  <view class="patient_info">就诊医生</view>
                  <view class="patient_infoText">{{ item.doc_name }}</view>
                </view>
                <view class="appoint_list-cent-item">
                  <view class="patient_info">就诊时间</view>
                  <view class="patient_infoText">{{ item.bill_time }}</view>
                </view>
              </view>
            </view>
            <view class="appoint_list-but">
              <view class="dp-f-1 appoint_but" @click="goto(`/pages/mz/cfinfo?id=${item.id}`)"
                >查看处方信息</view
              >
              <view
                class="dp-f-1 appoint_but et-color"
                @click="goto(`/pages/mz/detail?id=${item.id}`)"
                >查看指引信息</view
              >
            </view>
          </view>
          <!-- <view style="height: 100px"></view> -->
        </view>
      </view>
    </view>
    <view class="footer"></view>
  </view>
</template>

<script>
import { payedListMz, unPayListMz} from '@/api/ucenter.js'
import { getMypatientList} from '@/api/waiting.js'
import { reportCmPV } from '@/to/cloudMonitorHelper';
export default {
  setup() {},
  onLoad(query) {
    reportCmPV({ title: '门诊缴费',query });
    console.log('query.type', query.type)
    this.navIndex = !query.type ? 0 : query.type

    // 处理医保授权回调
    if (query.billId && query.patId) {
      console.log('检测到医保授权回调参数:', query)
      // 延迟跳转，确保页面加载完成
      setTimeout(() => {
        uni.navigateTo({
          url: `/pages/mz/paydetail?bill_id=${query.billId}&pat_id=${query.patId}&isGT=${query.isGT || 0}&from_yibao_callback=1`
        })
      }, 500)
    }
  },
  onShow(){
    this.getMyList()
  },
  data() {
    return {
      navIndex: 0,
      payedList: [],
      unpayList: [],
      patinetList: [],
    }
  },
  methods: {
    checkIndex(index) {
      this.navIndex = index
      // this.$refs.paging.reload()
      if (index == 1) {
        this.getPayedList()
        reportCmPV({ title: '缴费记录查询'});
      } else {
        this.getUnpayList()
      }
      // this.getList(index)
    },
    async getMyList() {
        const userInfo = uni.getStorageSync('userInfo')
        // 如果未授权，直接返回，否则会导致页面弹出多次提示窗，才能成功加载数据，影响用户体验
        if (!userInfo) {
          return
        }
        const { statusCode, data } = await getMypatientList(null, {
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '暂无数据',
            icon: 'none',
          })
          return
        }
        if (!data.data.length) {
          uni.showModal({
            title: '提示',
            content: '暂无就诊人，如还没有就诊卡请新建卡，如已经有就诊卡请绑定就诊卡',
            cancelText: '去绑定卡',
            confirmText: '去新建卡',
            success: function (res) {
              if (res.confirm) {
                //console.log('用户点击去新建卡')
                uni.navigateTo({
                  url: '/pages/ucenter/patient/add',
                })
              } else if (res.cancel) {
                //console.log('用户点击去绑定卡')
                uni.navigateTo({
                  url: '/pages/ucenter/patient/bind',
                })
              }
            },
          })
          return
        }
        this.patinetList = data.data
        // this.getUnpayList()
        this.checkIndex(this.navIndex)
      },
    async getUnpayList() {
      /* uni.setStorageSync('userInfo', {
        id: 910209,
        openid: 'o9U-Ft8KOx61VeU-MJMwNNUCbX60',
        userid: 910209,
      }) */
      const userInfo = uni.getStorageSync('userInfo')
      const { statusCode, data } = await unPayListMz(null, {
        userid: userInfo.user_id || userInfo.id,
        openid: userInfo.openid,
      })
      if (statusCode != 200) {
        uni.showToast({
          title: '请求网络失败',
          duration: 2000,
          icon: 'none',
        })
        return
      }
      if (data.code != 1) {
        uni.showToast({
          title: '暂无数据',
          duration: 2000,
          icon: 'none',
        })
        return
      }
      this.unpayList = data.data
    },
    async getPayedList() {
      // uni.setStorageSync('userInfo', {
      //   id: 1965396,
      //   openid: 'o9U-FtxjWp8yN3HHndU7X7wm32ds',
      //   userid: 1965396,
      // })
      const userInfo = uni.getStorageSync('userInfo')
      const { statusCode, data } = await payedListMz(null, {
        userid: userInfo.user_id,
        openid: userInfo.openid,
      })
      if (statusCode != 200) {
        uni.showToast({
          title: '请求网络失败',
          duration: 2000,
          icon: 'none',
        })
        return
      }
      if (data.code != 1) {
        uni.showToast({
          title: '暂无数据',
          duration: 2000,
          icon: 'none',
        })
        return
      }
      this.payedList = data.data
      // console.log(this.payedList)
    },
    goto(url) {
      uni.navigateTo({
        url,
      })
    },
  },
}
</script>
<style lang="scss" scoped>
uni-page-body{
  height: 100%;
}
.mz-index-page{
  display: flex;
  flex-direction: column;
  height: 100%;
}

.page-content{
  flex: 1;
  overflow: hidden;
}

.content {
  overflow: auto;
  height: 100%;
  padding:0 15px;
  box-sizing: border-box;
  padding-bottom: 100rpx;
}
.head-nav {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  z-index: 500;
  top: 0;
  width: 100%;
  background-color: #fafafa;
}
.head-nav > view {
  position: relative;
  display: block;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  padding: 7px 0;
  text-align: center;
  font-size: 15px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  border-right: 1px solid #cccccc;
  color: #888;
}
.head-nav > view:last-child {
  border-right: none;
}

.head-nav > view.activite {
  background-color: white;
  border-bottom: 2px solid rgb(238, 102, 138);
  color: rgb(238, 102, 138);
}

.hide {
  display: none;
}

.appoint_list {
  border-radius: 10px;
  margin-top: 10px;
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
}
.appoint_list-info {
  padding: 10px 15px;
}
.appoint_list-cent-item {
  display: flex;
  display: -webkit-flex;
  -webkit-align-items: center;
  justify-content: space-between;
  font-size: 0.9em;
  line-height: 2;
  .total_price{
    font-weight: bold;
    font-size: 1.2em;
  }
}
.appoint_list-top {
  border-bottom: 1px solid #e5e5e5;
  overflow: hidden;
  padding: 10px 0;
}
.dp-f-1 {
  flex: 1;
}
.appoint_list-but {
  line-height: 50px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  border-top: 1px solid #d5d5d6;
}
.appoint_but {
  text-align: center;
  border-left: 1px solid #d5d5d6;
  color: #999999;
  font-size: 16px;
}
.appoint_list-but .dp-f-1:first-child {
  border: none;
}
.et-color {
  color: rgb(238, 102, 138);
}
</style>
