<template>
  <view>
    <view class="container">
      <headerBAr title="预约挂号"></headerBAr>
      <view class="top-40">
        <!-- <view class="page-title">
          <view class="page-title-tab">选择院区</view>
          <view class="page-title-tab">全景导览 ></view>
        </view> -->
        <view class="hosList">
          <view
            class="hosItem"
            v-for="(item, index) in hosList"
            @click="depatrTab(item)"
            :key="index"
          >
            <view class="hosItem-title">{{ item.hos_name }}</view>
            <view class="hosItem-info">
              <view class="hosItem-address">{{ item.hospital_addr }}</view>
              <view class="hosItem-tolink">预约</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <uni-popup ref="popup" type="bottom" background-color="#fff" :safe-area="true">
      <view class="popupTitle">预诊分诊</view>
      <view class="popupScroll" style="padding: 30upx" @touchmove.stop.prevent="disabledScroll">
        <scroll-view scroll-y="true" class="scroll-Y">
          <view>为配合本市和医院新型冠状病毒感染肺炎防控工作，请先如实回答以下问题：</view>
          <uni-forms
            ref="baseForm"
            :modelValue="alignmentFormData"
            label-width="auto"
            label-position="top"
          >
            <uni-forms-item
              label="24小时内孩子体温情况"
              :label-width="200"
              :rules="[{ errorMessage: '域名项必填' }]"
              required
            >
              <uni-data-checkbox v-model="alignmentFormData.q1" :localdata="temperature" />
            </uni-forms-item>
            <uni-forms-item label="过去14天是否到访过港澳台和其他国家及地区？" required>
              <uni-data-checkbox v-model="alignmentFormData.q2" :localdata="bool" />
            </uni-forms-item>
            <uni-forms-item label="过去14天是否到访过境内中高风险地区？" required>
              <uni-data-checkbox v-model="alignmentFormData.q3" :localdata="bool" />
            </uni-forms-item>
            <uni-forms-item
              label="是否有干咳、乏力、嗅觉味觉减退、鼻塞、流涕、咽痛、结膜炎、肌痛和腹泻等新冠肺炎相关症状？"
              required
            >
              <uni-data-checkbox v-model="alignmentFormData.q4" :localdata="bool" />
            </uni-forms-item>
            <uni-forms-item label="您的健康码为：" required>
              <uni-data-checkbox v-model="alignmentFormData.q5" :localdata="hcode" />
            </uni-forms-item>
          </uni-forms>
        </scroll-view>
      </view>
      <view class="buttons">
        <button @click="closePop">取消</button>
        <button type="primary" @click="submit('popup')">确定</button>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import headerBAr from '@/pages/component/header_bar/header_bar.vue'
  import { reportCmPV } from '@/to/cloudMonitorHelper'

  export default {
    components: {
      headerBAr,
    },
    data() {
      return {
        navIndex: 0,
        hosList: [
          {
            hos_code: '871002',
            dep_id: 'A000129',
            hos_name: '昆明市儿童医院(前兴-新冠核酸检测)',
            hospital_addr: '昆明市西山区前兴路288号',
          },
        ],
        alignmentFormData: {
          q1: '',
          q2: '',
          q3: '',
          q4: '',
          q5: '',
        },
        temperature: [
          {
            text: '体温≥37.3°C',
            value: 1,
          },
          {
            text: '体温≤37.2℃',
            value: 0,
          },
        ],
        bool: [
          {
            text: '是',
            value: 1,
          },
          {
            text: '否',
            value: 0,
          },
        ],
        hcode: [
          {
            text: '绿码（低风险）',
            value: 0,
          },
          {
            text: '黄码（中风险）',
            value: 1,
          },
          {
            text: '红码（高风险）',
            value: 2,
          },
        ],
      }
    },
    methods: {
      depatrTab(panel, id) {
        if (panel.dep_id == 'A000129') this.$refs.popup.open('top')
      },
      submit() {
        for (var key in this.alignmentFormData) {
          if (this.alignmentFormData[key] === '') {
            uni.showModal({
              title: '提示',
              content: '每项条目必须选择',
            })
            return false
          }
          if (this.alignmentFormData[key] != 0) {
            uni.showModal({
              title: '提示',
              content: '请您携带孩子到医院进行现场预检分诊后就诊',
            })
            return false
          } else if (key == 'q5') {
            let that = this
            uni.showModal({
              title: '提示',
              content:
                '尊敬的患者家属：新冠核酸检测仅针对非发热的健康患者，地点在前兴院区住院部前小广场患者及陪护核酸检测处（开单和采集标本），免收挂号费和诊查费，发热患者请到医院进行现场预检分诊后就诊.',
              success: async function (res) {
                if (res.confirm) {
                  that.$refs.popup.close('top')
                  uni.setStorageSync('covid', 'covid')
                  uni.navigateTo({
                    url: '/pages/register/doctorList?hos_code=871002&dep_id=A000129',
                  })
                } else {
                  that.$refs.popup.close('top')
                }
              },
            })
            return false
          }
        }
      },
      closePop() {
        this.$refs.popup.close('top')
      },
      disabledScroll(){
				return
			},
    },
    onShow: function () {},
    created() {
      reportCmPV({ title: '核酸检测预约' })
    },
  }
</script>
<style scoped lang="scss">
  .popupTitle {
    color: #555;
    border-bottom: 1px solid #eee;
    padding: 20px 25px 8px;
    font-weight: 400;
    font-size: 18px;
    text-align: center;
  }
  .buttons {
    display: flex;
    display: -webkit-box;
    display: -webkit-flex;
    uni-button,
    button {
      flex: 1;
      margin-left: 0;
      margin-right: 0;
      border-radius: 0;
    }
    uni-button:after,
    button:after {
      border-radius: 0;
    }
    uni-button[type='primary'],
    button[type='primary'] {
      background-color: #ee668a;
    }
  }
  .scroll-Y {
    height: 870rpx;
  }
  .top-40 {
    top: 40px;
  }
  .page-title {
    padding: 10px 15px;
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    background-color: #edd1d8;
    justify-content: space-around;
  }
  .page-title-tab {
    flex: 1;
    text-align: center;
    font-size: 1em;
  }
  .hosList {
    padding: 0 15px;
    padding-top: 1px;
  }
  .hosItem {
    border-radius: 10px;
    border: 1px solid #e5e5e5;
    margin-top: 10px;
    position: relative;
    padding: 10px 15px;
  }
  .hosItem-title {
    margin-right: 0.2em;
    line-height: 2.5em;
    font-size: 1.125em;
    color: rgb(238, 102, 138);
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 10px;
  }
  .hosItem-tolink {
    color: rgb(238, 102, 138);
    border: 1px solid rgb(238, 102, 138);
    display: inline-block;
    padding: 0 1.32em;
    line-height: 2.3;
    font-size: 13px;
    border-radius: 5px;
  }
  .hosItem-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .hosItem-address {
    line-height: 2.5em;
    font-size: 1.125em;
    color: #666;
  }
</style>
