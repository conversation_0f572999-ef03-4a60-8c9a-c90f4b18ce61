{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^uni-((?!(icons)).*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    }
  },
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "昆明市儿童医院",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/guide/index",
      "style": {
        "navigationBarTitleText": "使用指南"
      }
    },
    {
      "path": "pages/news/index",
      "style": {
        "navigationBarTitleText": "资讯消息"
      }
    },
    {
      "path": "pages/healthedu/index",
      "style": {
        "navigationBarTitleText": "健康教育"
      }
    },
    {
      "path": "pages/mz/index",
      "style": {
        "navigationBarTitleText": "门诊缴费"
      }
    },
    {
      "path": "pages/mz/paydetail",
      "style": {
        "navigationBarTitleText": "门诊缴费详情"
      }
    },
    {
      "path": "pages/mz/waitList",
      "style": {
        "navigationBarTitleText": "排队查询",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/mz/detail",
      "style": {
        "navigationBarTitleText": "门诊订单详情"
      }
    },
    {
      "path": "pages/mz/cfinfo",
      "style": {
        "navigationBarTitleText": "门诊处方详情"
      }
    },
    {
      "path": "pages/covid/index",
      "style": {
        "navigationBarTitleText": "核酸检测"
      }
    },
    {
      "path": "pages/zy/index",
      "style": {
        "navigationBarTitleText": "住院服务"
      }
    },
    {
      "path": "pages/zy/dailyfee",
      "style": {
        "navigationBarTitleText": "住院费用清单"
      }
    },
    {
      "path": "pages/zy/full",
      "style": {
        "navigationBarTitleText": "住院充值"
      }
    },
    {
      "path": "pages/zy/chargeDetail",
      "style": {
        "navigationBarTitleText": "订单详情"
      }
    },
    {
      "path": "pages/ucenter/index",
      "style": {
        "navigationBarTitleText": "个人中心",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/ucenter/appoint/appoint",
      "style": {
        "navigationBarTitleText": "预约记录"
      }
    },
    {
      "path": "pages/ucenter/appoint/detail",
      "style": {
        "navigationBarTitleText": "预约详情"
      }
    },
    {
      "path": "pages/ucenter/report/report",
      "style": {
        "navigationBarTitleText": "我的报告"
      }
    },
    {
      "path": "pages/ucenter/report/detail",
      "style": {
        "navigationBarTitleText": "报告详情"
      }
    },
    {
      "path": "pages/ucenter/patient/patient",
      "style": {
        "navigationBarTitleText": "我的就诊人"
      }
    },
    {
      "path": "pages/ucenter/patient/detail",
      "style": {
        "navigationBarTitleText": "就诊人详情"
      }
    },
    {
      "path": "pages/ucenter/patient/healthcard",
      "style": {
        "navigationBarTitleText": "申领电子健康卡"
      }
    },
    {
      "path": "pages/ucenter/patient/editinfo",
      "style": {
        "navigationBarTitleText": "修改信息",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/ucenter/patient/add",
      "style": {
        "navigationBarTitleText": "家长/儿童建档",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/ucenter/patient/bind",
      "style": {
        "navigationBarTitleText": "绑定就诊人"
      }
    },
    {
      "path": "pages/ucenter/covd_promise",
      "style": {
        "navigationBarTitleText": "疫情承诺书",
        "navigationStyle": "custom",
        "app-plus": {
          "animationType": "slide-in-bottom",
          "animationDuration": 300
        }
      }
    },
    {
      "path": "pages/ucenter/covd_inform",
      "style": {
        "navigationBarTitleText": "疫情告知书",
        "navigationStyle": "custom",
        "app-plus": {
          "animationType": "slide-in-bottom",
          "animationDuration": 300
        }
      }
    },
    {
      "path": "pages/component/mine-list/mine-list",
      "style": {
        "navigationBarTitleText": "mine-list我的页面",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/register/appoint",
      "style": {
        "navigationBarTitleText": "预约挂号",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/register/departlist",
      "style": {
        "navigationBarTitleText": "预约挂号",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/register/doctordetail",
      "style": {
        "navigationBarTitleText": "医生详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/register/doctorDetailNew",
      "style": {
        "navigationBarTitleText": "医生详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/register/appointDetail",
      "style": {
        "navigationBarTitleText": "门诊信息",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/register/doctorList",
      "style": {
        "navigationBarTitleText": "预约挂号",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/notice/covd_promise",
      "style": {
        "navigationBarTitleText": "疫情承诺书",
        "navigationStyle": "custom",
        "app-plus": {
          "animationType": "slide-in-bottom",
          "animationDuration": 300
        }
      }
    },
    {
      "path": "pages/notice/covd_inform",
      "style": {
        "navigationBarTitleText": "疫情告知书",
        "navigationStyle": "custom",
        "app-plus": {
          "animationType": "slide-in-bottom",
          "animationDuration": 300
        }
      }
    },
    {
      "path": "pages/signature/signature",
      "style": {
        "navigationBarTitleText": "签名",
        "navigationStyle": "custom",
        "app-plus": {
          "titleNView": false
        }
      }
    },
    {
      "path": "pages/mz/index",
      "style": {
        "navigationBarTitleText": "门诊缴费",
        "navigationStyle": "custom",
        "app-plus": {
          "titleNView": false
        }
      }
    },{
      "path": "pages/mz/cfinfo",
      "style": {
        "navigationBarTitleText": "门诊信息",
        "navigationStyle": "custom",
        "app-plus": {
          "titleNView": false
        }
      }
    },{
      "path": "pages/mz/detail",
      "style": {
        "navigationBarTitleText": "门诊缴费详情",
        "navigationStyle": "custom",
        "app-plus": {
          "titleNView": false
        }
      }
    },{
      "path": "pages/mz/medins",
      "style": {
        "navigationBarTitleText": "医保缴费",
        "navigationStyle": "custom",
        "app-plus": {
          "titleNView": false
        }
      }
    },
    {
      "path": "pages/error/404",
      "style": {
        "navigationBarTitleText": "Not Found"
      }
    },
    {
    	"path" : "pages/hos-intro/hos-intro",
    	"style" : 
    	{
    		"navigationBarTitleText" : "医院简介"
    	}
    },
    {
    	"path" : "pages/panorama/panorama",
    	"style" : 
    	{
    		"navigationBarTitleText" : "昆明市儿童医院全景导览"
    	}
    },
    {
    	"path" : "pages/appoint-rule/appoint-rule",
    	"style" : 
    	{
    		"navigationBarTitleText" : "预约规则"
    	}
    },
    {
    	"path" : "pages/medical-notice/medical-notice",
    	"style" : 
    	{
    		"navigationBarTitleText" : "就诊须知"
    	}
    },
    {
    	"path" : "pages/medical-guide/medical-guide",
    	"style" : 
    	{
    		"navigationBarTitleText" : "就诊指南"
    	}
    },
    {
    	"path" : "pages/special/index",
    	"style" : 
    	{
    		"navigationBarTitleText" : "特需门诊",
        "navigationStyle": "custom"
    	}
    },
    {
    	"path" : "pages/special/multidisciplinary",
    	"style" : 
    	{
    		"navigationBarTitleText" : "多学科预约挂号",
        "navigationStyle": "custom"
    	}
    },
    {
    	"path" : "pages/credit/index",
    	"style" : 
    	{
    		"navigationBarTitleText" : "信用就医",
        "navigationStyle": "custom"
    	}
    },
    {
    	"path" : "pages/credit/create",
    	"style" : 
    	{
    		"navigationBarTitleText" : "信用就医",
        "navigationStyle": "custom"
    	}
    },
    {
    	"path" : "pages/check-appoint/index",
    	"style" : 
    	{
    		"navigationBarTitleText" : "检查预约",
        "navigationStyle": "custom"
    	}
    }
  ],
  "subPackages": [],
  "globalStyle": {
    "pageOrientation": "portrait",
    "navigationBarTitleText": "Hello uniapp",
    "navigationBarTextStyle": "white",
    "navigationBarBackgroundColor": "#007AFF",
    "backgroundColor": "#F8F8F8",
    "backgroundColorTop": "#F4F5F6",
    "backgroundColorBottom": "#F4F5F6",
    "mp-360": {
      "navigationStyle": "custom"
    },
    "h5": {
      "maxWidth": 1190,
      "navigationBarTextStyle": "black",
      "navigationBarBackgroundColor": "#F1F1F1"
    }
  },
  "tabBar": {
    "color": "#999999",
    "selectedColor": "rgb(238,102,138)",
    "backgroundColor": "#fff",
    //#ifdef H5
    "iconfontSrc":"/static/iconfont/iconfont.ttf",
    //#endif
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页"
        //#ifdef MP-ALIPAY
        ,"iconPath": "static/home/<USER>",
        "selectedIconPath": "static/home/<USER>"
        //#endif
        //#ifdef H5
        ,"iconfont":{
          "text": "\ue674",
          "selectedText": "\ue674",
          "fontSize": "24px",
          "color": "#999999",
          "selectedColor": "rgb(238,102,138)"
        }
        //#endif
      },
      {
        "pagePath": "pages/ucenter/index",
        "text": "我的"
        //#ifdef MP-ALIPAY
        ,"iconPath": "static/home/<USER>",
        "selectedIconPath": "static/home/<USER>"
        //#endif
        //#ifdef H5
        ,"iconfont": {
          "text": "\ue6b8",
          "selectedText": "\ue6b8",
          "fontSize": "24px",
          "color": "#999999",
          "selectedColor": "rgb(238,102,138)"
        }
        //#endif
      }
    ]
  }
}
