import { get, post } from '@/utils/request'

//获取JS-SDK签名
export const reqGetJsSign = (data, params) => get('/wechat/getJsSign', data, { showLoading: true })


//获取用户信息
export const reqGetUserInfoWeChat = (data, params) => get('/wechat/getInfo', data, { showLoading: true })
// 获取科室列表
export const reqGetDepList = (data, params) => get('/refactor_reg/get_depart_list', data, { showLoading: true })// 获取科室列表
// 获取医生列表
export const reqGetDocList = (data, params) => get('/refactor_reg/doctorList', data, { showLoading: true })
// 获取医生详情
export const reqGetDocDetail = (data, params) => get('/refactor_reg/doctorDetail', data, { showLoading: true })

// 获取医生详情和天数排班
export const reqGetDocDetailAndDay = (data, params) => get('/refactor_reg/doctor_detail_and_schedule', data, { showLoading: true })
// 获取医生当天详细排班
export const reqGetDocTimeSch = (data, params) => get('/refactor_reg/get_time_schedule', data, { showLoading: true })

// 获取特需门诊和多学科门诊
export const reqSpecialDepList = (data, params) => get('/refactor_reg/special_depart_list', data, { showLoading: true })

// 获取支付参数
export const reqMzPayParam = (data, params) => get(`/mz/mz_pay?userid=${params.userid}&openid=${params.openid}&order_id=${params.order_id}`,data,{showLoading:true})
// 更新就诊人微信电子健康卡信息
export const reqUpdatePatTx = (data, params) => post(`/patient/updateTx`,data,{showLoading:true})// 更新就诊人微信电子健康卡信息
// 获取电子健康卡二维码
export const reqGetHealthCode = (data, params) => get(`/txhealthcard/getQrCodeByWechatCode`,data,{showLoading:true})
// 获取进入卡包链接
export const reqCardBagUrl = (data, params) => get(`/txhealthcard/getCardOrderIdByWechatCodeUrl`,data,{showLoading:true})
// 取消绑定健康卡
export const reqUnBindHealthCard = (data, params) => post(`/patient/cleanTxHealthCard`,data,{showLoading:true})
// 更改信用就医状态
export const reqChangeCredit = (data, params) => post(`/patient/changeCredit`,data,{showLoading:true})
//提交挂号
export const reqSubmitAppoint = (data, params) => post(`/refactor_reg/submit`, data, { showLoading: true })
//取消预约
export const reqCancelAppoint = (data, params) => post(`/refactor_reg/cancel`, data, { showLoading: true })
