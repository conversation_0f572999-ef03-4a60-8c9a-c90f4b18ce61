<template>
  <view class="container">
    <view class="body">
      <view class="content" style="height: 100%; padding-bottom: 200px" v-if="mode === 'detail'">
        <!-- 由于待缴费列表和已缴费列表样式可能不一直分2块展示 -->
        <!-- 待缴费列表 -->
        <view class="appoint_list" v-show="unpayinfo.total_price">
          <view class="appoint_list-info">
            <view class="appoint_list-top">
              <view class="appoint_list-cent-item">
                <view class="patient_info">费用总额</view>
                <view class="patient_infoText total_price">￥{{ unpayinfo.total_price }}</view>
              </view>
            </view>
            <view class="appoint_list-cent">
              <view class="appoint_list-cent-item">
                <view class="patient_info">就诊人</view>
                <view class="patient_infoText"
                  >{{ new Array(unpayinfo.patient_name.length).join('*') + unpayinfo.patient_name.substr(-1)}}(ID:{{ unpayinfo.jz_card }})</view
                >
              </view>
              <view class="appoint_list-cent-item">
                <view class="patient_info">就诊卡号</view>
                <view class="patient_infoText">{{ unpayinfo.jz_card }}</view>
              </view>
              <view class="appoint_list-cent-item">
                <view class="patient_info">就诊院区</view>
                <view class="patient_infoText">{{ unpayinfo.hos_name }}</view>
              </view>
              <view class="appoint_list-cent-item">
                <view class="patient_info">就诊医生</view>
                <view class="patient_infoText">{{ unpayinfo.doc_name }}</view>
              </view>

              <view class="appoint_list-cent-item">
                <view class="patient_info">就诊时间</view>
                <view class="patient_infoText">{{ unpayinfo.bill_time }}</view>
              </view>
            </view>
          </view>
          <view class="appoint_list-but">
            <view class="dp-f-1 appoint_but et-color" @click="topay">现金支付</view>
            <view class="dp-f-1 appoint_but et-color" @click="clickYiBaoPay">医保缴费</view>
          </view>
        </view>
        <view style="height: 100px"></view>
      </view>
      <view class="pay-content" v-else>
        <div class="page-index">
          <div class="hd">
            <div class="info">
              <div class="info-hd">付款给</div>
              <div class="info-bd">昆明市儿童医院医院</div>
            </div>
            <div class="info-icon">
              <img class="info-icon-img" src="../../assets/logo.png" alt="">
            </div>
            <div class="bg"></div>
          </div>
          <div class="bd">
            <div class="box">
              <div class="box-hd">
                <div class="box-hd-label">费用总额</div>
                <div class="box-hd-value">{{mi_order.total_fee}}元</div>
              </div>
              <div class="box-bd">
                <div class="box-item">
                  <div class="box-bd-label">医保基金支付</div>
                  <div class="box-bd-value">{{mi_order.fund_pay}}元</div>
                </div>
                <div class="box-item">
                  <div class="box-bd-label">个人帐户支付</div>
                  <div class="box-bd-value">{{mi_order.psn_acct_pay}}元</div>
                </div>
                <div class="box-item">
                  <div class="box-bd-label">其他抵扣金额</div>
                  <div class="box-bd-value">{{mi_order.cash_reduced_fee}}元</div>
                </div>
              </div>
              <div class="box-ft">
                <div class="box-ft-label">现金支付</div>
                <div class="box-ft-value">{{mi_order.own_pay_amt}}元</div>
              </div>
            </div>
            <div class="bd-append">
              <i class="bd-append-icon"></i>
              <div class="bd-append-text">医保移动支付</div>
            </div>
          </div>
          <div class="ft">
            <div class="pay">
              <div class="pay-label">您还需支付：</div>
              <div class="pay-value">¥{{mi_order.own_pay_amt}}</div>
            </div>
            <div class="btn" @click="ali_med_pay_order">去支付</div>
          </div>
        </div>
      </view>
    </view>
    <view class="footer"></view>
    <view id="insertMolian" style="display: none"></view>
  </view>
</template>

<script>
import {createMzOrder, createMzMiOrderAli, getMzPay, getMzPayQuery, unPayListMz} from '@/api/ucenter.js'
import { aliMedHisOrder, aliMedInsAuth, aliMedPayOrder, getMyPatientDetail, refreshToken } from '@/api/waiting';
import { reqMzPayParam } from '@/api/wechat';
const monitor = require('@/to/alipayLogger.js')

  export default {
    setup() {},
    onLoad(query) {
      console.log(query)
      this.pat_id = query.pat_id
      this.bill_id = query.bill_id
      this.isGT = query.isGT
      this.query = query
      if (this.query.order_id && this.query.resultCode && this.query.resultCode !== 'CANCEL_AUTH') {
        console.log('当前订单医保已授权跳转医保下单逻辑')
        this.mode = 'pay'
        this.ali_med_hos_order(this.query.order_id)
      } else {
        this.getUnpay()
      }
    },
    onShow(options) {

    },
    data() {
      return {
        unpayinfo: {
          patient_name: '',
        },
        num: 0,
        order: {},
        order_id: 0,
        patient: {},
        query: {},
        mode: 'detail',
        mi_order: {},
        mz_order: {},
        isGT: 0
      }
    },
    methods: {
      myGetSystemInfo() {
        return new Promise((resolve, reject) => {
          my.getSystemInfo({
            success: resolve,
            fail: reject
          });
        });
      },
      myGetSetting() {
        return new Promise((resolve, reject) => {
          my.getSetting({
            success: resolve,
            fail: reject
          });
        });
      },
      myOpenSetting() {
        return new Promise((resolve, reject) => {
          my.openSetting({
            success: resolve,
            fail: reject
          });
        });
      },
      myAlert(content) {
        return new Promise((resolve, reject) => {
          my.alert({
            content,
            success: resolve,
            fail: reject
          });
        });
      },
      // 获取用户是否开启系统定位及授权支付宝使用定位
      async isLocationEnabled() {
        const systemInfo = await this.myGetSystemInfo();
        return !!(systemInfo.locationEnabled && systemInfo.locationAuthorized);
      },
      // 若用户未开启系统定位或未授权支付宝使用定位，则跳转至系统设置页
      async showAuthGuideIfNeeded() {
        if (!(await this.isLocationEnabled())) {
          my.showAuthGuide({
            authType: "LBS"
          });
          return false;
        }
        return true;
      },

      // 获取用户是否授权过当前小程序使用定位
      async isLocationMPAuthorized () {
        const settingInfo = await this.myGetSetting();
        return settingInfo.authSetting.location === undefined || settingInfo.authSetting.location;
      },

      // 若用户未授权当前小程序使用定位，则引导用户跳转至小程序设置页开启定位权限
      async requestLocationPermission() {
        await myAlert("如果用户之前拒绝授权当前小程序获取地理位置权限，将会弹出此弹窗，请根据需要替换文案。");
        const openSettingInfo = await this.myOpenSetting();
        return openSettingInfo.authSetting.location;
      },
      async authGuideLocation() {
        try {
          if (!(await this.showAuthGuideIfNeeded())) {
            return false;
          }
          if (await this.isLocationMPAuthorized()) {
            return true;
          }
          if (await this.requestLocationPermission()) {
            return true;
          }
          return false;
        } catch (error) {
          console.error(error);
          return false;
        }
      },
      ali_med_hos_order(order_id) {
        const userInfo = uni.getStorageSync('userInfo')
        console.log("开始进行支付宝医保支付医保下单流程")
        this.authGuideLocation().then(res => {
          if (res === true) {
            my.getLocation({
              type: 0, // 获取经纬度和省市区县数据
              success: (res) => {
                console.log(res)
                aliMedHisOrder({
                  accessToken: uni.getStorageSync('mfrstreCode')||uni.getStorageSync('userInfo').access_token,
                  user_id: userInfo.user_id,
                  openid: userInfo.openid,
                  order_id: order_id,
                  lon: res.longitude,
                  lat: res.latitude
                }).then((res) => {
                  this.mi_order = res.data.data.miOrder
                  this.mz_order = res.data.data.mzPayOrder
                }).catch((err) => {
                  uni.showModal({
                    title: '医保划价失败',
                    content:
                        '当前处方医保划价失败，请使用现金支付或至收费窗口支付',
                    success: async function (res) {
                        uni.navigateTo({
                          url: `/pages/mz/index`
                        })
                    },
                  })
                  console.log(err)
                })
              },
              fail: (error) => {
                console.error('定位失败: ', JSON.stringify(error));
              },
            });
          }
        })
      },
      ali_med_pay_order() {
        const userInfo = uni.getStorageSync('userInfo')
        aliMedPayOrder({
          accessToken: uni.getStorageSync('mfrstreCode')||uni.getStorageSync('userInfo').access_token,
          user_id: userInfo.user_id,
          openid: userInfo.openid,
          order_id: this.query.order_id
        }).then((res) => {
          console.log(res)
          my.tradePay({
            orderStr: res.data.data,
            success: (result) => {
              uni.navigateTo({url: `/pages/mz/index?type=1`})
            },
            fail: (error) => {
              my.alert({content: '医保支付失败或已取消'})
              console.log(JSON.stringify(error))
              uni.navigateTo({url: `/pages/mz/index`})
            }
          });
        }).catch((err) => {
          console.log(err)
        })
      },
      async getUnpay() {
        const userInfo = uni.getStorageSync('userInfo')
        const { statusCode, data } = await unPayListMz(null, {
          userid: userInfo.user_id || userInfo.id,
          openid: userInfo.openid,
        })
        const patientData = await getMyPatientDetail(null, {
          patientid: this.pat_id,
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        this.patient = patientData.data.data
        
        if (statusCode != 200) {
          uni.showToast({
            title: '请求网络失败',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        if (data.code != 1) {
          uni.showToast({
            title: '暂无数据',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        console.log(data.data)
        this.unpayinfo = data.data.filter(item => item.bill_id === this.bill_id)[0]
      },
      goto(url) {
        uni.navigateTo({
          url,
        })
      },
      clickYiBaoPay() {
        // #ifdef MP-ALIPAY
        this.ali_med_pay()
        // #endif
        // #ifdef H5
        // 这里是H5的支付逻辑
        this.wxYiBaoPay()
        // #endif
      },
      wxYiBaoPay() {
        uni.showModal({
          title: '提示',
          content: '请确认是否是为本人进行医保缴费',
          showCancel: true,
          cancelText: '为儿童缴费',
          cancelColor: '#000000',
          confirmText: '为本人缴费',
          success: (res) => {
            let redirectUrl = ''
            let yiBaoPayUrl = ''

            if(window.location.href.indexOf("tertong")>-1) {
              //跳转医保授权页面测试环境
              redirectUrl = 'https://tertong.ynhdkc.com/ui/pages/mz/index'
              yiBaoPayUrl = 'https://card.wecity.qq.com/oauth/code'
            } else {
              //跳转医保授权页面正式环境
              redirectUrl = 'https://ertong.ynhdkc.com/ui/pages/mz/index'
              yiBaoPayUrl = 'https://card.wecity.qq.com/oauth/code'
            }
            redirectUrl = encodeURIComponent(redirectUrl + "?billId=" + this.unpayinfo.bill_id + "&patId=" + this.unpayinfo.patient_id + "&isGT=" + this.unpayinfo.isGT)
            if (res.confirm) {
              console.log(redirectUrl)
              // window.location.href = "https://mitest.wecity.qq.com/openAuth/info?authType=2&isDepart=2&appid=wx3df855bf9977df7b&cityCode=530100&channel=AAGQ3Cq_5-d68zT1q6wsAkas&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK%2FF2LjZPYJ81nK2eYQqxtAArJrg4G9eEkxWJ1WYkXp&orgCodg=H53011200038&bizType=04107&orgAppId=1GA8HL0GN04O3F60C80A00006F92EC1B&redirectUrl=" + redirectUrl
              window.location.href = yiBaoPayUrl + "?authType=2&isDepart=2&appid=wx3df855bf9977df7b&cityCode=530100&channel=AAGQ3Cq_5-d68zT1q6wsAkas&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK%2FF2LjZPYJ81nK2eYQqxtAArJrg4G9eEkxWJ1WYkXp&orgCodg=H53011200038&bizType=04107&orgAppId=1GA8HL0GN04O3F60C80A00006F92EC1B&redirectUrl=" + redirectUrl
            } else if (res.cancel) {
              // 亲情付测试链接不一致
              if(window.location.href.indexOf("tertong")>-1) {
                yiBaoPayUrl = 'https://exp.wecity.qq.com/oauth/code'
              }
              // window.location.href = "https://mitest.wecity.qq.com/openAuth/info?authType=2&isDepart=2&appid=wx3df855bf9977df7b&cityCode=530100&channel=AAGQ3Cq_5-d68zT1q6wsAkas&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK%2FF2LjZPYJ81nK2eYQqxtAArJrg4G9eEkxWJ1WYkXp&orgCodg=H53011200038&bizType=04107&orgAppId=1GA8HL0GN04O3F60C80A00006F92EC1B&redirectUrl=" + redirectUrl
              // 还需要一个familyId
              window.location.href = yiBaoPayUrl + "?authType=2&isDepart=2&appid=wx3df855bf9977df7b&cityCode=530100&channel=AAGQ3Cq_5-d68zT1q6wsAkas&familyId=XXX&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK%2FF2LjZPYJ81nK2eYQqxtAArJrg4G9eEkxWJ1WYkXp&orgCodg=H53011200038&bizType=04107&orgAppId=1GA8HL0GN04O3F60C80A00006F92EC1B&redirectUrl=" + redirectUrl
            }
          }
        });
      },
      ali_med_pay() {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo.is_certified !== 'T') {
          my.alert({content:'当前用户未通过实名认证或与当前待缴费处方非本人处方无法使用医保移动支付'})
          return
        }
        createMzMiOrderAli({
          pat_id: this.pat_id,
          bill_id: this.bill_id,
          userid: userInfo.user_id,
          openid: userInfo.openid,
          isGT: this.isGT,
        }).then((res) => {
          this.order = res.data.data
          console.log(this.order)
          aliMedInsAuth({
            accessToken: uni.getStorageSync('mfrstreCode')||uni.getStorageSync('userInfo').access_token,
            user_id: userInfo.user_id,
            openid: userInfo.openid,
            order_id: this.order.id
          }).then((res) => {
            if (res.data.data.pay_auth_no) {
              uni.navigateTo({
                url: '/pages/mz/paydetail?order_id=' + this.order.id + '&pat_id=' + this.pat_id + '&bill_id=' + this.bill_id + '&resultCode=success',
              })
              // this.ali_med_hos_order()
            } else {
              my.ap.navigateToAlipayPage({
                path: encodeURI(res.data.data.auth_url)
              });
            }
          }).catch((err) => {
            console.log(err)
          })
        })
      },
      topay() {
        const userInfo = uni.getStorageSync('userInfo')
        console.log(userInfo)

        createMzOrder({
          pat_id: this.pat_id,
          bill_id: this.bill_id,
          userid: userInfo.user_id,
          openid: userInfo.openid,
        }).then((res) => {
          console.log(res)
          let resData = res.data.data
          let query = {
            userid: userInfo.user_id,
            openid: userInfo.openid,
            order_id: resData.id,
          }
          // #ifdef MP-ALIPAY
          this.aliPay(query)
          // #endif
          // #ifdef H5
          // 这里是H5的支付逻辑
          this.h5Pay(query)
          // #endif
          
        }).catch(() => {
          uni.showToast({
            title: '请求网络失败',
            duration: 2000,
            icon: 'none',
          })
          return
        })
        //
      },

      // H5支付
      h5Pay(query) {
        console.log(query)
       reqMzPayParam(null, query).then((res) => {
        console.log(res)
            if (res.data.code == 1) {
              let ret = res.data.data
              console.log(ret)
              this.molianPay(ret)
              // $('#goMolian').submit();
            } else {
              uni.showToast({
                title: res.msg,
                duration: 2000,
                icon: 'none',
              })
            }
          })
      },

      // мол联支付使用创建form表单的方式
      molianPay(params) {
        let form = document.createElement('form');
        form.setAttribute('method','POST');
        form.setAttribute('id','goMolian');
      
        form.setAttribute('action','http://ertongpay.etyy.cn/MuWapPaymentsV3.aspx');
        
        let MerBillNo = document.createElement('input');
        MerBillNo.setAttribute('name','MerBillNo');
        MerBillNo.setAttribute('value',params.OrderNo);
        form.appendChild(MerBillNo);

        let UserId = document.createElement('input');
        UserId.setAttribute('name','UserId');
        UserId.setAttribute('value',this.pat_id);
        form.appendChild(UserId);

        let ValidTime = document.createElement('input');
        ValidTime.setAttribute('name','ValidTime');
        ValidTime.setAttribute('value','');
        form.appendChild(ValidTime);

        let MerNo = document.createElement('input');
        MerNo.setAttribute('name','MerNo');
        MerNo.setAttribute('value',params.MerNo);
        form.appendChild(MerNo);

        let ProductId = document.createElement('input');
        ProductId.setAttribute('name','ProductId');
        ProductId.setAttribute('value',"-");
        form.appendChild(ProductId);

        let ProductName = document.createElement('input');
        ProductName.setAttribute('name','ProductName');
        ProductName.setAttribute('value',"门诊缴费");
        form.appendChild(ProductName);

        let OrderDate = document.createElement('input');
        OrderDate.setAttribute('name','OrderDate');
        OrderDate.setAttribute('value',params.OrderDate);
        form.appendChild(OrderDate);

        let OrderAmount = document.createElement('input');
        OrderAmount.setAttribute('name','OrderAmount');
        OrderAmount.setAttribute('value',params.OrderAmount);
        form.appendChild(OrderAmount);

        let OrdSourceIp = document.createElement('input');
        OrdSourceIp.setAttribute('name','OrdSourceIp');
        OrdSourceIp.setAttribute('value',"");
        form.appendChild(OrdSourceIp);

        let CurrencyType = document.createElement('input');
        CurrencyType.setAttribute('name','CurrencyType');
        CurrencyType.setAttribute('value',"RMB");
        form.appendChild(CurrencyType);

        let PayAppCode = document.createElement('input');
        PayAppCode.setAttribute('name','PayAppCode');
        PayAppCode.setAttribute('value',"04");
        form.appendChild(PayAppCode);

        let PaySourceCode = document.createElement('input');
        PaySourceCode.setAttribute('name','PaySourceCode');
        PaySourceCode.setAttribute('value',"04");
        form.appendChild(PaySourceCode);

        let PayAccount = document.createElement('input');
        PayAccount.setAttribute('name','PayAccount');
        PayAccount.setAttribute('value',params.MerNo);
        form.appendChild(PayAccount);

        let SignMD5 = document.createElement('input');
        SignMD5.setAttribute('name','SignMD5');
        SignMD5.setAttribute('value',params.SignMD5);
        form.appendChild(SignMD5);

        // let url = window.location.protocol + "//" + window.location.host + "/html/mz/paydetail.html?type=1&id="+params.order_id;
        let url = window.location.protocol + "//" + window.location.host + "/ui/pages/mz/detail?id="+params.order_id;
        let Merchanturl = document.createElement('input');
        Merchanturl.setAttribute('name','Merchanturl');
        Merchanturl.setAttribute('value',url);
        form.appendChild(Merchanturl);

        let S2SMerchanturl = document.createElement('input');
        S2SMerchanturl.setAttribute('name','S2SMerchanturl');
        S2SMerchanturl.setAttribute('value',params.S2SMerchanturl);
        form.appendChild(S2SMerchanturl);

        let openidInput = document.createElement('input');
        openidInput.setAttribute('name','openid');
        openidInput.setAttribute('value',params.openid);
        form.appendChild(openidInput);

        console.log('form', 'submit')
        let elPay = document.getElementById('insertMolian');
        elPay.append(form);
        let formEl = document.getElementById('goMolian');
        console.log(formEl)
        formEl.submit();
      },

      // 支付宝支付
      aliPay(query) {
        getMzPay(null, query).then((res) => {
            res = res.data
            if (res.code == 1) {
              my.tradePay({
                tradeNO: res.data.TradeNO,
                success: () => {
                  uni.showLoading({
                    title: '加载中',
                  })
                  setTimeout(() => {
                    that.getQuery(query)
                  }, 2000);
                },
                fail: (res) => {
                  my.alert({
                    content: JSON.stringify(res),
                  })
                },
              })
              console.log(res.data.TradeNO)
            } else {
              uni.showToast({
                title: res.msg,
                duration: 2000,
                icon: 'none',
              })
            }
          })
      },

      // 获取支付宝订单状态
      getQuery(query) {
        let that = this
        this.num ++
        getMzPayQuery(null, query).then((tres) => {
          tres = tres.data
          if (tres.code == 1) {
            uni.hideLoading()
            monitor.api({ api: '门诊缴费', success: true, c1: 'taSR_YL', time: 200 })
            uni.navigateTo({
              url: '/pages/mz/detail?id=' + that.unpayinfo.id,
            })
          }else{
            if(that.num >= 5){
                uni.hideLoading()
                uni.showToast({
                    title: '请稍后再试',
                    duration: 2000,
                    icon: 'none',
                })
            }else{
                that.getQuery(query)
            }
          }
        })
      },
    }
  }
</script>
<style lang="scss" scoped>
@import 'medins.css';
  .content {
    overflow: auto;
    height: 100%;
    padding: 15px;
    margin-bottom: 200rpx;
  }
  .head-nav {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    z-index: 500;
    top: 0;
    width: 100%;
    background-color: #fafafa;
  }
  .head-nav > view {
    position: relative;
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    padding: 7px 0;
    text-align: center;
    font-size: 15px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    border-right: 1px solid #cccccc;
    color: #888;
  }
  .head-nav > view:last-child {
    border-right: none;
  }

  .head-nav > view.activite {
    background-color: white;
    border-bottom: 2px solid rgb(238, 102, 138);
    color: rgb(238, 102, 138);
  }

  .hide {
    display: none;
  }

  .appoint_list {
    border-radius: 10px;
    margin-top: 10px;
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
  }
  .appoint_list-info {
    padding: 10px 15px;
  }
  .appoint_list-cent-item {
    display: flex;
    display: -webkit-flex;
    -webkit-align-items: center;
    justify-content: space-between;
    font-size: 0.9em;
    line-height: 2;
    .total_price {
      font-weight: bold;
      font-size: 1.2em;
    }
  }
  .appoint_list-top {
    border-bottom: 1px solid #e5e5e5;
    overflow: hidden;
    padding: 10px 0;
  }
  .dp-f-1 {
    flex: 1;
  }
  .appoint_list-but {
    line-height: 50px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    border-top: 1px solid #d5d5d6;
  }
  .appoint_but {
    text-align: center;
    border-left: 1px solid #d5d5d6;
    color: #999999;
    font-size: 16px;
  }
  .appoint_list-but .dp-f-1:first-child {
    border: none;
  }
  .et-color {
    color: rgb(238, 102, 138);
  }
</style>
