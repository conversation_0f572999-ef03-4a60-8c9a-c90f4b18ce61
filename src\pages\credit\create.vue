<template>
    <view class="credit-create-page">
        <wx-open-launch-weapp id="launch-btn" username="gh_899cdcd47460"
            :path="`package1/pages/hmHospitalLoan/homepage/homepage?channelId=YN00000012&code=${code}`"
            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
            <script type="text/wxtag-template">
                    <style>
                        .btn {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            opacity:0.0;
                        }
                    </style>
                    <button class="btn">交行惠民就医</button>
                </script>
        </wx-open-launch-weapp>
    </view>
</template>

<script>
import {getMyPatientDetail} from '@/api/waiting.js'
export default {
    data() {
        return {
            code: '',
            patientId: '',
        }
    },
    methods: {
        getCode() {
            const userInfo = uni.getStorageSync('userInfo')
            getMyPatientDetail(null, {
                patientid: this.patientId,
                userid: userInfo.user_id,
                openid: userInfo.openid,
            }).then((res) => {
                console.log(res)
                if (res.data.code != 1) {
                    uni.showToast({
                        title: '暂无数据',
                        icon: 'none',
                    })
                    return
                }
                this.code = res.data.data.code
            })
        },
    },
    onLoad(option) {
        console.log(option)
        this.patientId = option.id
        this.getCode()
        this.configJsSign(location.href)
    },
}
</script>

<style lang="scss" scoped>
.credit-create-page {
    position: relative;
    width: 100vw;
    height: 100vh;
    background: url(https://dyt-ertong.oss-cn-beijing.aliyuncs.com/misc/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20221123105456.png) no-repeat scroll;
    background-size:100% 100%;
}
</style>