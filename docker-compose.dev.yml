version: '3.8'

services:
  uni-app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: ertong-uni-app-dev
    ports:
      - "8080:8080"
    volumes:
      # 完整挂载项目目录，支持热重载
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - UNI_PLATFORM=h5
      - HOST=0.0.0.0
      - PORT=8080
      - CHOKIDAR_USEPOLLING=true
    networks:
      - uni-network
    restart: unless-stopped
    stdin_open: true
    tty: true
    command: ["pnpm", "run", "dev:h5"]

networks:
  uni-network:
    driver: bridge
