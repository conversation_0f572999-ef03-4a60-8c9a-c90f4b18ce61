<template>
  <view>
    <view class="header"></view>
    <view class="body">
      <view class="info">绑定就诊人</view>

      <view class="bind-form">
        <uni-forms :modelValue="postBindData">
          <uni-forms-item label="患者姓名" name="patient_name" required>
            <uni-easyinput
              type="text"
              v-model="postBindData.patient_name"
              placeholder="请输入真实姓名"
            />
          </uni-forms-item>
          <uni-forms-item label="联系电话" name="patient_phone" required>
            <uni-easyinput
              type="text"
              v-model="postBindData.patient_phone"
              placeholder="请输入预留手机号"
            />
          </uni-forms-item>
          <uni-forms-item label="ID号" name="patient_phone" required>
            <uni-easyinput type="text" v-model="postBindData.jz_card" placeholder="请输入ID号" />
          </uni-forms-item>
          <view class="form-txt-info">
            绑定个人信息后，获取医院提供健康信息及就诊相关信息查询服务。绑定时，医院需要通过您的实名认证信息查询医院健康卡帐户信息，您同意将您的姓名、身份证号、手机号提供给医院。本服务号提供的信息仅供参考，以医院实际提供的资料为准！同意本授权后，已代表您认真阅读并且接受医院服务号所有业务说明以及业务规则，对此产生争执以及纠纷医院免责。
          </view>
          <uni-forms-item>
            <uni-data-checkbox v-model="postBindData.agree" :localdata="agrees" />
          </uni-forms-item>
          <button type="primary" class="my-button" @click="submit()"> 确定 </button>
        </uni-forms>
      </view>
    </view>
    <view class="footer"></view>
  </view>
</template>

<script>
import { bindPatient } from '@/api/ucenter.js'
import { reportCmPV } from '@/to/cloudMonitorHelper';

export default {
  setup() {},
  onLoad(query) {
    reportCmPV({ title: '绑定就诊卡',query });
    this.checkLogin();
  },
  data() {
    return {
      postBindData: {
        patient_name: '',
        patient_phone: '',
        jz_card: '',
        agree: '',
        userid: 0,
        openid: '',
      },
      agrees: [{ text: '阅读并同意授权', value: 'on' }],
    }
  },
  methods: {
    submit() {
      if (!this.postBindData.patient_name) {
        uni.showToast({
          icon: 'none',
          title: `就诊人姓名不能为空`,
        })
        return
      }
      if (!this.postBindData.patient_phone || this.postBindData.patient_phone.length != 11) {
        uni.showToast({
          icon: 'none',
          title: `手机号格式不正确`,
        })
        return
      }
      if (!this.postBindData.jz_card) {
        uni.showToast({
          icon: 'none',
          title: `患者ID不能为空`,
        })
        return
      }
      if (!this.postBindData.agree) {
        uni.showToast({
          icon: 'none',
          title: `必须同意授权`,
        })
        return
      }
      const userInfo = uni.getStorageSync('userInfo')
      if (!userInfo) {
        uni.showToast({
          icon: 'error',
          title: `未获取到用户信息，请重新登陆`,
        })
        return
      }
      this.postBindData.openid = userInfo.openid
      this.postBindData.userid = userInfo.user_id
      bindPatient(this.postBindData).then((res) => {
        let rt = res.data
        if (rt.code == 1) {
          uni.navigateBack({delta: 1})
        } else {
          uni.showToast({
            icon: 'none',
            title: rt.msg,
            duration: 3000,
          })
        }
      })
    },
  },
}
</script>


<style lang="scss" scoped>
.body {
  .my-button {
    color: #fff;
    background-color: rgb(238, 102, 138);
    border: none;
  }
  .info {
    text-align: center;
    padding: 20rpx;
    background-color: rgb(237, 209, 216);
  }
  .bind-form {
    padding: 15px;
  }
  .form-txt-info {
    font-size: 13px;
    color: #666666;
  }
}
</style>
