<template>
  <view>
    <view class="notice">
      <view class="title">昆明市儿童医院</view>
      <view class="title">“新型冠状病毒肺炎”流行病学史承诺书</view>

      <view class="desc">患者姓名 {{ new Array(userinfo.patient_name.length).join('*') + userinfo.patient_name.substr(-1) }} 出生日期（8位数） {{ userinfo.patient_birthday }}</view>
      <view class="desc">性别 {{ userinfo.patient_sex == 1 ? "男" : "女" }} ID号或住院号 {{ userinfo.jz_card }}</view>
      <view class="notice-title"> 告知内容： </view>
      <view class="notice-text">根据国家发布的“新型冠状病毒肺炎”有关防控要求，该病作为急性呼吸道传染病已纳入《中华人民共和国传染病防治法》规定的乙类传染病，按甲类传染病管理。为做好疫情防控工作，及时识别“新型冠状病毒肺炎”患者，如实提供流行病学史至关重要。若您拒绝提供或隐瞒流行病学史，可能违反《中华人民共和国传染病防治法》等相关法律规定，由此造成的一切法律后果及对患者治疗带来的一切影响均由您个人承担。</view>
      <view class="notice-text blob">说明：根据《刑法》第三百三十条规定：拒绝执行卫生防疫机构依照传染病防治法提出的预防、控制措施的，引起甲类传染病传播或者有传播严重危险的，处三年以下有期徒刑或者拘役；后果特别严重的，处三年以上七年以下有期徒刑。</view>
      <view class="notice-title"> 患方承诺： </view>
      <view class="notice-text">医务人员已用通俗易懂的语言告知本人如实提供流行病学史的重要性，本人郑重承诺：</view>
      <view class="notice-text">1.患者及其监护人在发病前14天内无中高风险地区的旅行史或居住史；</view>
      <view class="notice-text">2.患者及其监护人在发病前14天内与新型冠状病毒感染的患者或疑似感染者无接触史；</view>
      <view class="notice-text">3.患者及其监护人在发病前14天内未接触过来自中高风险地区人员；</view>
      <view class="notice-text">4.患者及其监护人未接触过聚集性发病的患者,未到过聚集性发病的场所。（聚集性发病：2周内在小范围如家庭、办公室、学校班级等场所，出现2例及以上发热和/或呼吸道症状的病例。）</view>
      <view class="notice-text">本人愿意承担因隐瞒以上事实所造成的一切后果。</view>
      <view class="notice-text">本人对以上提供的健康相关信息的真实性负责，如因信息不实引起疫情传播和扩散，愿承担由此带来的全部法律责任。</view>
      <view class="uni-form-item uni-column relation">
				<view class="title">身份证号码</view>
				<input class="uni-input" type="idcard" v-model="id_card" />
			</view>
      <view class="uni-form-item uni-column relation">
				<view class="title">与患者关系</view>
				<input class="uni-input" maxlength="10" v-model="relation" type="text" placeholder="本人,父子,父女,母子,母女..." />
			</view>
    </view>
    <view class="uni-padding-wrap uni-common-mt">
      <button type="primary" class="signBtn" @click="signBtn">我保证以上承诺的真实性</button>
      <button @click="backto">我不保证以上承诺的真实性</button>
    </view>
  </view>     
</template>

<script>
import { getById,savePatientCovid19Confirm } from '@/api/waiting'
  export default {
    data() {
			return {
        id_card:'',
        relation:'',
        userinfo:{},
				signImg: {
					covd: '',
				}
			}
		},
    onLoad: function (option) {
      // uni.setStorageSync('userInfo', {
      //   id: 1461428,
      //   openid: 'o9U-Ft4X8xUVM3Sg9WqJX6H-qZhk',
      //   userid: 3,
      // })
      let query = {
          userid: uni.getStorageSync('userInfo').userid,
          openid: uni.getStorageSync('userInfo').openid,
          patientid: option.id,
        }
        getById(query).then((res) => {
          this.userinfo = res.data.data
        })
      
    },
		onShow() {
			let that = this
			// uni.$on('signData',function(data){
			// 	that.signImg = data
      //   if(data.covd){
      //     that.saveConfirm()
      //   }
			// })
      if(uni.getStorageSync('name_sign')){
        that.signImg = uni.getStorageSync('name_sign')
        that.sub_appoint(that.userinfo)
      }
		},
    methods: {
      saveConfirm(){
        let query = {
            id_card:this.id_card,
            relation:this.relation,
            name_sign:this.signImg.covd,
            jz_card:  this.userinfo.jz_card,
            patient_id:  this.userinfo.id,
            user_id: uni.getStorageSync('userInfo').userid,
          }
          savePatientCovid19Confirm(query).then((res) => {
            if (res.data.code == 1) {
              uni.setStorageSync('name_sign', "")
              uni.navigateBack({
                delta: 1,
              })
            } else {
              uni.showToast({
                title: res.data.msg,
                icon: 'none',
              })
            }
          })
      },
      signBtn() {
        if (!this.id_card) {
          uni.showToast({
            title: '身份证号码信息缺失',
            duration: 2000,
            icon: 'none',
          })
            return;
        }
        if (!(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(this.id_card))) {
          uni.showToast({
            title: '身份证号码格式不正确',
            duration: 2000,
            icon: 'none',
          })
            return;
        }
        if (!this.relation) {
          uni.showToast({
            title: '与患者关系缺失',
            duration: 2000,
            icon: 'none',
          })
            return;
        }
        
        uni.navigateTo({
          url: '/pages/signature/signature',
          animationType: 'slide-in-bottom',
          animationDuration: 200,
        })
        
      },
      backto(){
        uni.navigateBack({
          delta: 1
        });
      },
    },
  }
</script>
<style lang="scss" scoped>
  .uni-column{
    background: #fff;
  }
  .notice {
    text-align: center;
    color: #3d4145;
    padding: 5px;
    .title {
      margin-bottom: 5px;
      font-weight: 400;
      font-size: 18px;
    }
    .desc {
      font-size: 14px;
      color: #999999;
    }
    .notice-title {
      margin-top: 10px;
      margin-bottom: 5px;
      font-size: 17px;
      font-weight: bold;
      text-align: left;
    }
    .notice-text {
      font-size: 16px;
      text-align: left;
    }
    .notice-text.blob{
      font-weight: bold;
      font-style: italic;
    }
    .relation{
      border-top:  1px solid #e5e5e5;
      border-bottom:  1px solid #e5e5e5;
      display: flex;
      align-items: center;
      padding: 10px 0;
      .title{
        margin: 0;
        margin-right: 10px;
        font-size: 14px;
      }
    }
  }
  .uni-padding-wrap{
    padding: 15px;
    .signBtn{
      background: #ee668a;
      margin-bottom: 10px;
    }
  }
</style>
