import { get, post } from '@/utils/request'

const createUser = (data, params) => get('/aliapp/getTokenAli', data, { showLoading: true })//获取用户信息

const aliMedInsAuth = (data, params) => get('/aliapp/getAliMedInsAuth', data, { showLoading: true })//获取支付宝用户医保授权

const aliMedHisOrder = (data, params) => get('/aliapp/aliMedHisOrder', data, { showLoading: true })

const aliMedPayOrder = (data, params) => get('/mz/mz_mi_pay_ali', data, { showLoading: true })

const refreshToken = (data, params) => get('/aliapp/getTokenAliOnly', data, { showLoading: true })//获取获取token和刷新

const getDepartList = (data, params) => get('/hospital/departList', data, { showLoading: true })//获取科室列表

const gethosList = (data, params) => get('/hospital/hospitalList', data, { showLoading: true })//获取医院列表

const searchDepar = (data, params) => get('/hospital/doctorListAll', data, { showLoading: true })//科室搜索

const getSpecialDoctorList = (data, params) =>
 get('/hospital/specialDoctorList', data, { showLoading: true })

const getDoctorList = (data, params) => get('/hospital/doctorList', data, { showLoading: true })
// 使用指南
const getSpecialDoctorDetail = (data, params) =>
  get('/hospital/specialDoctorDetail', data, { showLoading: true })
const getDoctorDetail = (data, params) => get('/hospital/doctorDetail', data, { showLoading: true })
const getproDoctorDetail = (data, params) => get('/hospital/proDoctorList', data, { showLoading: true })

const getMyList = (data, params) => get('/patient/mylist', data, { showLoading: true })

const cancelAppoint = (data, params) => post('/appoint/cancel', data, { showLoading: true })//取消预约

const sendGreenEnergy = (data, params) => get('/aliapp/sendGreenEnergy', data, { showLoading: true })//绿色能量

const getGuide = (data, params) => get('/page/all?key=manual', data, { showLoading: true })
// 新闻资讯
const getList = (data, params) => get(`/page/all?key=${params.type}`, data, { showLoading: true })
const pageList = (data, params) =>
  get(`/healthedu/pagelist?key=${params.type}`, data, { showLoading: true })

// 预约记录
const getRecordList = (data, params) =>
  get(
    `/appoint/recordList?page=${params.page}&type=${params.type}&per_page_num=10&userid=${params.userid}&openid=${params.openid}`,
    data,
    { showLoading: true }
  )
// 预约详情
const getRecordDetail = (data, params) =>
  get(`/appoint/detail?id=${params.id}&userid=${params.userid}&openid=${params.openid}`, data, {
    showLoading: true,
  })

// 就诊人列表
const getMypatientList = (data, params) =>
  get(`/patient/mylist?userid=${params.userid}&openid=${params.openid}`, data, {
    showLoading: true,
  })
// 就诊人详情
const getMyPatientDetail = (data, params) =>
  post(
    `/patient/getbyid?patientid=${params.patientid}&userid=${params.userid}&openid=${params.openid}`,
    data,
    { showLoading: true }
  )
// 设置默认就诊人
const setDefaultPatient = (data, params) =>
  post(
    `/patient/defaultP?pat_id=${params.pat_id}&userid=${params.userid}&openid=${params.openid}`,
    data,
    { showLoading: true }
  )
// 解绑就诊人
const unBindPatient = (data, params) =>
  post(
    `/patient/delete?pat_id=${params.pat_id}&userid=${params.userid}&openid=${params.openid}`,
    data,
    { showLoading: true }
  )
//新冠告知书签名
const savePatientCovid19Notice = (data, params) =>
  post('/patient/savePatientCovid19Notice', data, { showLoading: true })

//新冠承诺书书签名
const savePatientCovid19Confirm = (data, params) =>
  post('/patient/savePatientCovid19Confirm', data, { showLoading: true })

// 首页消息推送
const getMsg = (data, params) =>
  get(`/aliapp/sendHospitalOrder`, data, { showLoading: true })

// 获取健康卡二维码
const getQrCode = (data, params) =>
  get(`/patient/getHealthQr?health_card=${params.health_card}`, data, { showLoading: true })

// 报告列表
const getReportList = (data, params) =>
  get(
    `/report/proveList?datetype=${params.datatype}&pat_id=${params.pat_id}&userid=${params.userid}&openid=${params.openid}`,
    data,
    { showLoading: true }
  )
// 报告详情
const getReportDetail = (data, params) =>
  get(
    `/report/proveDetail?report_id=${params.report_id}&pat_id=${params.pat_id}&userid=${params.userid}&openid=${params.openid}`,
    data,
    { showLoading: true }
  )

/**
 * @description 查询可取号列表
 * @param {*} data
 * @param {*} params
 * @returns
 */
//获取id
const getById = (data, params) => post(`/patient/getbyid`, data, { showLoading: true })
//挂号
const AppointDb = (data, params) => post(`/appoint/getTodayAppointDb`, data, { showLoading: true })
//提交挂号
const SubmitAppoint = (data, params) => post(`/appoint/submit`, data, { showLoading: true })

export {
  createUser,
  refreshToken,
  searchDepar,
  getMyList,
  getDepartList,
  gethosList,
  getSpecialDoctorList,
  getDoctorList,
  getDoctorDetail,
  getSpecialDoctorDetail,
  getGuide,
  getList,
  pageList,
  getRecordList,
  getRecordDetail,
  getMypatientList,
  getReportList,
  getReportDetail,
  getMyPatientDetail,
  AppointDb,
  SubmitAppoint,
  getById,
  getMsg,
  getQrCode,
  setDefaultPatient,
  unBindPatient,
  getproDoctorDetail,
  savePatientCovid19Notice,
  savePatientCovid19Confirm,
  cancelAppoint,
  sendGreenEnergy,
  aliMedInsAuth,
  aliMedHisOrder,
  aliMedPayOrder
}
