{"_from": "z-paging@^2.3.2", "_id": "z-paging@2.3.2", "_inBundle": false, "_integrity": "sha512-HQsVubeYcn/mVtGAAG62gzhBp+3gx95v96PtzD4uij0ylFdCoMPPLrpi3D+/zDU/cslc5agNHgL8DMCV9yYf9w==", "_location": "/z-paging", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "z-paging@^2.3.2", "name": "z-paging", "escapedName": "z-paging", "rawSpec": "^2.3.2", "saveSpec": null, "fetchSpec": "^2.3.2"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/z-paging/-/z-paging-2.3.2.tgz", "_shasum": "e8b31f427c458d1b3c263e1411556b98a3c151fb", "_spec": "z-paging@^2.3.2", "_where": "G:\\workspace\\kmet-front", "bugs": {"url": "https://github.com/SmileZXLee/uni-z-paging/issues"}, "bundleDependencies": false, "dcloudext": {"category": ["前端组件", "通用组件"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "393727164"}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/z-paging"}, "deprecated": false, "description": "超简单、低耦合！使用wxs+renderjs实现。支持长列表优化，支持自定义下拉刷新、上拉加载更多，支持自动管理空数据图、点击返回顶部，支持聊天分页、本地分页，支持国际化等100+项配置", "displayName": "【z-paging下拉刷新、上拉加载】高性能，全平台兼容。支持虚拟列表，支持nvue、vue3", "engines": {"HBuilderX": "^3.0.7"}, "homepage": "https://github.com/SmileZXLee/uni-z-paging#readme", "id": "z-paging", "keywords": ["下拉刷新", "上拉加载", "分页器", "nvue", "虚拟列表"], "name": "z-paging", "repository": {"type": "git", "url": "git+https://github.com/SmileZXLee/uni-z-paging.git"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y"}, "快应用": {"华为": "y", "联盟": "y"}, "Vue": {"vue2": "y", "vue3": "y"}}}}, "version": "2.3.2"}