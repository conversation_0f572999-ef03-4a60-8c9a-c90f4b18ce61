<template>
  <view>
    <view>个人中心</view>
    <view class="uni-padding-wrap">
      <cover-image class="controls-play img" @click="play" src="/static/uni.png"></cover-image>
    </view>
    <view class="minedictor">
      <view class="listTitle"> 我的就诊 </view>
      <view class="uni-panel" v-for="(item, index) in toolsList" :key="item.id">
        <view class="uni-panel-h" @click="goDetailPage(index, item.id)">
          <uni-icons :type="item.icon" size="20"></uni-icons>
          <text class="uni-panel-text">{{ item.name }}</text>
          <text class="uni-panel-icon uni-icon">{{ item.pages ? '&#xe581;' : '&#xe470;' }}</text>
        </view>
      </view>
    </view>
    <view class="minedictor">
      <view class="listTitle"> 其他问题 </view>
      <view class="uni-panel" v-for="(item, index) in otherList" :key="item.id">
        <view class="uni-panel-h" @click="goDetailPage(index, item.id)">
          <uni-icons :type="item.icon" size="20"></uni-icons>
          <text class="uni-panel-text">{{ item.name }}</text>
          <text class="uni-panel-icon uni-icon">{{ item.pages ? '&#xe581;' : '&#xe470;' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
  import { mapState } from 'vuex'
  export default {
    data() {
      return {
        title: 'title',
        toolsList: [
          {
            id: 'appoint',
            name: '预约记录',
            icon: 'wallet-filled',
          },
          {
            id: 'report',
            name: '我的报告',
            icon: 'calendar-filled',
          },
          {
            id: 'patient',
            name: '我的就诊人',
            icon: 'person-filled',
          },
        ],
        otherList: [
          {
            id: 'healthedu',
            name: '健康教育',
            icon: 'info-filled',
          },
          {
            id: 'news',
            name: '资讯消息',
            icon: 'info-filled',
          },
          {
            id: 'manual',
            name: '使用指南',
            icon: 'info-filled',
          },
        ],
      }
    },
    computed: {
      ...mapState({
        hasLeftWin: (state) => !state.noMatchLeftWindow,
      }),
    },
    methods: {
      goDetailPage(panel, e) {
        if (typeof e === 'string') {
          const url = '/pages/component/' + e + '/' + e
          if (this.hasLeftWin) {
            uni.reLaunch({
              url: url,
            })
          } else {
            uni.navigateTo({
              url: url,

              success(res) {
              },
              fail(err) {
              },
            })
          }
        } else {
          if (this.hasLeftWin) {
            uni.reLaunch({
              url: e.url,
            })
          } else {
            uni.navigateTo({
              url: e.url,
            })
          }
        }
      },
    },
    onLoad: function () {},
  }
</script>
<style>
  @import '../../../common/uni-nvue.css';
</style>
<style>
  .controls-play {
    width: 80px;
    height: 80px;
    margin: 0 auto;
  }

  .uni-padding-wrap {
    padding: 22px 0;
  }

  .uni-panel {
    margin-bottom: 0px;
  }

  .uni-panel-h {
    border-bottom: 1px solid #e5e5e5;
  }

  .listTitle {
    margin-top: 10px;
    margin-bottom: 4px;
    padding-left: 15px;
    padding-right: 15px;
    color: #999999;
    font-size: 14px;
  }

  .uni-panel-text {
    padding-left: 10px;
  }
</style>
