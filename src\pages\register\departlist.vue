<template>
	<view>
		<view class="container">
			<headerBAr title="预约挂号"></headerBAr>
			<view class="top-40">
				<view class="searchBar">
					<uni-search-bar placeholder="搜索科室，医生名称" bgColor="#fff" v-model="searchValue" @input="search" />
				</view>

				<view class="searchResult" v-if="searchValue !== ''">
					<view class="title">科室搜索结果：</view>
					<view class="searchDepartList">
						<uni-list>
							<uni-list-item :clickable="true" showArrow v-for="(item, index) in searchDepart"
								:key="index" @click="toregister(item)" :title="item.department_name" />
						</uni-list>
					</view>
					<view class="title">医师搜索结果：</view>
					<view class="searchDoctorList">
						<view class="searchDoctorItem" v-for="(item, index) in searchDoctor" :key="index"
							@click="linkDoctor(item)">
							<view class="searchDoctorimg">
								<image v-if="item.doc_avatar" :src="item.doc_avatar"></image>
								<image v-else src="/static/avatar-default.png"></image>
							</view>
							<view class="searchDoctortext">
								<view class="searchdoctor-title">
									<view>{{ item.doc_name }}</view>
									<view class="searchdoctor-leven">{{ item.title_name }}</view>
								</view>
								<view class="searchdoctor-dep">科室：{{ item.department_name }}</view>
								<view class="searchdoctor-desc">{{ item.doc_good }}</view>
							</view>
						</view>
					</view>
				</view>

				<view class="depart_cont">
					<view class="depart_tab">
						<view class="depart_tab-item" :class="departTapIndex == 0 ? 'active' : ''"
							@click="depart_tab(0)" style="border-right: 1px solid #cccccc">科室列表</view>
						<!-- #ifdef MP-ALIPAY -->
						<view class="depart_tab-item" :class="departTapIndex == 1 ? 'active' : ''"
							@click="depart_tab(1)">专家预约</view>
						<!-- #endif -->
					</view>
					<view class="depart_tab_doctor" v-if="departTapIndex == 1">
						<view>
							<view v-for="(item, index) in doctor_list" :key="index" @click="linkDoctor(item)">
								<view class="doctor-item" v-if="item.appointable == 1">
									<view class="doctor-img">
										<img :src="item.doc_avatar" />
									</view>
									<view class="doctor-text">
										<view class="doctor-title">
											<view class="doctor-name">{{ item.doc_name }}</view>
											<view class="doctor-leven" v-if="item.title_name">{{ item.title_name }}
											</view>
											<view class="doctor-leven" v-else>{{ item.level_name }}</view>
											<view class="doctor-appoint" v-if="item.src_num">可约</view>
											<view class="doctor-appoint none" v-else>约满</view>
										</view>
										<view class="doctor-desc" v-if="item.dep_name">{{ item.dep_name }}</view>
										<view class="doctor-schedule-date">
											<view class="doctor-schedule-date-item"
												v-for="(item, index) in item.sch_date_short_list" :key="index">
												{{ item }}
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="depart_tab_back" v-else>
						<view class="depart_tab-depart">
							<view class="depart_tab-depart-view">
								<view class="depart_tab-depart-left">
									<view class="depart_tab-depart-left-departList">
										<view class="depart_tab-depart-left-departList-item"
											:class="departListIndex == index ? 'active' : ''"
											v-for="(item, index) in departList" @click="departClick(index)"
											:key="index">
											{{ item.name }}
										</view>
									</view>
								</view>
								<view class="depart_tab-depart-right">
									<view class="depart_tab-depart-right-departList">
										<view v-for="(item, index) in departListR" :key="index">
											<view v-if="item.childrens && item.childrens.length">
												<view class="sub-level-title" @click="handleToggleLastLevel(item)">
													<text class="name">{{ item.name }}</text>
													<text class="arrow" :class="{ open: item.open }">&gt;</text>
												</view>
												<view v-show="item.open" class="last-level-list">
													<view class="last-level-item" v-for="(it, idx) in item.childrens"
														:key="idx" @click.stop="toregister(it)">
														<text class="text-view">{{ it.name }}</text>

														<!-- #ifdef H5  -->
														<view v-if="it.has_today_schedule === 1" class="today-box">
															<text>当日有号</text>
														</view>
														<!-- #endif -->
													</view>
												</view>
											</view>
											<view v-else class="depart_tab-depart-right-departList-item"
												@click="toregister(item)">
												<template v-if="!item.needJumpMiniApp">
													<text class="text-view">{{ item.name }}</text>
													<!-- #ifdef H5  -->
													<view v-if="item.has_today_schedule === 1" class="today-box">
														<text>当日有号</text>
													</view>
													<!-- #endif -->
												</template>
												<template v-else>
													<wx-open-launch-weapp id="launch-btn" appid="wx59458bf5b704f4ee" path="custom-tab-bar/index">
														<script type="text/wxtag-template">
															<style>
																.luanch-box{
																	border-bottom: 1px solid #dfdfdf;
																	overflow: hidden;
																	text-overflow: ellipsis;
																	white-space: nowrap;
																	height: 3em;
																	display: list-item;
																	text-align: -webkit-match-parent;
																	list-style: none;
																	line-height: 3em;
																	display: flex;
																	align-items: center;
																	justify-content: space-between;
																}

																.text-view{
																	flex: 1;
																	overflow: hidden;
																	text-overflow: ellipsis;
																	white-space: nowrap;
																}
															</style>
															<view class="luanch-box">
																<text clsss="text-view">{{ item.name }}</text>
															</view>
														</script>
													</wx-open-launch-weapp>
												</template>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
						<view class="depart_tab-doctor"></view>
					</view>
				</view>
			</view>
		</view>
		<uni-popup ref="popup" type="bottom" background-color="#fff" :safe-area=true>
			<view class="popupTitle">预诊分诊</view>
			<view class="popupScroll" style="padding: 30upx;" @touchmove.stop.prevent="disabledScroll">
				<scroll-view scroll-y="true" class="scroll-Y">
					<view>为配合本市和医院新型冠状病毒感染肺炎防控工作，请先如实回答以下问题：</view>
					<uni-forms ref="baseForm" :modelValue="alignmentFormData" label-width="auto" label-position="top">
						<uni-forms-item label="24小时内孩子体温情况" :label-width="200" :rules="[{ errorMessage: '域名项必填' }]"
							required>
							<uni-data-checkbox v-model="alignmentFormData.q1" :localdata="temperature" />
						</uni-forms-item>
						<uni-forms-item label="过去14天是否到访过港澳台和其他国家及地区？" required>
							<uni-data-checkbox v-model="alignmentFormData.q2" :localdata="bool" />
						</uni-forms-item>
						<uni-forms-item label="过去14天是否到访过境内中高风险地区？" required>
							<uni-data-checkbox v-model="alignmentFormData.q3" :localdata="bool" />
						</uni-forms-item>
						<uni-forms-item label="是否有干咳、乏力、嗅觉味觉减退、鼻塞、流涕、咽痛、结膜炎、肌痛和腹泻等新冠肺炎相关症状？" required>
							<uni-data-checkbox v-model="alignmentFormData.q4" :localdata="bool" />
						</uni-forms-item>
						<uni-forms-item label="您的健康码为：" required>
							<uni-data-checkbox v-model="alignmentFormData.q5" :localdata="hcode" />
						</uni-forms-item>
					</uni-forms>
				</scroll-view>
			</view>
			<view class="buttons">
				<button @click="closePop">取消</button>
				<button type="primary" @click="submit('popup')">确定</button>
			</view>
		</uni-popup>

		<uni-popup ref="tipPopup" type="center" :animation="false">
			<view class="popup-content">
				<view class="popup-title">温馨提示</view>
				<view class="popup-scroll">
					<view class="popup-text">
						<view v-html="departmentNotify"></view>
					</view>
				</view>
				<view class="popup-btns">
					<view class="popup-btn" @click="closeTipPopup">取消</view>
					<view class="popup-btn primary" @click="confirmTipPopup">确认</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import {
	getDepartList,
	searchDepar,
	getproDoctorDetail
} from '@/api/waiting'
import { reqGetDepList } from '../../api/wechat'
import headerBAr from '@/pages/component/header_bar/header_bar.vue'
import { getAvatarUrl } from '@/utils/utils'

export default {
	components: {
		headerBAr,
	},
	data() {
		return {
			searchValue: '',
			doctor_list: [], //医生列表
			hos_code: '',
			sch_date: '',
			departListIndex: 0,
			departTapIndex: 0,
			departList: [],
			departListR: [],
			searchDepartList: [{
				name: 'yuiahsd'
			}],
			searchDoctor: [],
			searchDepart: [],
			alignmentFormData: {
				q1: "",
				q2: "",
				q3: "",
				q4: "",
				q5: "",
			},
			temperature: [{
				text: '体温≥37.3°C',
				value: 1
			}, {
				text: '体温≤37.2℃',
				value: 0
			}],
			bool: [{
				text: '是',
				value: 1
			}, {
				text: '否',
				value: 0
			}],
			hcode: [{
				text: '绿码（低风险）',
				value: 0
			}, {
				text: '黄码（中风险）',
				value: 1
			}, {
				text: '红码（高风险）',
				value: 2
			}],
			notifyPopupType: 1, // 一级科室还是二级科室
			departmentNotify: '', //科室通知
			popupDepIndex: 0, //一级科室下标
			depInfo: {}, // 二级科室信息
		}
	},
	methods: {
		handleToggleLastLevel(item) {
			item.open = !item.open
		},
		submit() {
			for (var key in this.alignmentFormData) {
				if (this.alignmentFormData[key] === '') {
					uni.showModal({
						title: '提示',
						content: "每项条目必须选择"
					})
					return false
				}
				if (this.alignmentFormData[key] != 0) {
					uni.showModal({
						title: '提示',
						content: "请您携带孩子到医院进行现场预检分诊后就诊"
					})
					return false
				} else if (key == "q5") {
					let that = this
					uni.showModal({
						title: '提示',
						content: "尊敬的患者家属：新冠核酸检测仅针对非发热的健康患者，地点在前兴院区住院部前小广场患者及陪护核酸检测处（开单和采集标本），免收挂号费和诊查费，发热患者请到医院进行现场预检分诊后就诊.",
						success: async function (res) {
							if (res.confirm) {
								that.$refs.popup.close('top')
								uni.navigateTo({
									url: '/pages/register/doctorList?hos_code=' + that.hos_code +
										'&dep_id=A000129',
								})
							} else {
								that.$refs.popup.close('top')
							}
						},
					})
					return false
				}
			}

		},
		closePop() {
			this.$refs.popup.close('top')
		},
		disabledScroll() {
			return
		},
		depart_tab(ind) {
			this.departTapIndex = ind
		},
		departClick(index) {
			let itemInfo = this.departList[index]
			this.popupDepIndex = index
			this.notifyPopupType = 1
			this.departmentNotify = itemInfo.department_notify
			if (this.departmentNotify) {
				this.$refs.tipPopup.open()
			} else {
				this.depatrTab(index)
			}
		},
		closeTipPopup(type) {
			this.$refs.tipPopup.close();
		},
		confirmTipPopup() {
			this.$refs.tipPopup.close();
			if (this.notifyPopupType === 1) {
				this.depatrTab(this.popupDepIndex)
			} else {
				// 如果是省级名医，跳转到医生详情
				let depCode = this.depInfo.department_code;
				if (depCode.includes('S03') || depCode.includes('Q02')) {
					let depId = depCode.split('-')[0];
					let docId = depCode.split('-')[1];
					uni.navigateTo({
						url: '/pages/register/doctorDetailNew?hos_id=' +
							this.depInfo.hospital_code +
							'&dep_id=' +
							depId +
							'&doc_id=' +
							docId +
							'&hos_code=' +
							this.hos_code,
					})
					return
				}
				uni.navigateTo({
					url: '/pages/register/doctorList?hos_code=' + this.hos_code + '&dep_id=' + this.depInfo.department_code,
				})
			}
		},
		depatrTab(index) {
			let itemInfo = this.departList[index]

			// this.departListR = this.departList[index].departs
			console.log(this.departList[index])
			if (!itemInfo.childrens || itemInfo.childrens.length == 0) {
				uni.navigateTo({
					url: '/pages/register/doctorList?hos_code=' + this.hos_code + '&dep_id=' + this.departList[index].department_code,
				})
				return
			}
			this.departListR = itemInfo.childrens.map(item => {
				if (item.childrens && item.childrens.length) {
					return {
						...item,
						open: false
					}
				}
				return {
					...item
				}
			})
			this.departListIndex = index
			let id = ''
			// #ifdef MP-ALIPAY
			id = itemInfo.thrdpart_dep_code
			if (['229824', '229826', '450', '524'].includes(id)) {
				uni.showModal({
					title: '温馨提示',
					content: '6个月以内的婴儿，请预约新生儿（婴儿）门诊'
				})
			}
			if (['229846', '229895'].includes(id)) {
				uni.showModal({
					title: '温馨提示',
					content: '6个月以内的婴儿及新生儿疾病，包括咳嗽、感冒、胃肠疾病、腹痛、腹泻、便秘、食物过敏、新生儿黄疸、早产儿随访等'
				})
			}
			// #endif
		},
		linkDoctor(item) {
			uni.navigateTo({
				url: '/pages/register/doctorDetailNew?hos_id=' +
					item.hos_id +
					'&dep_id=' +
					item.dep_id +
					'&doc_id=' +
					item.doc_id +
					'&hos_code=' +
					this.hos_code,
			})
		},
		toregister(item) {
			console.log(item)
			// #ifdef MP-ALIPAY
			let id = item.thrdpart_dep_code
			if (id == 'X000000') {
				console.log('点击需要跳转小程序')
				return
			}
			const that = this
			if (
				id == '0231533' ||
				id == '0231552' ||
				id == '0231639' ||
				id == '3018'
			) {
				uni.showModal({
					title: '温馨提示',
					content: "尊敬的家长，因儿童保健科为非感染区域，为保证孩子进入科室不会遭遇院内感染，获得健康服务，希望家长在预约挂号成功后，如就诊当天孩子出现流涕、咳嗽、发热、腹泻等不适症状时，务必放弃此次就诊，待病情好转治愈后再另行预约挂号就诊。谢谢支持！儿童保健科",
					success: async function (res) {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/register/doctorList?hos_code=' + that.hos_code + '&dep_id=' + id,
							})
						} else {
							//console.log('取消申领')
						}
					},
				})
			} else if (id == 'A000021' || id == 'A000096') {
				uni.showModal({
					title: '温馨提示',
					content: "尊敬的患者家属：儿内H区为普通发热患者就诊区，不发热的患者请预约其它儿内专科、专家就诊。",
					success: async function (res) {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/register/doctorList?hos_code=' + that.hos_code + '&dep_id=' + id,
							})
						} else {
							//console.log('取消申领')
						}
					},
				})
			} else if (item.dep_id == 'A000129') {
				this.$refs.popup.open('top')
			} else {
				uni.navigateTo({
					url: '/pages/register/doctorList?hos_code=' + this.hos_code + '&dep_id=' + id,
				})
			}
			// #endif
			// #ifdef H5
			console.log('二级科室', item)

			this.depInfo = item
			this.departmentNotify = item.department_notify
			this.notifyPopupType = 2
			if (this.departmentNotify) {
				this.$refs.tipPopup.open()
			} else {
				// 如果是省级名医，跳转到医生详情
				let depCode = item.department_code;
				if (depCode.includes('S03') || depCode.includes('Q02')) {
					let depId = depCode.split('-')[0];
					let docId = depCode.split('-')[1];
					uni.navigateTo({
						url: '/pages/register/doctorDetailNew?hos_id=' +
							item.hospital_code +
							'&dep_id=' +
							depId +
							'&doc_id=' +
							docId +
							'&hos_code=' +
							this.hos_code,
					})
					return
				}
				uni.navigateTo({
					url: '/pages/register/doctorList?hos_code=' + this.hos_code + '&dep_id=' + item.department_code,
				})
			}
			// #endif
		},
		async left(hos_code) {
			// const query = { hos_code }
			// let { data } = await getDepartList(query)
			// this.departList = data.data
			// this.departListR = data.data[0].departs
			// #ifdef MP-ALIPAY
			const url =
				`https://appv3-api.ynhdkc.com/apis/v1/tenant/customer/public/hospital-areas/${hos_code == 871003 ? 16 : 12}/departments/tree`
			const _this = this
			uni.request({
				url,
				method: 'POST',
				success(res) {
					_this.departList = res.data.list
					_this.departListR = res.data.list[0].childrens.map(item => {
						if (item.childrens && item.childrens.length) {
							return {
								...item,
								open: false
							}
						}
						return {
							...item
						}
					})
				},
				fail(err) {
					console.error('获取科室列表失败：', err)
				}
			})
			// #endif
			// #ifdef H5
			reqGetDepList({
				hospital_code: hos_code,
			}).then((res) => {
				console.log(res)
				if (res.data.data) {
					this.departList = this.renameChildrenToChildrens(res.data.data)

					// 有科室需要设置跳转到微信小程序
					const index = this.departList.findIndex(item => item.department_code === '032131')
					if (index !== -1) {
						let childrenFirst = this.departList[index].childrens[0];
						this.departList[index].childrens[0] = {
								id:'000001',
								needJumpMiniApp:true,
								appid:'wx59458bf5b704f4ee',
								name:'脑电图检查自助预约',
								sort:'1',
								hospital_area_code:childrenFirst.hospital_code,
								thrdpart_dep_code:'X000000',
								parent_id: childrenFirst.parent_code,
								status: 1,
								departs:null
							}
					}


				}
				if (this.departList.length > 0 && this.departList[0].childrens) {
					console.log('科室列表:', this.departList)
					this.departListR = this.departList[0].childrens.map(item => {
						if (item.childrens && item.childrens.length) {
							return {
								...item,
								open: false
							}
						}
						return {
							...item
						}
					})
				}

			})
			// #endif
		},
		/**
		 * 将树形数组中的department-name,转为name
		 * @param {Array} treeArray - 树形数组数据
		 * @returns {Array} - 转换后的树形数据
		 */
		renameChildrenToChildrens(treeArray) {
			return treeArray.map(node => {
				const newNode = { ...node }; // 浅拷贝当前节点
				newNode.name = node.department_name // 将 `dep_name` 重命名为 `name`
				// 如果存在 `children`，则递归处理并改为 `childrens`
				if (node.children) {
					newNode.childrens = this.renameChildrenToChildrens(node.children);
					delete newNode.children; // 删除旧的 `children` 字段
				}

				return newNode;
			});
		},
		search(res) {
			if (this.searchValue != '') {
				let query = {
					//医生
					hos_code: this.hos_code,
					keywords: this.searchValue,
				}
				searchDepar(query).then((res) => {
					this.searchDoctor = res.data.data.map(item => {
						item.doc_avatar = getAvatarUrl(item.doc_avatar)
						return item
					})
				})
				console.log(this.departList)
				let searchDepartList = []
				for (let k in this.departList) {
					if (this.departList[k].childrens && this.departList[k].childrens.length > 0) {
						for (let i = 0; i < this.departList[k].childrens.length; i++) {
							if (this.departList[k].childrens[i].department_name.indexOf(this.searchValue) != -1) {
								searchDepartList.push(this.departList[k].childrens[i])
							}
						}
					}
				}
				this.searchDepart = searchDepartList
			} else {
				this.searchDoctor = []
				this.searchDepart = []
			}
		},
		// input(res) {
		//   console.log('----input:', res)
		// },
		// clear(res) {
		//   uni.showToast({
		//     title: 'clear事件，清除值为：' + res.value,
		//     icon: 'none',
		//   })
		// },
		// blur(res) {
		//   uni.showToast({
		//     title: 'blur事件，输入值为：' + res.value,
		//     icon: 'none',
		//   })
		// },
		// focus(e) {
		//   uni.showToast({
		//     title: 'focus事件，输出值为：' + e.value,
		//     icon: 'none',
		//   })
		// },
		cancel(res) {
			this.searchDoctor = []
		},
	},
	onLoad: function (option) {
		this.hos_code = option.hos_code
		this.left(parseInt(this.hos_code))
		getproDoctorDetail({
			hos_code: this.hos_code
		}).then((res) => {
			let doctor_list = new Array()
			let _data = res.data.data
			this.show = false
			if (_data != false) {
				this.show = true
				this.dataList = _data.sch_date_list
				for (var i = 0; i < _data.doctor_list.length; i++) {
					var appoint_status = _data.doctor_list[i]['sch_date']
					_data.doctor_list[i].doc_avatar = getAvatarUrl(_data.doctor_list[i].doc_avatar)
					if (appoint_status) {
						_data.doctor_list[i].appointable = 1
					} else {
						_data.doctor_list[i].appointable = 0
					}
				}
				this.doctor_list = _data.doctor_list
			}
		})
		// 获取jssdk 授权
		this.configJsSign(location.href)
	},
	onBackPress() {
		// #ifdef APP-PLUS
		plus.key.hideSoftKeybord()
		// #endif
	},
}
</script>
<style scoped lang="scss">
.popupTitle {
	color: #555;
	border-bottom: 1px solid #eee;
	padding: 20px 25px 8px;
	font-weight: 400;
	font-size: 18px;
	text-align: center;
}

.buttons {
	display: flex;
	display: -webkit-box;
	display: -webkit-flex;

	uni-button,
	button {
		flex: 1;
		margin-left: 0;
		margin-right: 0;
		border-radius: 0;
	}

	uni-button:after,
	button:after {
		border-radius: 0;
	}

	uni-button[type=primary],
	button[type=primary] {
		background-color: #ee668a;
	}
}

.popup-content {
	margin: 0 auto;
	background-color: #FFF;
	border-radius: 32rpx;
	overflow: hidden;
	width: 80%;

	.popup-title {
		padding: 1.3em 1.6em 0.5em;
		font-size: 30rpx;
		line-height: 1.3;
		word-wrap: break-word;
		word-break: break-all;
		font-weight: 600;
		color: #333;
		text-align: left;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;


		&::after {
			content: '';
			position: absolute;
			display: block;
			width: 100%;
			height: 1rpx;
			bottom: 0;
			left: 0;
			background-color: #eee;
		}
	}

	.popup-scroll {
		max-height: 600rpx;
		min-height: 200rpx;
		overflow-y: scroll;
	}

	.popup-text {
		display: flex;
		flex-direction: column;
		padding: 24rpx;
		font-size: 28rpx;
		line-height: 1.8em;
		overflow: scroll;
	}

	.popup-btns {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 96rpx;
		border-top: 1rpx solid #eee;

		.popup-btn {
			flex: 1;
			text-align: center;
			line-height: 96rpx;
			font-size: 36rpx;
			color: #5f646e;
		}

		.primary {
			color: #fff;
			background-color: rgb(238, 102, 138);
			margin-bottom: -.1em;
		}

		.disabled {
			color: #fff;
			background-color: #D7D7D7;
		}
	}
}

.scroll-Y {
	height: 870rpx;
}

.top-40 {
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
	z-index: 1;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}

.container {
	overflow: hidden;

	.searchResult {
		.title {
			background: #fbf9fe;
			padding: 10px 15px 5px 15px;
			color: #999999;
			font-size: 14px;
		}
	}

	.searchDepartList {
		margin-bottom: 20px;
		font-size: 17px;

		.searchDepartItem {
			padding: 10px 15px;
			position: relative;
			display: -webkit-box;
			display: -webkit-flex;
			display: flex;
			-webkit-box-align: center;
			-webkit-align-items: center;
			align-items: center;
		}
	}

	.searchDoctorItem {
		padding: 5px 15px;
		color: #000;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;
		border-top: 1px solid #e5e5e5;
		background: #fff;

		.searchDoctorimg {
			margin-right: 0.8em;
			height: 68px;
			width: 54px;
			line-height: 68px;
			text-align: center;
			overflow: hidden;
		}

		.searchDoctorimg image {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.searchDoctortext {
			flex: 1;

			.searchdoctor-title {
				color: #444;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-weight: 400;
				font-size: 18px;
				width: auto;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				word-wrap: normal;
				word-wrap: break-word;
				word-break: break-all;

				.searchdoctor-leven {
					color: #555;
					font-size: 16px;
					flex: 1;
					padding-left: 5px;
				}
			}

			.searchdoctor-dep {
				color: #444;
				font-weight: 400;
				font-size: 14px;
			}

			.searchdoctor-desc {
				color: #444;
				font-size: 13px;
				line-height: 1.2;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				margin: 8px 0;
			}
		}
	}
}

.search-result {
	padding-top: 10px;
	padding-bottom: 20px;
	text-align: center;
}

.searchBar {
	position: relative;
	background-color: #efeff4;
}

.uni-searchbar {
	padding: 8px 10px;
}

.uni-searchbar__box {
	height: 20px;
}

.depart_cont {
	position: relative;
	height: 100%;
	background: #fff;
}

.depart_tab {
	display: -webkit-flex;
	display: flex;
	position: absolute;
	z-index: 500;
	top: 0;
	width: 100%;
	background: #fff;
}

.depart_tab-item {
	position: relative;
	display: block;
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	flex: 1;
	padding: 5px 0;
	text-align: center;
	font-size: 15px;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.depart_tab-item.active {
	background-color: white;
	border-bottom: 2px solid rgb(238, 102, 138);
	color: rgb(238, 102, 138);
}

.doctor-item {
	padding: 5px 15px;
	color: #000;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
	border-top: 1px solid #e5e5e5;
	background: #fff;
}

.doctor-img {
	margin-right: 0.8em;
	width: 45px;
	height: 68px;
	line-height: 68px;
	text-align: center;
	overflow: hidden;
}

.doctor-img img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.doctor-text {
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	flex: 1;
}

.doctor-title {
	color: #444;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 400;
	font-size: 18px;
	width: auto;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	word-wrap: normal;
	word-wrap: break-word;
	word-break: break-all;
}

.doctor-leven {
	color: #555;
	font-size: 16px;
	flex: 1;
	padding-left: 5px;
}

.doctor-appoint {
	padding: 3px 10px;
	background-color: #ee668a;
	margin-right: 3px;
	border-radius: 5px;
	display: inline-block;
	min-width: 8px;
	color: #ffffff;
	line-height: 1.2;
	text-align: center;
	font-size: 12px;
	vertical-align: middle;
}

.doctor-appoint.none {
	background-color: #666;
}

.doctor-desc {
	color: #444;
	font-size: 19px;
	line-height: 1.2;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	margin: 8px 0;
}

.doctor-schedule-date {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	padding: 0 15px;
	margin-bottom: 10px;
}

.doctor-schedule-date-item {
	margin-right: 3px;
	border-radius: 5px;
	background-color: #ee668a;
	display: inline-block;
	padding: 3px 5px;
	min-width: 8px;
	color: #ffffff;
	line-height: 1.2;
	text-align: center;
	font-size: 12px;
	vertical-align: middle;
}

.depart_tab_doctor {
	overflow: scroll;
	-webkit-overflow-scrolling: touch;
}

.depart_tab_back,
.depart_tab_doctor {
	box-sizing: border-box;
	height: 100%;
	padding-top: 36px;
}

.depart_tab-depart {
	height: 100%;
	overflow: auto;
}

.depart_tab-depart-view {
	display: -webkit-flex;
	background-color: #f3f3f3;
	display: flex;
	position: relative;
	top: 0px;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100%;
}

.depart_tab-depart-left {
	background-color: #f3f3f3;
	overflow: scroll;
	-webkit-overflow-scrolling: touch;
	text-align: center;
	font-size: 15px;
	width: 45%;
}

.depart_tab-depart-left-departList-item {
	display: list-item;
	text-align: -webkit-match-parent;
	list-style: none;
	line-height: 3em;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.depart_tab-depart-left-departList-item.active {
	background-color: white;
	color: rgb(238, 102, 138);
}

.depart_tab-depart-right {
	width: 100%;
	text-align: left;
	overflow: scroll;
	-webkit-overflow-scrolling: touch;
	font-size: 15px;
	background-color: #ffffff;
}

.depart_tab-depart-right-departList {
	display: block;
	list-style-type: disc;
	-webkit-margin-start: 0px;
	-webkit-margin-end: 0px;
}

.depart_tab-depart-right-departList-item {
	padding-left: 1em;
	border-bottom: 1px solid #dfdfdf;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	height: 3em;
	display: list-item;
	text-align: -webkit-match-parent;
	list-style: none;
	line-height: 3em;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.today-box {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(238, 102, 138, 0.2);
	border: 1rpx solid rgb(238, 102, 138);
	color: rgb(238, 102, 138);
	font-size: 24rpx;
	border-radius: 4rpx;
	margin-right: 12rpx;
	padding: 0 0.4em;
	height: 1.5em;
}

.sub-level-title {
	padding-left: 1em;
	border-bottom: 1px solid #dfdfdf;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	height: 3em;
	display: list-item;
	text-align: -webkit-match-parent;
	list-style: none;
	line-height: 3em;
	display: flex;
	justify-content: space-between;

	.name {
		flex: 1;
		margin-right: 0.25rem;
	}

	.arrow {
		margin-right: 0.25rem;
		transform: scaleY(1.5);

		&.open {
			transform: rotate(90deg) scaleY(1.5);
		}
	}
}

.text-view {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.last-level-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding-left: 2em;
	border-bottom: 1px solid #dfdfdf;
	height: 3em;
	text-align: -webkit-match-parent;
	list-style: none;
	line-height: 3em;

	&:last-child {
		border: 0;
	}
}
</style>