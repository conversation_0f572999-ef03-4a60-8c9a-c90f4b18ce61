<template>
  <view>
    <view id="container">
      <headerBAr title="预约挂号"></headerBAr>
      <view class="top-40" v-if="!show">
        <view class="noMore">
          <view class="noMore-info">
            <view class="noMore-info-text">暂未获取到排班数据</view>
          </view>
        </view>
      </view>
      <view class="top-40" v-else>
        <view class="schdate-list">
          <view class="timeSchBox">
            <view class="timeSch">
              <view class="timeItem" :class="TabIndex == index ? 'active' : 'null'" v-for="(item, index) in dataList"
                :key="index" @click="Tabdate(index)">
                <view class="date">{{ item.week }}</view>
                <view class="time">{{ item.sch_date_short }}</view>
                <view class="state" v-if="item.src_num">有号</view>
                <view class="state none" v-else>无号</view>
              </view>
            </view>
          </view>
        </view>
        <view class="doctor-info">
          <view v-for="(item, index) in showDocList" :key="index" @click="linkDoctor(item)">
            <view class="doctor-item">
              <view class="doctor-img">
                <image class="img" :src="item.doc_avatar ? item.doc_avatar : '/static/avatar-default.png'"
                  mode="aspectFit" />
              </view>
              <view class="doctor-text">
                <view class="doctor-title">
                  <view class="doctor-name">{{ item.doc_name }}</view>
                  <view class="doctor-leven" v-if="item.title_name">{{ item.title_name }}</view>
                  <view class="doctor-leven" v-else>{{ item.register_name }}</view>
                  <view class="doctor-appoint" v-if="item.appointable == 1">可约</view>
                  <view class="doctor-appoint none" v-else>{{ item.appointable === 0 ? '约满' : '停诊' }}</view>
                </view>
                <view class="doctor-desc" v-if="item.doc_good">擅长：{{ item.doc_good }}</view>
                <!-- view class="doctor-schedule-date">
                  <view
                    class="doctor-schedule-date-item"
                    v-for="(item, index) in item.sch_date_short_list"
                    :key="index"
                    >{{ item }}</view
                  >
                </view> -->
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import headerBAr from '@/pages/component/header_bar/header_bar.vue'
import { getDoctorList, getSpecialDoctorList } from '@/api/waiting'
import { reqGetDocList } from '../../api/wechat'
import { getDiffDate, getAvatarUrl } from '@/utils/utils.js'
export default {
  data() {
    return {
      TabIndex: 0,
      hos_code: '',
      dataList: [], //日期列表
      doctor_list: [], //医生列表
      showDocList: [], //医生列表
      show: true,
      weightMap: new Map([
        ["省级名医", 100],
        ["主任医师", 90],
        ["主任技师", 90],
        ["副主任医师", 80],
        ["专科", 70],
        ["技师", 70],
        ["普通", 60]
      ])
    }
  },
  methods: {
    Tabdate(index) {
      this.TabIndex = index
      this.sch_date = this.dataList[index].sch_date
      this.loadDoctor(this.sch_date, this.doctor_list)
    },
    linkDoctor(item) {
      console.log(item)
      if (item.appointable != 1) {
        return
      }
      // doctordetail doctorDetailNew
      uni.navigateTo({
        url:
          '/pages/register/doctorDetailNew?hos_id=' +
          item.hos_id +
          '&dep_id=' +
          item.dep_id +
          '&doc_id=' +
          item.doc_id +
          '&hos_code=' +
          this.hos_code,
      })
    },
    // 根据日期加载可显示的医生列表，并判断医生是否可约
    loadDoctor(sch_date, Odoctor_list) {
      let showList = new Array()
      for (var i = 0; i < Odoctor_list.length; i++) {
        if (Odoctor_list[i]['sch_date_list'].indexOf(sch_date) >= 0) {
          var appoint_status = Odoctor_list[i]['sch_date_status']

          if(appoint_status[sch_date].book_status == 2){
            // 停诊状态
            Odoctor_list[i].appointable = 2
          } else if(appoint_status[sch_date].src_num > 0 &&
            appoint_status[sch_date].book_status == 1) {
              // 可约状态
            Odoctor_list[i].appointable = 1
          } else {
            // 约满状态
            Odoctor_list[i].appointable = 0
          }
        } else {
          // 选中的日期不在医生的排班日期列表中，不显示到医生列表中
          Odoctor_list[i].appointable = -1 // 不出诊
        }

        if (Odoctor_list[i].dep_id == '0231518') {
          if (Odoctor_list[i]['sch_date_list'].indexOf(sch_date) >= 0) {
            showList.push(Odoctor_list[i])
          }
        } else {
          if (Odoctor_list[i].appointable == -1) {
            continue
          }
          showList.push(Odoctor_list[i])
        }
      }
      // this.doctor_list = Odoctor_list
      this.showDocList = showList
    }
  },
  created() { },
  onLoad: function (option) {
    this.hos_code = option.hos_code
    let query = {
      hos_code: option.hos_code,
      dep_id: option.dep_id,
      from_date: getDiffDate(0),
      end_date: getDiffDate(4),
      _: Date.now()
    }
    // #ifdef MP_ALIPAY
    let httplink = getDoctorList
    if (option.hos_code == '871006') {
      httplink = getSpecialDoctorList
    }
    httplink(query).then((res) => {
      let doctor_list = new Array()
      let _data = res.data.data
      this.show = false
      if (_data != false) {
        this.show = true
        this.dataList = _data.sch_date_list
        this.sch_date = this.dataList[0].sch_date
        for (var i = 0; i < _data.doctor_list.length; i++) {
          if (_data.doctor_list[i]['sch_date_list'].indexOf(this.sch_date) >= 0) {
            var appoint_status = _data.doctor_list[i]['sch_date_status']
            if (
              appoint_status &&
              appoint_status[this.sch_date] &&
              appoint_status[this.sch_date].src_num > 0
            ) {
              //if (appoint_status && appoint_status[sch_date] && appoint_status[sch_date].src_num>0 && appoint_status[sch_date].book_status == 0) {
              _data.doctor_list[i].appointable = 1
            } else {
              _data.doctor_list[i].appointable = 0
            }
          } else {
            _data.doctor_list[i].appointable = -1 // 不出诊
          }
          if (_data.doctor_list[i].dep_id == '0231518') {
            if (_data.doctor_list[i]['sch_date_list'].indexOf(this.sch_date) >= 0) {
              doctor_list.push(_data.doctor_list[i])
            }
          } else {
            if (_data.doctor_list[i].appointable == -1) {
              continue
            }
            doctor_list.push(_data.doctor_list[i])
          }
        }
        this.doctor_list = _data.doctor_list
      }
    })
    // #endif
    // #ifdef H5
    console.log('H5------------------------------------')
    reqGetDocList(query).then((res) => {
      console.log(res)
      let _data = res.data.data
      this.show = false
      if (_data.sch_date_list || _data.doctor_list) {
        this.show = true
        this.dataList = _data.sch_date_list

        if (this.dataList && this.dataList.length > 0) {
          this.sch_date = this.dataList[0].sch_date
        }
        if (_data.doctor_list && _data.doctor_list.length > 0) {
          for (var i = 0; i < _data.doctor_list.length; i++) {
            _data.doctor_list[i].doc_avatar = getAvatarUrl(_data.doctor_list[i].doc_avatar)
          }
          // 对医生进行排序，先按照权重，再按照拼音首字母排序
          this.doctor_list = _data.doctor_list.sort((a, b) => {
            const getWeight = (doc) => {
              const title = doc.register_name;
              return this.weightMap.get(title) || 0;
            };

            // 主排序：权重降序
            const weightDiff = getWeight(b) - getWeight(a);
            if (weightDiff !== 0) return weightDiff;

            // 次排序：拼音首字母升序
            const pinyinA = (a.doc_name_pinyin || '').charAt(0).toLowerCase();
            const pinyinB = (b.doc_name_pinyin || '').charAt(0).toLowerCase();
            return pinyinA.localeCompare(pinyinB);
          });
          this.loadDoctor(this.sch_date, this.doctor_list)
        }
      }
    })
    // #endif
  },
  onBackPress() {
    // #ifdef APP-PLUS
    plus.key.hideSoftKeybord()
    // #endif
  },
  components: {
    headerBAr,
  },
}
</script>
<style scoped>
.top-40 {
  position: relative;
}

.container {
  overflow: hidden;
}

.schdate-list {
  background-color: #ffffff;
  margin-top: 10px;
  padding: 0 6px;
  position: relative;
  overflow: hidden;
  border-top: 1px solid #e5e5e5;
}

.timeSchBox {
  overflow: scroll;
}

.timeSch {
  float: left;
}

.timeSch,
.timeItem {
  padding: 5px 0;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.timeItem {
  width: 69.2308px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  text-align: center;
  font-size: 14px;
  -webkit-justify-content: center;
  justify-content: center;
}

.timeSch .timeItem.active {
  background-color: rgb(238, 102, 138);
  color: #fff;
  border-radius: 0.5em;
}

.timeItem .time {
  font-size: 0.8em;
}

.timeItem .state {
  color: #51ccdd;
}

.timeItem .state.none {
  color: rgb(102, 102, 102);
}

.doctor-item {
  padding: 5px 15px;
  color: #000;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  border-top: 1px solid #e5e5e5;
  background: #fff;
}

.doctor-img {
  margin-right: 0.8em;
  width: 45px;
  height: 68px;
  line-height: 68px;
  text-align: center;
  overflow: hidden;
}

.doctor-img .img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.doctor-text {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
}

.doctor-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 400;
  font-size: 17px;
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
  word-wrap: break-word;
  word-break: break-all;
}

.doctor-leven {
  color: #999999;
  font-size: 13px;
}

.doctor-appoint {
  padding: 3px 10px;
  background-color: #ee668a;
  margin-right: 3px;
  border-radius: 5px;
  display: inline-block;
  min-width: 8px;
  color: #ffffff;
  line-height: 1.2;
  text-align: center;
  font-size: 12px;
  vertical-align: middle;
}

.doctor-appoint.none {
  background-color: #666;
}

.doctor-desc {
  color: #999999;
  font-size: 13px;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  margin: 3px 0;
}

.doctor-schedule-date {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  padding: 0 15px;
  margin-bottom: 10px;
}

.doctor-schedule-date-item {
  margin-right: 3px;
  border-radius: 5px;
  background-color: #ee668a;
  display: inline-block;
  padding: 3px 5px;
  min-width: 8px;
  color: #ffffff;
  line-height: 1.2;
  text-align: center;
  font-size: 12px;
  vertical-align: middle;
}

.noMore {
  background-color: #ffffff;
  position: relative;
  overflow: hidden;
}

.noMore-info {
  width: 65%;
  margin: 1.5em auto;
  line-height: 22px;
  font-size: 14px;
  text-align: center;
  border-top: 1px solid #e5e5e5;
  margin-top: 33px;
}

.noMore-info-text {
  position: relative;
  top: -12px;
  padding: 0 7.5px;
  background-color: #ffffff;
  color: #999999;
  display: inline-block;
}

uni-button {
  background: #ee668a;
}

.button-hover {
  background: #ca738a;
}
</style>
