version: '3.8'

services:
  uni-app-prod:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: ertong-uni-app-prod
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
      - UNI_PLATFORM=h5
    networks:
      - uni-network
    restart: unless-stopped
    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  uni-network:
    driver: bridge
