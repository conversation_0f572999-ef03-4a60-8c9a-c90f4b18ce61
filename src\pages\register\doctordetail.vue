<template>
  <view>
    <view class="container">
      <headerBAr title="预约挂号"></headerBAr>
      <view class="doctor-detail top-40">
        <view class="media-box">
          <view class="media-box_appmsg">
            <view class="doctor_img">
				      <image :src="detail.doc_avatar ? detail.doc_avatar : '/static/avatar-default.png'" mode="aspectFit" class="img"></image>
            </view>
            <view class="doctor_info">
              <view class="doctor_info_name">
                <view>{{ detail.doc_name }}</view>
                <view class="sub_title">{{ detail.title_name }}</view>
              </view>
              <view class="doctor_info_lebal" v-if="detail.dep_name">科室：{{ detail.dep_name }}</view>
              <!-- <view class="doctor_info_lebal">{{
                detail.doc_good ? detail.doc_good : detail.doc_info
              }}</view> -->
              <flodText
                :text="detail.doc_good ? detail.doc_good : detail.doc_info"
                ></flodText>
            </view>
          </view>
        </view>
        <view class="cellBox">
          <view class="cellList" v-for="(item, index) in schlist" :key="index">
            <view class="doctor-datepart" @click="drawer(index)">
              <view>{{ item.sch_date }}</view>
              <view>{{ item.week }}&nbsp;{{ item.time_type_desc }}</view>
              <view>余号：{{ item.src_num }}个</view>
            </view>
            <view class="doctor-datepart-grids" v-show="gridInd == index ? true : false">
              <view
                class="doctor-datepart-grids-item"
                @click="toAppoint(item, li)"
                :class="li.forbid_book ? 'none' : null"
                v-for="(li, i) in item.partSchedule"
                :key="i"
              >
                <view class="text">{{ li.start_time }}-{{ li.end_time }}</view>
                <view class="text"> 余号:{{ li.src_num }}个</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { getDoctorDetail, getSpecialDoctorDetail } from '@/api/waiting'
  import headerBAr from '@/pages/component/header_bar/header_bar.vue'
  import { reqGetDocDetail } from '../../api/wechat'
  import flodText from '@/components/flod-text/flod-text.vue'
  import { getAvatarUrl } from '@/utils/utils'

  export default {
    components: {
      headerBAr,
      flodText
    },
    data() {
      return {
        detail: {},
        schlist: {},
        keys: {},
        gridInd: -1,
      }
    },
    methods: {
      drawer(index) {
		if (this.gridInd == index) {
			this.gridInd = -1
		} else {
			this.gridInd = index
		}
      },
      toAppoint(item, li) {
        if (li.src_num > 0) {
          uni.navigateTo({
            url:
              '/pages/register/appointDetail?hos_name=' +
              this.detail.hos_name +
              '&doc_name=' +
              this.detail.doc_name +
              '&dep_name=' +
              this.detail.dep_name +
              '&level_name=' +
              this.detail.level_name +
              '&time_type_desc=' +
              item.time_type_desc +
              '&schedule_id=' +
              item.schedule_id +
              '&sch_date=' +
              item.sch_date +
              '&time_type=' +
              item.time_type +
              '&queue_sn=' +
              li.queue_sn +
              '&start_time=' +
              li.start_time +
              '&end_time=' +
              li.end_time +
              '&hos_id=' +
              this.keys.hos_id +
              '&dep_id=' +
              this.keys.dep_id +
              '&doc_id=' +
              this.keys.doc_id +
              '&hos_code=' +
              this.keys.hos_code,
          })
        }
      },
    },
    onLoad: function (option) {
      this.keys = option
      let query = {
        doc_id: option.doc_id,
        dep_id: option.dep_id,
        hos_code: option.hos_code,
      }
      // #ifdef MP-ALIPAY
      let httplink = getDoctorDetail
      if (option.hos_code == '871006') {
        httplink = getSpecialDoctorDetail
      }
      httplink(query).then((res) => {
        this.detail = res.data.data
        this.detail.doc_avatar = getAvatarUrl(this.detail.doc_avatar)
        this.schlist = this.detail.schedule
        for (var key in this.schlist) {
          this.gridInd = key
          return
        }
      })
      // #endif

      // #ifdef H5
      console.log('H5')
      reqGetDocDetail(query).then((res) => {
          this.detail = res.data.data
          this.schlist = this.detail.schedule
          for (var key in this.schlist) {
            this.gridInd = key
            return
          }
        })
        // #endif
    },
    onBackPress() {
      // #ifdef APP-PLUS
      plus.key.hideSoftKeybord()
      // #endif
    },
  }
</script>
<style scoped>
  .container {
    overflow: scroll;
  }
  .doctor-detail {
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
  }
  .doctor-detail .media-box {
    padding: 5px 15px;
  }
  .media-box_appmsg {
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
  }
  .doctor_img {
    margin-right: 0.8em;
    width: 45px;
    height: 68px;
    line-height: 68px;
    text-align: center;
    overflow: hidden;
  }
  .doctor_img .img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .doctor_info {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    min-width: 0;
  }
  .doctor_info_name {
    display: flex;
    -webkit-display: flex;
    align-items: flex-end;
    -webkit-align-items: flex-end;
    margin-bottom: 8px;
    font-weight: 400;
    font-size: 17px;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    word-wrap: break-word;
    word-break: break-all;
  }
  .sub_title {
    font-size: 12px;
    margin-left: 0.2em;
    color: #999;
  }
  .doctor_info_lebal {
    color: rgb(238, 102, 138);
    font-size: 16px;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
  .cellBox {
    margin-top: 20px;
    padding-bottom: 40px;
  }
  .cellList {
    background-color: #ffffff;
    line-height: 1.47058824;
    font-size: 17px;
    overflow: hidden;
    position: relative;
  }
  .doctor-datepart {
    padding: 10px 15px;
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    font-size: 14px;
    -webkit-justify-content: space-between;
    justify-content: space-between;
  }
  .doctor-datepart-grids {
    padding: 5px 15px;
    position: relative;
    overflow: hidden;
  }
  .doctor-datepart-grids-item {
    position: relative;
    float: left;
    padding: 20px 10px;
    width: 33.33333333%;
    box-sizing: border-box;
    padding: 4px;
    border: 6px solid rgba(255, 255, 255, 1);
    background-color: rgb(238, 102, 138);
    border-radius: 10px;
  }
  .doctor-datepart-grids-item.none {
    background-color: #c5c0c2;
  }
  .doctor-datepart-grids-item .text {
    display: block;
    text-align: center;
    color: #000000;
    font-size: 14px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
</style>
