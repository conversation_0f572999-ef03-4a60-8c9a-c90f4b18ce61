<template>
  <view>
    <view class="detail-container">
      <headerBAr title="预约挂号"></headerBAr>
      <view class="doctor-detail top-40">
        <view class="media-box">
          <view class="media-box_appmsg">
            <view class="doctor_img">
              <image :src="detail.doc_avatar ? detail.doc_avatar : '/static/avatar-default.png'" mode="aspectFit"
                class="img"></image>
            </view>
            <view class="doctor_info">
              <view class="doctor_info_name">
                <view>{{ detail.doc_name }}</view>
                <view class="sub_title">{{ registerName }}</view>
              </view>
              <view class="doctor_info_lebal" v-if="detail.dep_name">科室：{{ detail.dep_name }}</view>
              <!-- <view class="doctor_info_lebal">{{
                detail.doc_good ? detail.doc_good : detail.doc_info
              }}</view> -->
              <flodText :text="detail.doc_good ? detail.doc_good : detail.doc_info"></flodText>
            </view>
          </view>
        </view>
        <view class="sch-container">
          <view class="sch-box">
            <view class="sch-list-box">
              <view class="sch-item" v-for="(item, index) in schlist" :class="[activeSchId === item.schedule_id ? 'active' : '',
              item.src_num > 0 ? '' : 'disabled']" :key="item.schedule_id">
                <view @click="drawer(item)">
                  <text>{{ item.week }}</text>
                  <text>{{ item.sch_date_short }}</text>
                  <text class="state" v-if="item.src_num > 0">有号</text>
                  <text v-else>无号</text>
                </view>
              </view>
            </view>
          </view>
          <view class="select-sch-time">
            <text>{{ activeSch.sch_date }}&nbsp;&nbsp;{{ activeSch.weekText }}</text>
          </view>
          <view class="time-sch-box">
            <view v-if="timeSchList.length > 0" style="width: 100%;">
              <view class="time-sch-item" v-for="(item, index) in timeSchList" :key="item.end_time">
                <view class="top">
                  <text>{{ item.start_time }} - {{ item.end_time }}</text>
                  <text style="font-size: 12px; padding-right: 4px;">剩余：{{ item.src_num }}</text>
                </view>
                <view class="bottom">
                  <text>{{ detail.dep_name }} 【{{ registerName }}】</text>
                  <view class="doctor-appoint" v-if="item.src_num > 0" @click="toAppoint(item)">可约</view>
                </view>
              </view>
            </view>
            <view v-else style="line-height: 50px; text-align: center; width: 100%; color: #DDD;">暂无号源</view>
            <uni-collapse v-if="noNumTiemSchList.length > 0" accordion v-model="collapseNoNum">
              <uni-collapse-item title="默认开启" :show-arrow="false">
                <template v-slot:title>
                  <view class="no-num-tip">
                    <text>约满号源 &nbsp;{{ collapseNoNum === '0' ? '折叠' : '展开' }}</text>
                    <text class="iconfont " :class="collapseNoNum === '0' ? 'icon-less' : 'icon-moreunfold'"></text>
                  </view>
                </template>
                <view class="">
                  <view class="time-sch-item time-sch-item-disabled" style="box-sizing: border-box;"
                    v-for="(item, index) in noNumTiemSchList" :key="item.end_time">
                    <view class="top">
                      <text>{{ item.start_time }} - {{ item.end_time }}</text>
                      <text style="font-size: 12px; padding-right: 4px;">剩余：{{ item.src_num }}</text>
                    </view>
                    <view class="bottom">
                      <text>{{ detail.dep_name }} 【{{ registerName }}】</text>
                      <view class="doctor-appoint" v-if="item.src_num > 0">已满</view>
                    </view>
                  </view>
                </view>
              </uni-collapse-item>
            </uni-collapse>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDoctorDetail, getSpecialDoctorDetail } from '@/api/waiting'
import headerBAr from '@/pages/component/header_bar/header_bar.vue'
import { reqGetDocDetailAndDay, reqGetDocTimeSch } from '../../api/wechat'
import flodText from '@/components/flod-text/flod-text.vue'
import dayjs from 'dayjs'
import { getAvatarUrl } from '../../utils/utils'

export default {
  components: {
    headerBAr,
    flodText
  },
  data() {
    return {
      detail: {},
      schlist: {},
      keys: {},
      gridInd: -1,
      activeSchId: '',
      activeSch: {},
      timeSchList: [],
      noNumTiemSchList: [],
      collapseNoNum: '',
      registerName: ''
    }
  },
  methods: {
    drawer(sch) {
      if (this.activeSchId == sch.schedule_id || sch.src_num <= 0) {
        return
      } else {
        this.activeSchId = sch.schedule_id
        this.activeSch = sch
        this.registerName = sch.register_name
        this.getDocTimeSch()
      }
    },
    toAppoint(li) {
      console.log('toAppoint', li)
      if (li.src_num > 0) {
        let queryObj = {
          hos_name: this.detail.hos_name,
          doc_name: this.detail.doc_name,
          appointment_notice: this.detail.appointment_notice,
          dep_name: this.detail.dep_name,
          level_name: this.detail.level_name,
          hospital_rule: this.detail.hospital_rule,
          time_type_desc: this.activeSch.time_type_desc,
          // schedule_id: this.activeSch.schedule_id,
          sch_date: this.activeSch.sch_date,
          time_type: this.activeSch.time_type,
          queue_sn: li.queue_sn,
          start_time: li.start_time,
          end_time: li.end_time,
          schedule_id: li.schedule_id,
          hos_id: this.keys.hos_id,
          dep_id: this.keys.dep_id,
          doc_id: this.keys.doc_id,
          hos_code: this.keys.hos_code,
        }
        uni.setStorageSync('appointDetail', queryObj)
        uni.navigateTo({
          url: '/pages/register/appointDetail'
        })
      }
    },
    getWeek(date) {
      let datas = dayjs(date).day();
      let week = ['日', '一', '二', '三', '四', '五', '六'];
      return '星期' + week[datas];
    },
    // 获取医生详情和天数排班信息
    getDocDetailAndDay() {
      let query = {
        doc_code: this.keys.doc_id,
        dep_code: this.keys.dep_id,
        hos_code: this.keys.hos_code,
      }
      reqGetDocDetailAndDay(query).then((res) => {
        if (res.data.code != 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
            duration: 2000,
          })
          return
        }
        this.detail = res.data.data
        this.detail.doc_avatar = getAvatarUrl(this.detail.doc_avatar)
        this.schlist = this.detail.schedule.map((item) => {
          item.sch_date_short = item.sch_date.split('-')[1] + '-' + item.sch_date.split('-')[2]
          item.weekText = this.getWeek(item.sch_date)
          return item
        })
        if (this.detail.schedule.length > 0) {
          let canSch = this.schlist.find(item => item.src_num > 0)
          if (canSch) {
            this.activeSchId = canSch.schedule_id
            this.activeSch = canSch
            this.registerName = canSch.register_name
            this.getDocTimeSch()
          }
        }
      })
    },
    // 获取时段排班信息
    getDocTimeSch() {
      if(this.activeSchId == '') {
        return
      }
      reqGetDocTimeSch({
        schedule_id: this.activeSchId,
      }).then((res) => {
        if (res.data.code != 1) {
          this.timeSchList = []
          this.noNumTiemSchList = []
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
            duration: 2000,
          })
          return
        }
        console.log('getDocTimeSch', res)
        this.timeSchList = res.data.data.filter(item => item.src_num > 0)
        this.noNumTiemSchList = res.data.data.filter(item => item.src_num <= 0)
        console.log('timeSchList', this.timeSchList)
        console.log('noNumTiemSchList', this.noNumTiemSchList)
      })
    },
    // 展开收起
    noNumShowFun() {
      this.noNumShow = !this.noNumShow
    }
  },
  onLoad: function (option) {
    this.keys = option
    // #ifdef H5
    console.log('H5')
    this.getDocDetailAndDay()
    // #endif
  },
  onShow() {
    console.log('onShow')
    this.getDocTimeSch()
  },
  onBackPress() {
    // #ifdef APP-PLUS
    plus.key.hideSoftKeybord()
    // #endif
  },
}
</script>
<style lang="scss" scoped>
.detail-container {
  overflow: scroll;
  min-height: 100vh;
  background-color: rgb(241, 241, 241);
}

.doctor-detail {
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.doctor-detail .media-box {
  padding: 5px 15px;
  background-color: #fbf9fe;
}

.media-box_appmsg {
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.doctor_img {
  margin-right: 0.8em;
  width: 45px;
  height: 68px;
  line-height: 68px;
  text-align: center;
  overflow: hidden;
}

.doctor_img .img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.doctor_info {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  min-width: 0;
}

.doctor_info_name {
  display: flex;
  -webkit-display: flex;
  align-items: flex-end;
  -webkit-align-items: flex-end;
  margin-bottom: 8px;
  font-weight: 400;
  font-size: 17px;
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
  word-wrap: break-word;
  word-break: break-all;
}

.sub_title {
  font-size: 12px;
  margin-left: 0.2em;
  color: #999;
}

.doctor_info_lebal {
  color: rgb(238, 102, 138);
  font-size: 16px;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.sch-container {
  padding: 0 15px;
  margin-top: 8px;
  padding-bottom: 40px;

  .sch-box {
    padding: 8px 12px;
    background-color: #ffffff;
    border-radius: 8px;
   
    .sch-list-box{
      display: flex;
      align-items: center;
      overflow-x: scroll;
      gap: 8px;
    }

    .sch-item {
      height: 70px;
      width: 50px;
      flex-shrink: 0;
      padding: 0 10px;
      border-radius: 10px;
      background-color: #ffffff;
      color: #333;
      font-size: 14px;

      >view {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
      }

      .state {
        color: rgb(238, 102, 138);
      }
    }

    .disabled {
      color: #999;

      .state {
        color: #999;
      }
    }

    .active {
      background-color: rgb(238, 102, 138);
      color: #ffffff;

      .state {
        color: #ffffff;
      }
    }
  }

  .select-sch-time {
    margin: 8px 0;
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 8px 12px;
    height: 22px;
    display: flex;

    color: #333;
    align-items: center;
  }

  .time-sch-box {
    background-color: #ffffff;
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;

    .no-num-tip {
      display: flex;
      align-items: center;
      justify-content: center;

      padding: 10px 15px;
      color: #666;
      font-size: 15px;

      .iconfont {
        margin-top: 4px;
      }
    }

    .time-sch-item {
      height: 58px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      padding: 0 16px;
      background-color: #ffffff;

      .top,
      .bottom {
        width: 100%;
        display: flex;
        color: #666;
        align-items: center;
        justify-content: space-between;
      }

      .bottom {
        font-size: 14px;

        .doctor-appoint {
          padding: 3px 10px;
          background-color: #ee668a;
          margin-right: 3px;
          border-radius: 5px;
          display: inline-block;
          min-width: 26px;
          color: #ffffff;
          line-height: 1.2;
          text-align: center;
          font-size: 12px;
          vertical-align: middle;
        }
      }
    }

    .time-sch-item-disabled {

      .top,
      .bottom {
        color: #BBB;
      }

      .bottom {
        .doctor-appoint {
          background-color: #D7D7D7;
          color: #FFF;
        }
      }
    }

    .time-sch-item+.time-sch-item {
      position: relative;

      &::before {
        content: '';
        position: absolute;
        display: block;
        width: 100%;
        height: 1px;
        top: 0;
        left: 0;
        background-color: #e5e5e5;
        transform: scaleY(0.5);
      }
    }
  }
}
</style>
