<template>
    <view class="edit-info-page">
        <headerBAr title="修改信息"></headerBAr>
      <view class="content-box">
        <view class="form-box">
          <uni-forms ref="baseForm" :modelValue="patientDetaile">
            <uni-forms-item label="姓名">
              <view class="pinfotxt">{{ patientDetaile.patient_name || '' }}</view>
            </uni-forms-item>
            <uni-forms-item label="证件类型" name="idcard_type">
              <uni-data-picker placeholder="请选择证件类型" popup-title="请选择证件类型" :localdata="idcard_info"
                v-model="patientDetaile.idcard_type"
                ></uni-data-picker>
            </uni-forms-item>
  
            <uni-forms-item label="证件号" name="patient_idcard">
              <uni-easyinput inputBorder v-model="patientDetaile.patient_idcard"
                placeholder="请输入证件号码" />
            </uni-forms-item>
            <uni-forms-item label="手机号">
              <view class="pinfotxt">
                {{ patientDetaile.patient_phone.substr(0, 3) }}******{{ patientDetaile.patient_phone.substr(9) }}
                </view>
            </uni-forms-item>
            <button type="primary" class="my-button" @click="submit"> 确定 </button>
            <button class="close-btn" @click="goBack()"> 关闭 </button>
          </uni-forms>
        </view>
      </view>
      <view class="footer"></view>
    </view>
  </template>
  
  <script>
  import { getMyPatientDetail } from '@/api/waiting.js'
  import { upPatientInfo } from '@/api/ucenter.js'
  import { nationList } from './nation.js'
  import { uniForms, uniFormsItem, uniEasyinput } from '@dcloudio/uni-ui'
  import headerBAr from '@/pages/component/header_bar/header_bar.vue'

  export default {
    components: { uniForms, uniFormsItem, uniEasyinput, headerBAr },
    data() {
      return {
        pat_id: '',
        patientDetaile: {
          idcard_type: '',
          patient_idcard: '',
          patient_phone: '',
        },
        size: 180,
        idcard_info: [
          { value: 0, text: '身份证' },
          { value: 1, text: '出生证' },
        ],
        sexs: [
            { text: '男', value: 1 },
            { text: '女', value: 2 },
        ],
        nation_info: nationList,
        
      }
    },
    onLoad: function (option) {
      this.pat_id = option.id
      this.getDetail(option.id)
    },
    methods: {
      submit(ref) {
        console.log(this.patientDetaile.idcard_type)
        if (this.patientDetaile.idcard_type !== 0 && !this.patientDetaile.idcard_type) {
          uni.showToast({
            icon: 'none',
            title: `请选择证件类型`,
          })
          return
        }
        if (this.patientDetaile.patient_idcard.length < 5) {
          uni.showToast({
            icon: 'none',
            title: `证件号格式错误`,
          })
          return
        }
        if (this.patientDetaile.idcard_type == 0 && this.patientDetaile.patient_idcard.length != 18) {
          uni.showToast({
            icon: 'none',
            title: `身份证为18位`,
          })
          return
        }
    
        this.updatePat()
      },
      updatePat(){
        upPatientInfo({
          id: this.patientDetaile.id,
          idcard_type: this.patientDetaile.idcard_type,
          patient_idcard: this.patientDetaile.patient_idcard,
        }).then((res) => {
          let rt = res.data
          if (rt.code == 1) {
            uni.navigateBack({ delta: 1 })
          } else {
            uni.showToast({
              icon: 'none',
              title: rt.msg,
              duration: 2000,
            })
          }
        })
      },
      async getDetail(pat_id) {
        const userInfo = uni.getStorageSync('userInfo')
  
        const { statusCode, data } = await getMyPatientDetail(null, {
          patientid: pat_id,
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '暂无数据',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        this.patientDetaile = data.data
      },
      goBack() {
        history.back(-1)
      },
    },
  }
  </script>
  
  <style lang="scss" scoped>
  .uni-stat__select {
    padding: 0 !important;
  }
  
  .edit-info-page {
    background-color: #fff;
  
    .content-box {
     padding-top: 40px;
      .form-box {
        padding: 15px;
        background-color: #fff;
  
  
        .qr-code-box{
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 auto;
          margin-bottom: 20px;
          width: 100%;
  
          .qr-code {
            width:120px;
            height:120px;
            border: 1px solid #000;
  
            >image{
              width: 100%;
              height: 100%;
            }
          }
        }
  
        .pinfotxt {
          display: flex;
          flex-shrink: 0;
          box-sizing: border-box;
          flex-direction: row;
          align-items: center;
          width: 65px;
          padding: 5px 0;
          height: 36px;
        }
      }
  
      .my-button {
        color: #fff;
        background-color: rgb(238, 102, 138);
      }
  
      .disabled-btn{
        color: rgba(255, 255, 255, 0.6);
      }
  
      .close-btn {
        margin: 20px 0 0;
      }
  
      .has-card-box{
        color: #007aff;
        font-size: 16px;
        margin: 0 15px;
        text-align: center;
        padding: 10px;
      }
    }
  
  }
  </style>
  