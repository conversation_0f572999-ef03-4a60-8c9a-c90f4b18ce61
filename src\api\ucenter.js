import { get, post } from '@/utils/request'

// 为了获取健康卡完善就诊人信息
const upPatientInfo = (data, params) => post(`/patient/update`, data, { showLoading: true })
// 绑定就诊人
const bindPatient = (data,params) => post(`/patient/bind`, data, { showLoading: true })
// 新建就诊人
const createPatient = (data,params) => post(`/patient/create`,data, { showLoading: true })
// 门诊待缴费
const unPayListMz = (data,params) => get(`/mz/unpayList?userid=${params.userid}&openid=${params.openid}`,data, { showLoading: true })
// 创建门诊缴费订单 UI需要画
const createMzOrder = (data,params) => post(`/mz/createMzOrder`,data,{showLoading:true})
// 创建支付宝医保支付订单
const createMzMiOrderAli = (data,params) => post(`/mz/createMzMiOrderAli`,data,{showLoading:true})
// 创建门诊缴费订单成功就去默联下单
const getMzPay = (data,params) => get(`/mz/mz_pay_ali?userid=${params.userid}&openid=${params.openid}&order_id=${params.order_id}`,data,{showLoading:true})
const getMzPayQuery = (data,params) => get(`/mz/mz_pay_ali_query?userid=${params.userid}&openid=${params.openid}&order_id=${params.order_id}`,data,{showLoading:true})

// 门诊已缴费
const payedListMz = (data,params) => get(`/mz/payedOrderList?userid=${params.userid}&openid=${params.openid}`,data, { showLoading: true })
// 门诊缴费详情
const payedDetailMz = (data,params) => get(`/mz/payedDetail?id=${params.id}&userid=${params.userid}&openid=${params.openid}`,data, { showLoading: true })
// 查看门诊处方信息
const cfDetailMz = (data,params) => get(`/mz/cfDetail?id=${params.id}&userid=${params.userid}&openid=${params.openid}`,data, { showLoading: true })
// 候诊查询
const waitListMz = (data,params) => get(`/mz/waitList?userid=${params.userid}&openid=${params.openid}`,data, { showLoading: true })
// 住院列表
const getlistZy = (data,params) => get(`/zy/zylist?pat_id=${params.pat_id}&userid=${params.userid}&openid=${params.openid}`,data, { showLoading: true })
// 住院详情
const getDetailZy = (data,params) => get(`/zy/detail?pat_id=${params.pat_id}&admisstimes=${params.admisstimes}&inpatientno=${params.inpatientno}&userid=${params.userid}&openid=${params.openid}`,data, { showLoading: true })
// 住院费用详细
const getFeeDetailZy = (data,params) => get(`/zy/getDailyFee?pat_id=${params.pat_id}&admisstimes=${params.admisstimes}&inpatientno=${params.inpatientno}&userid=${params.userid}&openid=${params.openid}`,data, { showLoading: true })
// 住院创建充值订单
const reqCreateOrderZy = (data,params) => post(`/zy/createOrder`,data, { showLoading: true })
// 住院充值已缴费明细
const getChargePayDetail = (data,params) => get(`/zy/payedDetail`,data, { showLoading: true })

export {upPatientInfo,bindPatient,createPatient,unPayListMz,
    payedListMz,payedDetailMz,cfDetailMz,waitListMz,getlistZy,getDetailZy,getFeeDetailZy,createMzOrder, createMzMiOrderAli,getMzPay,getMzPayQuery, getChargePayDetail, reqCreateOrderZy}
