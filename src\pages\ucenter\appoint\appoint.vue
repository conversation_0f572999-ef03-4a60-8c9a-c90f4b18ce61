<template>
  <view class="appoint-page">
    <headerBAr title="个人中心"></headerBAr>
    <view class="head-nav">
      <view :class="navIndex == 0 ? 'activite' : ''" @click="checkIndex(0)">全部</view>
      <view :class="navIndex == 1 ? 'activite' : ''" @click="checkIndex(1)">已预约</view>
      <view :class="navIndex == 2 ? 'activite' : ''" @click="checkIndex(2)">已取消</view>
    </view>
    <view class="content">
      <z-paging
        ref="paging"
        v-model="appointlist"
        @query="getList"
        :paging-style="{ top: '76px' }"
      >
      <view class="list-box">
        <view
          class="appoint-item"
          v-for="(item, index) in appointlist"
          :key="index"
          @click="toDetail(index, item.order_id)"
        >
          <view class="appoint-item-top">
            <view>{{ new Array(item.patient_name.length).join('*') + item.patient_name.substr(-1) }}</view>
            <view class="appoint-item_badge">{{ item.appoint_status_desc }}</view>
          </view>
          <view class="appoint-item-cent">
            <view class="appoint-item-cent-item">
              <view>医生</view>
              <view>{{ item.doc_name }}</view>
            </view>
            <view class="appoint-item-cent-item">
              <view>就诊院区</view>
              <view>{{ item.hos_name }}</view>
            </view>
            <view class="appoint-item-cent-item">
              <view>就诊科室</view>
              <view>{{ item.dep_name }}</view>
            </view>
            <view class="appoint-item-cent-item">
              <view>就诊日期</view>
              <view>{{ item.sch_date }}</view>
            </view>
            <view class="appoint-item-cent-item">
              <view>就诊时间</view>
              <view>{{ item.start_time }} - {{ item.end_time }}</view>
            </view>
            <view class="appoint-item-cent-item">
              <view>预约时间</view>
              <view>{{ item.appoint_time }}</view>
            </view>
          </view>
        </view>
      </view>
      </z-paging>
    </view>

    <view>
      <uni-popup ref="popup" type="message">
        <uni-popup-message
          :type="popMsgType"
          :message="popMsgText"
          :duration="2000"
        ></uni-popup-message>
      </uni-popup>
    </view>
  </view>
</template>

<script>
  import { getRecordList,getMypatientList } from '@/api/waiting.js'
  import headerBAr from '@/pages/component/header_bar/header_bar.vue'
  import { reportCmPV } from '@/to/cloudMonitorHelper';

  export default {
    components: {
      headerBAr,
    },
    data() {
      return {
        navIndex: 0,
        popMsgType: 'error',
        popMsgText: '',
        appointlist: [],
        patinetList: [],
      }
    },
    onLoad: function (option) {
      reportCmPV({ title: '挂号记录查询',option });
    },
    methods: {
      async mylist() {
        // type 0 全部  1 已预约  2 已取消
        //  console.log(pageNo, pageSize)
        /* uni.setStorageSync('userInfo', {
          id: 3,
          openid: 'o9U-Ft4X8xUVM3Sg9WqJX6H-qZhk',
          userid: 3,
        }) */
        const userInfo = uni.getStorageSync('userInfo')
        const { statusCode, data } = await getMypatientList(null, {
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '暂无数据1',
            icon: 'none',
          })
          return
        }
        if (!data.data.length) {
          uni.showModal({
            title: '提示',
            content: '请选择就诊人，如还没有就诊卡请新建卡，如已经有就诊卡请绑定就诊卡',
            cancelText: '去绑定卡',
            confirmText: '去新建卡',
            success: function (res) {
              if (res.confirm) {
                //console.log('用户点击去新建卡')
                uni.navigateTo({
                  url: '/pages/ucenter/patient/add',
                })
              } else if (res.cancel) {
                //console.log('用户点击去绑定卡')
                uni.navigateTo({
                  url: '/pages/ucenter/patient/bind',
                })
              }
            },
          })
          return
        }
        this.patinetList = data.data
        this.getList()
      },
      async getList(pageNo, pageSize){
        if(this.patinetList.length == 0){
          this.mylist()
        }else{
          const userInfo = uni.getStorageSync('userInfo')
          const { statusCode, data } = await getRecordList(null, {
            page: pageNo,
            type: this.navIndex,
            userid: userInfo.user_id,
            openid: userInfo.openid,
          })
          if (statusCode != 200) {
            uni.showToast({
              title: '暂无数据',
              duration: 2000,
              icon: 'none',
            })
            return
          }
          this.$refs.paging.complete(data.data)
          // this.appointlist = data.data
          // console.log(data.data)
        }
      },
      checkIndex(index) {
        this.navIndex = index
        this.$refs.paging.reload()
        // this.getList(index)
      },
      toDetail(panel, id) {
        const url = '/pages/ucenter/appoint/detail?id=' + id
        if (this.hasLeftWin) {
          uni.reLaunch({
            url: url,
          })
        } else {
          uni.navigateTo({
            url: url,
            success(res) {
            },
            fail(err) {
            },
          })
        }
      },
    },
  }
</script>

<style scoped lang="scss">
.appoint-page{

  .head-nav {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    z-index: 500;
    position: absolute;
    top: 0;
    height: 36px;
    width: 100%;
    background-color: #fafafa;
  }
  .head-nav > view {
    position: relative;
    display: block;
    flex: 1;
    padding: 7px 0;
    text-align: center;
    font-size: 15px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    border-right: 1px solid #cccccc;
    color: #888;
  }
  .head-nav > view:last-child {
    border-right: none;
  }

  .head-nav > view.activite {
    background-color: white;
    border-bottom: 2px solid rgb(238, 102, 138);
    color: rgb(238, 102, 138);
  }

  .content {
    height: 100%;

    .list-box{
      padding: 12px 15px 0;
      background-color: #fbf9fe;
    }
  }

  .appoint-item {
    border-radius: 10px;
    border: 1px solid #e5e5e5;
    background-color: #ffffff;
    padding: 10px 15px;
  }

  .appoint-item+.appoint-item {
    margin-top: 10px;
  }
  .appoint-item-top,
  .appoint-item-cent-item {
    display: flex;
    display: -webkit-flex;
    -webkit-align-items: center;
    justify-content: space-between;
    line-height: 2.5em;
    font-size: 1.125em;
  }
  .appoint-item_badge {
    padding: 0.15em 0.4em;
    min-width: 8px;
    background-color: #ee668a;
    border-radius: 5px;
    color: #ffffff;
    line-height: 1.2;
    text-align: center;
    font-size: 12px;
  }
  .appoint-item-top {
    border-bottom: 1px solid #e5e5e5;
  }
}
</style>
