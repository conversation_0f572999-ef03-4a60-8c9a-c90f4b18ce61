<template>
  <view>
    <!-- <view class="container"> -->
      <view class="topbar">
        <view class="topbar-left" @click.stop="toappoint">
          <uni-icons type="home-filled" size="30"></uni-icons>
        </view>
        <view class="topbar_title">{{ title }}</view>
        <view class="topbar-right" @click.stop="toucenter">
          <uni-icons type="person" size="30"></uni-icons>
        </view>
      </view>
    <!-- </view> -->
  </view>
</template>

<script>
  export default {
    props: {
      title: {
        type: String,
        default: '',
      },
    },
    methods: {
      toappoint() {
        uni.switchTab({
          url: '/pages/index/index',
          animationType: 'slide-in-bottom',
          animationDuration: 200,
        })
      },
      toucenter() {
        uni.switchTab({
          url: '/pages/ucenter/index',
          animationType: 'slide-in-bottom',
          animationDuration: 200,
        })
      },
    },
  }
</script>
<style scoped>
  .topbar {
    left: 0;
    z-index: 3;
    top: 0;
    right: 0;
    position: fixed;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    border-bottom: 1px solid #efefef;
    background-color: rgba(255, 255, 255, 1);
    line-height: 40px;
  }
  .topbar_title {
    overflow: hidden;
    margin: auto;
    text-align: center;
    font-size: 18px;
    color: rgb(238, 102, 138);
    font-weight: bold;
  }
</style>
