<template>
    <view class="flod-text">
        <view class="text-box" id="textBox" :style="{ maxHeight: isExpand ? '999em' : '3.6em' }">
            <view class="btn" :class="isExpand ? 'expand' : ''" @click="expandOrShrink" v-if="showExpand">
            {{ isExpand ? "收起" : "查看全部" }}
            <uni-icons :type="isExpand ? 'top' : 'bottom'" color="#58BDE8" size="16"></uni-icons>
            </view>
            {{ text }}
        </view>
    </view>
</template>
<script>
export default {
    props: {
        text: {
            //文字
            type: String,
            default: "",
        },
    },
    data() {
        return {
            isExpand: false, //是否展开
            showExpand: false, //是否显示展开收起按钮
        };
    },
    watch: {
        text() {
            // 有数据且页面渲染完成
            this.$nextTick(async () => {
                this.test()
            });
        },
    },
    mounted() {
        // 若text数据一开始有值，则监听触发不了，所以在此进行处理
        if (this.text) {
            uni.getSystemInfo().then((res) => {
                // 获取屏幕宽度
                this.windowWidth = res[1].windowWidth;
                // this.judgeExpand(this.lineCount, this.fontSize);
                this.test()
            });
        }
    },
    methods: {
        // 点击展开/收起按钮
        expandOrShrink() {
            this.isExpand = !this.isExpand;
        },
        test() {
            let elmnt = document.getElementById("textBox");
            let scollHeght = elmnt.scrollHeight;
            let clientHeight = elmnt.clientHeight;
            console.log("scrollHeight", scollHeght);
            console.log("clientHeight", clientHeight);
            // 计算是否需要展开收起按钮
            // 这里加5是为了避免在某些机型上出现的误差
            if(scollHeght > (clientHeight + 5)) {
                this.showExpand = true;
            } else {
                this.showExpand = false;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.flod-text{
    display: flex;
}
.text-box {
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: justify;
    line-height: 1.2;
    max-height: 3.6em;
    position: relative;
    color: rgb(238, 102, 138);

    &::before {
        content: '';
        height: calc(100% - 18px);
        float: right;
        width: 0px;
    }
}

.btn {
    position: relative;
  float: right;
  clear: both;
  margin-left: 20px;
  font-size: 16px;
  padding: 0 0 0 8px;
  line-height: 18px;
  border-radius: 4px;
  color: #58BDE8;
  cursor: pointer;
  border:0;
}

.btn::before{
  content: '...';
  position: absolute;
  left: -5px;
  color: rgb(238, 102, 138);;
  transform: translateX(-100%)
}

.expand{
    &::before{
       content: '';
    }
}
</style>