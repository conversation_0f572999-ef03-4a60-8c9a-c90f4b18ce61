<template>
    <view class="wait-list-page">
        <headerBAr title="排队查询"></headerBAr>
        <view class="waitList-list" v-if="waitList.length > 0">
            <view class="wait-item" v-for="(item, index) in waitList" :key="index">
                <view class="wait-item-hd">
                    <view class="wait-item-info">
                        <text class="info-label">就诊人</text>
                        <text class="info-value" style="color: rgb(238,102,138);">{{ item.patient_name }}</text>
                    </view>
                </view>
                <view class="wait-item-bd">
                    <view class="wait-item-info">
                        <text class="info-label">就诊时间</text>
                        <text class="info-value">{{ item.see_doctor_date }}&nbsp;{{ item.see_doctor_time_type
                            }}&nbsp;</text>
                    </view>
                    <view class="wait-item-info">
                        <text class="info-label">就诊科室</text>
                        <text class="info-value">{{ item.depart_name }}</text>
                    </view>
                    <view class="wait-item-info">
                        <text class="info-label">就诊医生</text>
                        <text class="info-value">{{ item.doctor_name }}</text>
                    </view>
                </view>
                <view class="wait-item-bd">
                    <view class="wait-item-info">
                        <text class="info-label">就诊号</text>
                        <text class="info-value">{{ item.my_see_no }}</text>
                    </view>
                    <view class="wait-item-info">
                        <text class="info-label">前面候诊</text>
                        <text class="info-value">{{ item.now_see_num }}人</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="no-data" v-else>
            <view class="no-data-content">
                <text class="no-data-content-tips">没有找到相关排队记录</text>
            </view>
        </view>
    </view>
</template>

<script>
import headerBAr from '@/pages/component/header_bar/header_bar.vue'
import { waitListMz } from '@/api/ucenter'
export default {
    components: {
        headerBAr,
    },
    data() {
        return {
            waitList: [],
        }
    },
    methods: {
        getWaitList() {
            const userInfo = uni.getStorageSync('userInfo')
            waitListMz(null, {
                userid: userInfo.user_id,
                openid: userInfo.openid,
            }).then((res) => {
                console.log(res)
                if (res.data.code !== 1) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000,
                    })
                    return
                }
                this.waitList = res.data.data
            })
        },
    },
    onLoad() {
        this.getWaitList()
    },
}
</script>

<style lang="scss" scoped>
.wait-list-page {
    background-color: #fbf9fe;
    min-height: 100vh;

    .waitList-list {
        padding: 50px 16px;

        .wait-item {
            position: relative;
            border-radius: 10px;
            background-color: #FFFFFF;
        }

        .wait-item + .wait-item {
            margin-top: 10px;
        }

        .wait-item-hd {
            position: relative;
            padding: 10px 15px;
            text-align: right;
            line-height: 2.5em;
        }

        .wait-item-hd:after {
            content: " ";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
            left: 15px;
            right: 15px;
        }

        .wait-item-hd .info-value {
            font-style: normal;
            font-size: 1.6em;
        }

        .wait-item-bd {
            padding: 10px 15px;
            font-size: .9em;
            text-align: right;
            color: #999999;
            line-height: 2;
        }

        .wait-item-info {
            overflow: hidden;
        }

        .info-label {
            float: left;
            margin-right: 1em;
            /* min-width: 4em; */
            color: #999999;
            text-align: justify;
            text-align-last: justify;
        }

        .info-value {
            display: block;
            overflow: hidden;
            word-break: normal;
            word-wrap: break-word;
        }
    }

    .no-data {
        padding-top: 60px;

        &-content {
            border-top: 1px solid #E5E5E5;
            width: 65%;
            margin: 1.5em auto;
            line-height: 1.6em;
            font-size: 14px;
            text-align: center;

            &-tips {
                position: relative;
                top: -0.9em;
                padding: 0 .55em;
                background-color: #FFFFFF;
                color: #999999;
                display: inline-block;
                vertical-align: middle;
            }
        }

    }
}
</style>