<template>
  <view>
    <view class="container">
      <view class="title"> 门诊信息 </view>
      <view class="appoint_list">
        <view class="appoint_list-info">
          <view class="appoint_list-cent">
            <view class="appoint_list-cent-item">
              <view class="patient_info">就诊医院</view>
              <view class="patient_infoText">{{ appdata.hos_name }}</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">就诊科室</view>
              <view class="patient_infoText">{{ appdata.dep_name }}</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">医生姓名</view>
              <view class="patient_infoText">{{ appdata.doc_name }}-{{ appdata.level_name }}</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">就诊日期</view>
              <view class="patient_infoText"
                >{{ appdata.sch_date }}{{ appdata.time_type_desc ? '&nbsp;&nbsp;' : ''}}{{ appdata.time_type_desc }}</view
              >
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">就诊时间</view>
              <view class="patient_infoText">{{ appdata.start_time }}~{{ appdata.end_time }}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="title"> 就诊信息 </view>
      <view class="appoint_list">
        <view class="appoint_list-info">
          <view class="appoint_list-cent">
            <view class="appoint_list-cent-item">
              <view class="patient_info">就诊人</view>
              <picker
                @change="bindPickerChange"
                :value="pickerIndex"
                :range="pickerArray"
                range-key="patient_name"
              >
                <view class="uni-input">{{ pickerArray[pickerIndex].id == '-1' ? '请选择就诊人' : pickerArray[pickerIndex].patient_name }}</view>
              </picker>
              <!-- <view class="patient_infoText">{{appdata.doc_name}}</view> -->
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">患者ID号</view>
              <view v-if="pickerArray[pickerIndex].id != '-1'" class="patient_infoText">{{ pickerArray[pickerIndex].jz_card }}</view>
              <view v-else class="add-patient-btn" @click="addPatient">添加就诊人</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">电子健康卡号</view>
              <view class="patient_infoText">{{ pickerArray[pickerIndex].tx_health_card }}</view>
            </view>
            <!-- <view v-if="appdata.hospital_rule" style="display: flex; flex-direction: column; align-items: flex-start;" class="appoint_list-cent-item">
              <view class="patient_info" >预约规则</view>
              <view class="patient_infoText" style="text-align: left; color:rgb(238,102,138);"
               v-html="appdata.hospital_rule">
              </view>
            </view> -->
          </view>
        </view>
      </view>
      <view class="uni-padding-wrap uni-common-mt">
        <button type="primary" @click="submit_appoint" :disabled="clickM">确定预约</button>
      </view>
      <!-- #ifdef MP-ALIPAY -->
      <view class="aliText" @click="alishowd(true)">
        <image class="aliball" src="/static/ali/ball.png" />
        <view>预约挂号预计得蚂蚁森林能量</view>
        <view class="alig">277g</view>
        <uni-icons type="info" color="#999" size="18"></uni-icons>
      </view>
      <view class="alibag" v-show="alishow">
        <view class="alialert">
          <image class="alertpng" src="/static/ali/alert.png" />
          <view class="alititel">预约挂号、查报告得绿色能量</view>
          <view class="aliul">
            <view class="alili">完成预约挂号可获得蚂蚁森林绿色能量277g，每月上限5次（取消挂号失效）</view>
            <view class="alili">完成报告查询均可获得2g能量，每月上限10次</view>
            <view class="alili">得到的绿色能量可以前往【蚂蚁森林】用来种树，改善我们的环境</view>
          </view>
          <view class="konw" @click="alishowd(false)">知道了</view>
        </view>
      </view>
      <!-- #endif -->
    </view>

    <uni-popup ref="tipPopup" type="center" :animation="false">
			<view class="popup-content">
				<view class="popup-title">温馨提示</view>
				<view class="popup-scroll">
					<view class="popup-text">
						<view v-html="appdata.appointment_notice"></view>
					</view>
				</view>
				<view class="popup-btns">
					<view class="popup-btn" @click="closeTipPopup">取消</view>
					<view class="popup-btn primary" @click="closeTipPopup">确认</view>
				</view>
			</view>
		</uni-popup>
  </view>
</template>

<script>
  import { getMyList,getMypatientList, AppointDb,refreshToken, SubmitAppoint } from '@/api/waiting'
  import {reqSubmitAppoint} from '@/api/wechat.js'
  import dayjs from 'dayjs'
  export default {
    data() {
      return {
        pickerIndex: 0,
        pickerUser: {},
        pickerArray: [{ patient_name: '就诊人' }],
        navIndex: 0,
        appdata: {},
        alishow: false,
        clickM: false
      }
    },
    onShow: function () {
      console.log('onShow')
      this.clickM = false
      this.getPatientList()
    },
    onLoad: function (option) {
      uni.removeStorageSync('patientInfo')
      console.log('onLoad')
      // uni.getStorageSync('appointDetail')
      this.appdata = uni.getStorageSync('appointDetail')
      console.log(this.appdata)
      // if(this.appdata.appointment_notice){
      //   this.$nextTick(()=>{
      //     this.$refs.tipPopup.open()
      //   })
      // }

      // #ifdef H5
      // H5的设计中不用区分上下午，默认写死为全天
      this.appdata.time_type = 7
      this.appdata.time_type_desc = ''
      // #endif
    },
    onUnload: function () {
      console.log('onUnload')
      uni.removeStorageSync('appointDetail')
      uni.removeStorageSync('patientInfo')
    },
    methods: {
      getPatientList() {
       
        let query = {
          userid: uni.getStorageSync('userInfo').user_id,
          openid: uni.getStorageSync('userInfo').openid,
        }
    
        getMypatientList(null,query).then((res) => {
          // #ifdef MP-ALIPAY
          for (let pos = res.data.data.length - 1; pos >= 0; pos--) {
            res.data.data[pos].patient_name = new Array(res.data.data[pos].patient_name.length).join('*') + res.data.data[pos].patient_name.substr(-1)
          }
          // #endif
          this.pickerArray = res.data.data

          
          if(uni.getStorageSync('patientInfo')){
            this.pickerUser = this.pickerArray.find((item)=>{
              return item.id == uni.getStorageSync('patientInfo').id
            })
            this.pickerIndex = this.pickerArray.findIndex((item)=>{
              return item.id == uni.getStorageSync('patientInfo').id
            })
            uni.removeStorageSync('patientInfo')
          }else{
            this.pickerUser = this.pickerArray[0]
            this.pickerIndex = 0
          }
          this.pickerArray.push({ patient_name: '添加就诊人', id:'-1' })
        }).catch(err => {
          uni.showModal({
            title: '错误',
            content: JSON.stringify(err),
          })
        })
      },
      closeTipPopup() {
        this.$refs.tipPopup.close()
      },
      submit_appoint() {
        this.clickM = true
        let userInfo = uni.getStorageSync('userInfo')
        
        // 提交后清除选中的就诊人信息
        uni.removeStorageSync('patientInfo') 
        let that = this
        // #ifdef MP-ALIPAY
        my.getAuthCode({
          scopes: ['mfrstre'], 
          success: (res) => { 
            console.log(res)
            if (res.authCode) {
              uni.showLoading({
                title: '加载中'
              });    
              let data = {
                grant_type: "authorization_code",
                code: res.authCode
              }
              refreshToken(data).then((Tres)=>{
                console.log(Tres)
                if(Tres.data.code == 1){
                  uni.setStorageSync('mfrstreCode', Tres.data.data.access_token)
                  uni.hideLoading();
                  that.pantword()
                }
              })
            }
          },
          fail: res => {
            that.pantword(res)
          }
        });
        // #endif
        // #ifdef H5
        that.pantword()
        // #endif
      },
      pantword(){
        let userInfo = uni.getStorageSync('userInfo')
        const that = this
        // #ifdef MP-ALIPAY
        my.getAuthCode({
          scopes: ['auth_user', 'hospital_order'],
          success: (res) => {
            if (res.authCode) {
              let data = {
                grant_type: "authorization_code",
                code: res.authCode,
                openid: userInfo.openid,
                token: userInfo.access_token
              }
              refreshToken(data).then((Tres)=>{
                if(Tres.data.code == 1){
                console.log(Tres.data.data.access_token)
                uni.setStorageSync('authorizationCode', Tres.data.data.access_token)
                }
              })
            }
          },
        })
        // #endif
        if (!this.pickerUser || !this.pickerUser.id || this.pickerUser.id == '-1') {
          uni.showModal({
            title: '提示',
            content: '请选择就诊人，如还没有就诊卡请新建卡，如已经有就诊卡请绑定就诊卡',
            cancelText: '去绑定卡',
            confirmText: '去新建卡',
            success: function (res) {
              if (res.confirm) {
                //console.log('用户点击去新建卡')
                uni.navigateTo({
                  url: '/pages/ucenter/patient/add?from=detail',
                })
              } else if (res.cancel) {
                //console.log('用户点击去绑定卡')
                uni.navigateTo({
                  url: '/pages/ucenter/patient/bind?from=detail',
                })
              }
            },
          })
        } else {
          let query = {
            pat_id: this.pickerUser.id,
            dep_id: this.appdata.dep_id,
            sch_date: this.appdata.sch_date
          }
          // AppointDb(query).then((res) => { //判断该科室是否已预约
          //   if(res.data.code ==  0){
          //     uni.showToast({
          //       icon:"none",
          //       title: res.data.msg,
          //     });
          //     return;
          //   }
            let date = new Date(this.pickerUser.patient_birthday)
            let time = Date.parse(date)
            // if(time<new Date().getTime()-18 * 365 * 24 * 60 * 60 * 1000){
            //   uni.showToast({
            //     icon:"none",
            //     title: "该就诊人年龄超过18岁了",
            //   });
            //   return;
            // }
           
            //未录入身份信息的判断 （1.>182天的需要强制补全 2，未大于182天的不用）
            if (!this.pickerUser.patient_idcard && (Date.now() - time) / (1000*60*60*24) <= 182) {
              uni.showModal({
                title: '提示',
                content: '您的就诊人未录入身份信息，请补全您的身份证号信息',
                cancelText: '继续挂号',
                confirmText: '去补全',
                success: function (res) {
                  if (res.confirm) {
                    console.log('用户点击去补全')
                    // 记录下当前的就诊人信息，补全成功后直接选中当前信息
                    uni.setStorageSync('patientInfo', that.pickerUser)
                     // #ifdef MP-ALIPAY
                     uni.redirectTo({
                      url: '/pages/ucenter/patient/editinfo?id=' +that.pickerUser.id,
                    })
                    // #endif
                    // #ifdef H5
                    uni.navigateTo({
                      url: '/pages/ucenter/patient/editinfo?id=' +that.pickerUser.id,
                    })
                    // #endif
                  } else if (res.cancel) {
                    // 提交挂号信息
                    that.submit_order()
                  }
                },
              })
            } else if (!this.pickerUser.patient_idcard && (Date.now() - time) / (1000*60*60*24) > 182) {
              uni.showModal({
                title: '提示',
                content: '您的就诊人未录入身份信息，请补全您的身份证号信息',
                cancelText: '关闭',
                confirmText: '去补全',
                success: function (res) {
                  if (res.confirm) {
                    // 记录下当前的就诊人信息，补全成功后直接选中当前信息
                    uni.setStorageSync('patientInfo', that.pickerUser)
                    // #ifdef MP-ALIPAY
                    uni.redirectTo({
                      url: '/pages/ucenter/patient/editinfo?id=' +that.pickerUser.id,
                    })
                    // #endif
                    // #ifdef H5
                    uni.navigateTo({
                      url: '/pages/ucenter/patient/editinfo?id=' +that.pickerUser.id,
                    })
                    // #endif
                  }
                }
              })
            } else {
              // 提交挂号信息
              that.submit_order()
            }
            this.clickM = false
          // })
          
        }
      },
      bindPickerChange: function (e) {
        if(this.pickerIndex !== e.detail.value){
          this.clickM = false
          this.pickerIndex = e.detail.value
          this.pickerUser = this.pickerArray[e.detail.value]
        }
      },
      alishowd(state){
        this.alishow = state
      },
      skipCard(code, depId){
        const cardList = [ // 不需要电子健康卡的科室
            {hos_code:'871002',dep_id:'A000039'},
            {hos_code:'871003',dep_id:'3014'},
            {hos_code:'871002',dep_id:'2302'},
            {hos_code:'871002',dep_id:'A000021'},
            {hos_code:'871003',dep_id:'A000096'},
            {hos_code:'871002',dep_id:'2302'}
        ]
        for (var p in cardList) {
          if(cardList[p].hos_code == code && cardList[p].dep_id == depId){
            return true
          }
        }
        return false
      },
      submit_order () {
        const that = this
        if (this.skipCard(this.appdata.hos_code, this.appdata.dep_id)) {
          this.clickM = false
        } else {
          if (this.pickerUser.tx_health_card === '') {
            var n1 = ''
            // 支付宝小程序使用的是birth_day字段
            // #ifdef MP-ALIPAY
              n1 = new Date(this.pickerUser.birth_day.replace(/-/g,"/"));//输入你想要的年数
            // #endif
            // #ifdef H5
              n1 = new Date(this.pickerUser.patient_birthday.replace(/-/g,"/"));//输入你想要的年数
            // #endif
            var n2 = new Date();//置或显示系统日期
            var n = n2.getTime() - n1.getTime();
            // 如果出山天数大于182天，提示去申请电子健康卡
            if ((n/1000/60/60/24)>182) {
              console.log('大于182天')
              uni.showModal({
                title: '提示',
                content: '该就诊人还没有电子健康卡，请先申领电子健康卡，若无法线上申领的，请去医院患者服务中心进行现场办理！',
                success: function (res) {
                  if (res.confirm) {
                    uni.navigateTo({
                      url: '/pages/ucenter/patient/healthcard?id=' + that.pickerUser.id,
                    })
                  }
                },
              })
              return
            }
          }
        }
        this.clickM = false
        let confirmText = `您确定为【${this.pickerUser.patient_name}】预约挂号吗？如不是请点击取消`
        if (this.appdata.dep_id === 'A000129') {
          confirmText = `尊敬的患者家属：新冠核酸检测仅针对非发热的健康患者，发热患者请到医院进行现场预检分诊后就诊，\n您确定为【${this.pickerUser.patient_name}】预约新冠核酸检测吗？如果不是请点击取消`
        }
        uni.showModal({
          title: '提示',
          content: confirmText,
          success: function(res) {
            if (res.confirm) {
              // 提交预约挂号信息
              try {
                console.log(that.pickerUser)
                const submitParams = {
                  rel_sign: '',
                  name_sign: '',
                  patient_name: that.pickerUser.patient_name,
                  birth: that.pickerUser.patient_birthday,
                  sex: that.pickerUser.patient_sex,
                  dep_name: that.appdata.dep_name,
                  jz_card: that.pickerUser.jz_card,
                  patient_id: that.pickerUser.id,
                  hos_code: that.appdata.hos_code, 
                  dep_id: that.appdata.dep_id,
                  doc_id: that.appdata.doc_id,
                  schedule_id: that.appdata.schedule_id,
                  sch_date: that.appdata.sch_date,
                  time_type: that.appdata.time_type,
                  queue_sn: that.appdata.queue_sn,
                  pat_id: that.pickerUser.id,
                  userid: uni.getStorageSync('userInfo').userid,
                  openid: uni.getStorageSync('userInfo').openid,
                  id_no: that.pickerUser.patient_idcard,
                }

                let reqFuncUrl = null;
                // #ifdef MP-ALIPAY
                reqFuncUrl = SubmitAppoint
                // #endif

                // #ifdef H5
                reqFuncUrl = reqSubmitAppoint
                console.log(that.pickerUser.patient_birthday)
                submitParams.birth = dayjs(that.pickerUser.patient_birthday).format('YYYYMMDD');
                submitParams.birth_day = that.pickerUser.patient_birthday
                submitParams.sex = that.pickerUser.patient_sex == 1 ? "男" : "女";
                // #endif
                console.log('submitParams', submitParams)
                reqFuncUrl(submitParams).then(submitRes => {
                  if (submitRes.data.code == 1) {
                    uni.removeStorageSync('appointDetail')
                    // 跳转至预约挂号详情页
                    uni.redirectTo({
                      url: '/pages/ucenter/appoint/detail?id=' + submitRes.data.data.order_id,
                    })
                  } else {
                    uni.showToast({
                      icon:"none",
                      title: submitRes.data.msg,
                    });
                  }
                })
              } catch (error) {
                console.log('提交预约挂号信息失败', error)
              }
            }
          }
        })
      },
      addPatient(){
        uni.showModal({
          title: '添加就诊人',
          content: '如还没有就诊卡请新建卡，如已经有就诊卡请绑定就诊卡',
          cancelText: '去绑定卡',
          confirmText: '去新建卡',
          success: function (res) {
            if (res.confirm) {
              uni.navigateTo({
                url: '/pages/ucenter/patient/add?from=detail',
              })
            } else if (res.cancel) {
              uni.navigateTo({
                url: '/pages/ucenter/patient/bind?from=detail',
              })
            }
          },
        })
      }
    },
  }
</script>

<style scoped lang="scss">
  .uni-common-mt{
    margin: 15px;
    padding-bottom: calc(20px + constant(safe-area-inset-bottom));
    padding-bottom: calc(20px + env(safe-area-inset-bottom));
  }
  .container {
    background: #fff;
  }
  .title {
    margin-top: 13px;
    margin-bottom: 5px;
    padding-left: 15px;
    padding-right: 15px;
    color: #999999;
    background-color: #ffffff;
    line-height: 1.47058824;
    font-size: 17px;
    overflow: hidden;
    position: relative;
  }
  .appoint_header {
    line-height: 45px;
    text-align: center;
    color: #3d4145;
    background-color: #edd1d8;
    font-size: 17px;
  }
  .appoint_list {
    background-color: #ffffff;
    border-top: 1px solid #e5e5e5;
    border-bottom: 1px solid #e5e5e5;
  }
  .appoint_list-info {
    padding:0 15px;
    font-size: 16px;
  }
  .appoint_list-cent-item {
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    display: -webkit-flex;
    -webkit-align-items: center;
    justify-content: space-between;
    line-height: 2.5;
  }
  .appoint_list-cent-item:last-child {
    border-bottom: none;
  }
  .patient_info {
    color: #666;
    font-weight: 400;
    white-space: nowrap;
  }

  .add-patient-btn{
    position: relative;
    display: block;
    padding:0 14px;
    box-sizing: border-box;
    text-align: center;
    text-decoration: none;
    border-radius: 5px;
    overflow: hidden;
    display: inline-block;
    line-height: 2.3;
    font-size: 13px;
    color: rgb(238,102,138);
    border: 1px solid rgb(238,102,138);
  }
  .patient_infoText {
    text-align: right;
    color: #999999;
    flex: 1;
    word-break: break-all;
    line-height: 1.5;
  }
  uni-button {
    background: #ee668a;
  } 
  uni-button[disabled][type=primary]{
    /* background: rgba(#ee668a, 0.6); */
    background-color: rgba(238, 102, 138, 0.6);
  }
  .button-hover {
    background: #ca738a;
  }
  .aliText{
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
  }
  .alig{
    color: #319b24;
    margin-right: 5rpx;
  }
  .aliball{
    height: 40rpx;
    width: 40rpx;
    margin-right: 5rpx;
  }
  .alibag{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
  }
  .alialert{
    position: fixed;
    top: 15%;
    width: 560rpx;
    left: 50%;
    margin-left: -280rpx;
    background: #fff;
    border-radius: 10px;
    padding: 0 40rpx;
    box-sizing: border-box;
  }
  .alertpng{
    width: 100%;
    height: 130px;
    position: relative;
    margin-top: -80rpx;
  }
  .alititel{
    font-weight: bold;
    font-size: 25rpx;
    text-align: center;
    margin-bottom: 30rpx;
  }
  .alili{
    font-size: 20rpx;
    margin-bottom: 35rpx;
    color: #999;
  }
  .konw{
    line-height: 100rpx;
    font-size: 35rpx;
    text-align: center;
    color: #78a5e6;
  }

  .popup-content {
	margin: 0 auto;
	background-color: #FFF;
	border-radius: 32rpx;
	overflow: hidden;
	width: 80%;

	.popup-title {
		padding: 1.3em 1.6em 0.5em;
		font-size: 30rpx;
		line-height: 1.3;
		word-wrap: break-word;
		word-break: break-all;
		font-weight: 600;
		color: #333;
		text-align: left;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;


		&::after {
			content: '';
			position: absolute;
			display: block;
			width: 100%;
			height: 1rpx;
			bottom: 0;
			left: 0;
			background-color: #eee;
		}
	}

	.popup-scroll {
		max-height: 600rpx;
		min-height: 200rpx;
		overflow-y: scroll;
	}

	.popup-text {
		display: flex;
		flex-direction: column;
		padding: 24rpx;
		font-size: 28rpx;
		line-height: 1.8em;
		overflow: scroll;
	}

	.popup-btns {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 96rpx;
		border-top: 1rpx solid #eee;

		.popup-btn {
			flex: 1;
			text-align: center;
			line-height: 96rpx;
			font-size: 36rpx;
			color: #5f646e;
		}

		.primary {
			color: #fff;
			background-color: rgb(238, 102, 138);
			margin-bottom: -.1em;
		}

		.disabled {
			color: #fff;
			background-color: #D7D7D7;
		}
	}
}
</style>
