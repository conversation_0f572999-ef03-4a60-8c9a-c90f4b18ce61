<template>
  <view class="container">
    <view class="header">
      <view class="comm_nav">
        <view
          class="nav_item"
          :class="pageIndex === index ? 'active' : ''"
          @tap="changePageIndex(item.newslist, index)"
          v-for="(item, index) in totalList"
          :key="item.id"
          >{{ item.name }}</view
        >
        <!--  <view class="nav_item" :class="pageIndex === 1 ? 'active' : ''" @tap="changePageIndex(1)">就诊卡</view>
                <view class="nav_item" @tap="changePageIndex(2)" :class="pageIndex === 2 ? 'active' : ''">预约挂号</view>
                <view class="nav_item" @tap="changePageIndex(3)" :class="pageIndex === 3 ? 'active' : ''">常见问题</view> -->
      </view>
    </view>
    <view class="body">
      <view class="phone">咨询电话：{{ telPhone }}</view>
      <view class="content">
        <uni-list>
          <uni-list-item
            :class="selectId != item.category_id ? 'noShow' : ''"
            v-for="(item, index) in titleList"
            :key="item.id"
            clickable
            @click="showDetailContent(item, index)"
            showArrow
            :title="item.title"
          />
        </uni-list>
      </view>
    </view>

    <view>
      <uni-popup ref="popup" background-color="#fff" type="top">
        <view class="pop-info">
          <view class="pop-title">{{ itemTitle }}</view>
          <view class="pop-content">
            <rich-text :nodes="itemContent"></rich-text>
          </view>
          <view class="pop-footer"><button @click="closePop">关闭</button></view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<script>
import { getGuide } from '@/api/waiting.js'
import parse from 'mini-html-parser2'
import { reportCmPV } from '@/to/cloudMonitorHelper';
export default {
  setup() {},
  mounted() {
    this.getGuideList()
  },
  data() {
    return {
      pageIndex: 0,
      telPhone: '0871-63309191',
      totalList: [],
      titleList: [],
      itemContent: '',
      itemTitle: '',
    }
  },
  onLoad(query) {
    reportCmPV({ title: '就医指南',query });
  },
  methods: {
    showDetailContent(item, index) {
      parse(item.content, (err, nodesList) => {
        if (!err) {
          this.itemContent = nodesList
        }
      })
      this.itemTitle = item.title
      this.$refs.popup.open('top')
    },
    closePop() {
      this.$refs.popup.close()
    },
    changePageIndex(list, index) {
      this.pageIndex = index
      this.titleList = list
    },
    async getGuideList() {
      const { statusCode, data } = await getGuide()
      if (statusCode !== 200) return
      this.totalList = data.data
      this.titleList = this.totalList[0].newslist
      /* if(data.data.length>0) {
            data.data.forEach((i,v) => {
            }); 
            } */
      // console.log(data)
      // console.log(statusCode)
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../common/uni-nvue.css';
.container {
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
.header {
  position: relative;
  left: 0;
  right: 0;
  z-index: 5;
  overflow: hidden;
}
.comm_nav {
  display: flex;
  padding: 0 60rpx;
  /* justify-content: space-between; */
  justify-content: center;
  background-color: #fff;
  .nav_item {
    font-size: 30rpx;
    width: 40%;
    padding: 20rpx 0;
    box-sizing: border-box;
    color: #8a8f99;
    font-weight: 500;
    text-align: center;
  }
  .active {
    font-weight: bold;
    color: rgb(238, 102, 138);
    border-bottom: 4rpx solid rgb(238, 102, 138);
  }
}

.body {
  overflow: auto;
  padding: 15px;
  .phone {
    text-align: center;
    padding: 20rpx;
    background-color: rgb(237, 209, 216);
  }
  .content{
    overflow: auto;
  }
  .uni-panel {
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 0;
  }
}

.pop-info {
  height: 100%;
  .pop-title {
    color: rgb(238, 102, 138);
    padding: 30rpx;
  }
  .pop-content {
    padding: 30rpx;
    height: 900rpx;
    overflow-x: hidden;
  }
  .pop-footer {
    padding: 30rpx;

    button {
      background-color: rgb(238, 102, 138);
      color: #fff;
    }
  }
}
</style>
