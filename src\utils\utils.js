export const getDiffDate = (diff) => {
  var dateObj = new Date();
  dateObj.setTime(dateObj.getTime() + diff * 24 * 60 * 60 * 1000);
  var result = dateObj.getFullYear() + "-" + (dateObj.getMonth() + 1) + "-" + dateObj.getDate();
  return result;
}

/**
 * 解析URL参数
 * @description 截取路由参数
 * @param {String} name - 要解析的路由参数
 * @return String
 */
export const getUrlParam = (name) => { 
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
  let r = window.location.search.substr(1).match(reg)
  if (r != null) {// ok
    return unescape(r[2])
  }
  // false
  return null
}

/**
 * 删除URL参数
 * @param {String} url 页面地址
 * @param {String} name 要删除的参数名称
 * @return {String} 删除参数后的URL
 */
export const delUrlParam = (url, keyName) => {
  let str = "";
  if (url.indexOf('?') != -1)
      str = url.substr(url.indexOf('?') + 1);
  else
      return url;
  let arr = [];
  let returnurl = "";
  if (str.indexOf('&') != -1) {
      arr = str.split('&');
      for (let i in arr) {
          if (arr[i].split('=')[0] != keyName) {
              returnurl = returnurl + arr[i].split('=')[0] + "=" + arr[i].split('=')[1] + "&";
          }
      }
      return url.substr(0, url.indexOf('?')) + "?" + returnurl.substr(0, returnurl.length - 1);
  } else {
      arr = str.split('=');
      if (arr[0] == keyName)
          return url.substr(0, url.indexOf('?'));
      else
          return url;
  }
}

/**
 * 获取头像地址，有的是完整的地址，有的是相对路径
 * @param {String} url 地址
 * @returns String
 */
export const getAvatarUrl = (url) => {
  if (!url) {
    return '';
  }
  
  if (/^https?:\/\//i.test(url)) {
    return url;
  }
  const origin = window.location.origin;
  return url.startsWith('/') 
    ? `${origin}${url}`
    : `${origin}/${url}`;
}