<template>
  <view>
    <view class="notice">
      <view class="title">昆明市儿童医院</view>
      <view class="title">“新型冠状病毒肺炎”流行病学史承诺书</view>

      <view class="desc"
        >患者姓名 {{ new Array(userinfo.patient_name.length).join('*') + userinfo.patient_name.substr(-1) }} 出生日期（8位数）
        {{ userinfo.patient_birthday }}</view
      >
      <view class="desc"
        >性别 {{ userinfo.patient_sex == 1 ? '男' : '女' }} 科 室 {{ appdata.dep_name }}</view
      >
      <view class="desc">ID号或住院号 {{ userinfo.jz_card }} 床号 ___________</view>
      <view class="notice-title"> 告知内容： </view>
      <view class="notice-text">
        根据国家发布的“新型冠状病毒肺炎”有关防控要求，该病作为急性呼吸道传染病已纳入《中华人民共和国传染病防治法》规定的乙类传染病，按甲类传染病管理。为做好疫情防控工作，及时识别“新型冠状病毒肺炎”患者，如实提供流行病学史至关重要。若您拒绝提供或隐瞒流行病学史，可能违反《中华人民共和国传染病防治法》等相关法律规定，由此造成的一切法律后果及对患者治疗带来的一切影响均由您个人承担。
      </view>
      <view class="notice-title">
        说明：根据《刑法》第三百三十条规定：拒绝执行卫生防疫机构依照传染病防治法提出的预防、控制措施的，引起甲类传染病传播或者有传播严重危险的，处三年以下有期徒刑或者拘役；后果特别严重的，处三年以上七年以下有期徒刑。
      </view>
      <view class="notice-title"> 患方承诺： </view>
      <view class="notice-text">
        医务人员已用通俗易懂的语言告知本人如实提供流行病学史的重要性，本人郑重承诺：
      </view>
      <view class="notice-text">
        1.患者及其监护人在发病前14天内无中高风险地区的旅行史或居住史；
      </view>
      <view class="notice-text">
        2.患者及其监护人在发病前14天内与新型冠状病毒感染的患者或疑似感染者无接触史；
      </view>
      <view class="notice-text">
        3.患者及其监护人在发病前14天内未接触过来自中高风险地区人员；
      </view>
      <view class="notice-text">
        4.患者及其监护人未接触过聚集性发病的患者,未到过聚集性发病的场所。（聚集性发病：2周内在小范围如家庭、办公室、学校班级等场所，出现2例及以上发热和/或呼吸道症状的病例。）
      </view>
      <view class="notice-text"
        >本人对以上提供的健康相关信息的真实性负责，如因信息不实引起疫情传播和扩散，愿承担由此带来的全部法律责任。</view
      >
      <!-- <view class="notice-title"> 患者家属意见： </view>
      <view class="notice-text">我已详细阅读以上内容，对医师的告知表示完全理解。经慎重考虑，我决定：</view> -->
      <view class="uni-form-item uni-column relation">
        <view class="title">身份证号码</view>
        <input class="uni-input" v-model="id_card" type="text" placeholder="请输入身份证号" />
      </view>
      <view class="uni-form-item uni-column relation">
        <view class="title">您与患者关系</view>
        <input
          class="uni-input"
          maxlength="10"
          v-model="relation"
          type="text"
          placeholder="本人,父子,父女,母子,母女..."
        />
      </view>
    </view>
    <view class="uni-padding-wrap uni-common-mt">
      <button type="primary" class="signBtn" @click="signBtn">同意并签名确认</button>
      <button @click="backto">不同意</button>
    </view>
  </view>
</template>

<script>
  import { SubmitAppoint, getById, sendGreenEnergy,getMsg } from '@/api/waiting'
  const monitor = require('../../to/alipayLogger.js')
  export default {
    data() {
      return {
        appdata: '',
        userinfo: {},
        pickerUser: {},
        relation: '',
        id_card: '',
        signImg: '',
        ispost: true,
      }
    },
    onLoad: function (option) {
      this.pickerUser = JSON.parse(option.pickerUser)
      this.appdata = JSON.parse(option.appdata)
      let query = {
        userid: this.pickerUser.user_id,
        openid: uni.getStorageSync('userInfo').openid,
        patientid: this.pickerUser.id,
      }
      getById(query).then((res) => {
        this.userinfo = res.data.data
      })
    },
    onShow() {
      let that = this
      if (uni.getStorageSync('name_sign')) {
        that.signImg = uni.getStorageSync('name_sign')
        that.submit_appoint(that.userinfo)
      }
      // uni.$on('signData', function (data) {
      //   that.signImg = data
      //   const params = 'covd:' + data.covd
      //   if (data.covd) {
      //     that.submit_appoint(that.userinfo)
      //   }
      // })
    },
    methods: {
      submit_appoint(_data) {
        let thatdata = this
        if (
          this.appdata.dep_id == 'A000021' ||
          this.appdata.dep_id == 'A000096' ||
          this.appdata.dep_id == 'A000129'
        ) {
          uni.showModal({
            title: '预约须知',
            content:
              '鉴于核酸检测人员数量较多，请预约好提前30分钟到前兴院区住院部前小广场患者及陪护核酸采集处登记处报道（开单及检测标本），采集时间可能与预约时间有出入，希望家长理解，如须预约，请确认订单。',
            success: async function (res) {
              if (res.confirm) {
                uni.showModal({
                  title: '新冠核酸检测告知书',
                  confirmText: '确定，进行告知书签署',
                  confirmColor: '#ee668a',
                  showCancel: false,
                  success: function (res) {
                    if (res.confirm) {
                      uni.setStorageSync('promise_sign', thatdata.signImg)
                      uni.setStorageSync('name_sign', '')
                      uni.reLaunch({
                        url:
                          '/pages/notice/covd_inform?idCard=' +
                          thatdata.id_card +
                          '&appdata=' +
                          JSON.stringify(thatdata.appdata) +
                          '&pickerUser=' +
                          JSON.stringify(thatdata.pickerUser),
                        animationType: 'slide-in-bottom',
                        animationDuration: 200,
                      })
                    }
                  },
                })
              } else {
                //console.log('取消申领')
                uni.navigateBack({
                  delta: 1,
                })
              }
            },
          })
          return false
        } else {
          let subquery = {
            id_no: this.id_card,
            rel_sign: this.relation,
            name_sign: this.signImg,
            patient_name: _data.patient_name,
            birth: _data.patient_birthday.replace(/-/g, ''),
            sex: _data.patient_sex == 1 ? '男' : '女',
            dep_name: this.appdata.dep_name,
            jz_card: _data.jz_card,
            patient_id: _data.id,
            hos_code: this.appdata.hos_code,
            dep_id: this.appdata.dep_id,
            doc_id: this.appdata.doc_id,
            schedule_id: this.appdata.schedule_id,
            sch_date: this.appdata.sch_date,
            time_type: this.appdata.time_type,
            queue_sn: this.appdata.queue_sn,
            pat_id: this.pickerUser.id,
            userid: uni.getStorageSync('userInfo').userid,
            openid: uni.getStorageSync('userInfo').openid,
          }
          let that = this
          if (that.ispost) {
            that.ispost = false
            SubmitAppoint(subquery).then((res) => {
              that.ispost = true
              if (res.data.code == 1) {
                monitor.api({api:"预约挂号",success:true,c1:"taSR_YL",time:200})
                let query = {
                  scene: 'horegister',
                  order_no: res.data.data.order_no,
                  accessToken: uni.getStorageSync('mfrstreCode')||uni.getStorageSync('userInfo').access_token,
                }
                sendGreenEnergy(query).then((ret) => {
                  if(ret.data.code == 1){
                    uni.showToast({
                      image: '/static/ali/ball.png',
                      title: '本次预约获得'+ret.data.data.total_energy+'g绿色能量',
                      duration: 2000
                    });
                  }
                })
                let msgQuery = {
                  openid: uni.getStorageSync('userInfo').openid,
                  order_id: res.data.data.order_id,
                  patient_name: uni.getStorageSync('userInfo').user_name,
                  accessToken: uni.getStorageSync('authorizationCode')||uni.getStorageSync('userInfo').access_token,
                }
                getMsg(msgQuery).then((ret) => {
                  if(ret.data.code == 1){
                    console.log(ret.data.data)
                  }
                })
                uni.showModal({
                  title: '预约成功',
                  content: res.data.data.msg_content,
                  success: function (showres) {
                    uni.setStorageSync('name_sign', '')
                    if (showres.confirm) {
                      uni.navigateTo({
                        url: '/pages/ucenter/appoint/detail?id=' + res.data.data.order_id,
                      })
                    }
                  },
                })
              } else {
                uni.showToast({
                  title: res.data.msg,
                  icon: 'none',
                })
              }
            })
          }
        }
      },
      signBtn() {
        if (!this.relation) {
          uni.showToast({
            title: '与患者关系缺失',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        if (!this.id_card) {
          uni.showToast({
            title: '请录入身份证号',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        uni.navigateTo({
          url: '/pages/signature/signature',
          animationType: 'slide-in-bottom',
          animationDuration: 200,
        })
      },
      backto() {
        uni.navigateBack({
          delta: 1,
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  .uni-input,
  .uni-input-input {
    background: transparent;
  }
  .notice {
    text-align: center;
    color: #3d4145;
    padding: 5px;
    .title {
      margin-bottom: 5px;
      font-weight: 400;
      font-size: 18px;
    }
    .desc {
      font-size: 14px;
      color: #999999;
    }
    .notice-title {
      margin-top: 10px;
      margin-bottom: 5px;
      font-size: 17px;
      font-weight: bold;
      text-align: left;
    }
    .notice-text {
      font-size: 16px;
      text-align: left;
    }
    .relation {
      border-top: 1px solid #e5e5e5;
      border-bottom: 1px solid #e5e5e5;
      display: flex;
      align-items: center;
      padding: 10px 0;
      .title {
        margin: 0;
        margin-right: 10px;
      }
    }
  }
  .uni-padding-wrap {
    padding: 15px;
    .signBtn {
      background: #ee668a;
      margin-bottom: 10px;
    }
  }
</style>
