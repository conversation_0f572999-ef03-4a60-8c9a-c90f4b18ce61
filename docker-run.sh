#!/bin/bash

# Docker 运行脚本
# 用法: ./docker-run.sh [dev|prod|stop|logs|clean]

set -e

PROJECT_NAME="ertong-uni-app"

case "$1" in
    "dev")
        echo "🚀 启动开发环境..."
        docker-compose -f docker-compose.dev.yml up --build -d
        echo "✅ 开发环境已启动"
        echo "📱 访问地址: http://localhost:8080"
        echo "📱 带路径访问: http://localhost:8080/ui/"
        echo "📋 查看日志: ./docker-run.sh logs"
        ;;
    "prod")
        echo "🚀 启动生产环境..."
        docker-compose -f docker-compose.prod.yml up --build -d
        echo "✅ 生产环境已启动"
        echo "📱 访问地址: http://localhost"
        echo "📱 带路径访问: http://localhost/ui/"
        echo "📋 查看日志: ./docker-run.sh logs"
        ;;
    "stop")
        echo "🛑 停止所有容器..."
        docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
        docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
        echo "✅ 所有容器已停止"
        ;;
    "logs")
        echo "📋 查看日志..."
        if docker ps | grep -q "${PROJECT_NAME}-dev"; then
            docker-compose -f docker-compose.dev.yml logs -f
        elif docker ps | grep -q "${PROJECT_NAME}-prod"; then
            docker-compose -f docker-compose.prod.yml logs -f
        else
            echo "❌ 没有运行中的容器"
        fi
        ;;
    "clean")
        echo "🧹 清理 Docker 资源..."
        docker-compose -f docker-compose.dev.yml down --rmi all --volumes --remove-orphans 2>/dev/null || true
        docker-compose -f docker-compose.prod.yml down --rmi all --volumes --remove-orphans 2>/dev/null || true
        docker system prune -f
        echo "✅ 清理完成"
        ;;
    "restart")
        echo "🔄 重启服务..."
        $0 stop
        sleep 2
        $0 dev
        ;;
    *)
        echo "用法: $0 [dev|prod|stop|logs|clean|restart]"
        echo ""
        echo "命令说明:"
        echo "  dev     - 启动开发环境 (端口 8080)"
        echo "  prod    - 启动生产环境 (端口 80)"
        echo "  stop    - 停止所有容器"
        echo "  logs    - 查看容器日志"
        echo "  clean   - 清理所有 Docker 资源"
        echo "  restart - 重启开发环境"
        echo ""
        echo "示例:"
        echo "  $0 dev    # 启动开发环境"
        echo "  $0 logs   # 查看日志"
        echo "  $0 stop   # 停止服务"
        exit 1
        ;;
esac
