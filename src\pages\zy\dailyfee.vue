<template>
  <view class="container">
    <view class="header"> </view>
    <view class="body">
      <view class="info">
        <uni-list border-full>
          <uni-list-item class="sel-info" title="账单名称" rightText="账单金额" />
          <uni-list-item v-for="(item, index) in feeList" :key="index" :title="item.BillName" :rightText="item.BillCharge" />
        </uni-list>
      </view>
    </view>
    <view class="footer"> </view>
  </view>
</template>
<script>
// import { defineComponent } from '@vue/composition-api'
import { getFeeDetailZy } from '@/api/ucenter.js'
export default {
  setup() {},
  onLoad(option) {
    //option为object类型，会序列化上个页⾯传递的参数
    // console.log(option.pat_id)
    // console.log(option.inpatientno)
    // console.log(option.admisstimes)
    this.getDetailInfo(option.pat_id,option.inpatientno,option.admisstimes)
  },
  data() {
    return {
      feeList:[]
    }
  },
  methods: {
    async getDetailInfo(pat_id,inpatientno,admisstimes) {
      const userInfo = uni.getStorageSync('userInfo')
      const { statusCode, data } = await getFeeDetailZy(null, {
        userid: userInfo.user_id,
        openid: userInfo.openid,
        pat_id: pat_id,
        inpatientno: inpatientno,
        admisstimes: admisstimes,
      })
      if (statusCode != 200) {
        uni.showToast({
          title: '网络错误',
          icon: 'none',
        })
        return
      }
      if (data.code != 1) {
        uni.showToast({
          title: '暂无数据',
          icon: 'none',
        })
        return
      }
      this.feeList = data.data
    },
  },
}
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
  overflow: auto;
}
.sel-info{
  color: rgb(238, 102, 138);
}
.body{
  .info{
    padding: 15px;
  }
}
</style>
