@echo off
echo Docker Environment Test
echo.

echo Checking Docker version...
docker --version
echo.

echo Checking running containers...
docker ps
echo.

echo Checking uni-app container...
docker ps | findstr "ertong-uni-app-dev"
if %errorlevel% equ 0 (
    echo Development container is running
    echo Access URL: http://localhost:8080/ui/
    echo.
    echo Container logs:
    docker logs ertong-uni-app-dev --tail 5
) else (
    echo Development container is not running
    echo Start command: docker-run.bat dev
)

echo.
echo Checking port 8080...
netstat -an | findstr ":8080"

echo.
echo Test completed!
pause
