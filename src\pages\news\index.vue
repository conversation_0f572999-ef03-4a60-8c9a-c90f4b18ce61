<template>
  <view class="container">
    <view class="header">
      <view class="comm_nav">
        <view
          class="nav_item"
          :class="pageIndex === index ? 'active' : ''"
          @tap="changePageIndex(item.newslist, index)"
          v-for="(item, index) in totalList"
          :key="item.id"
          >{{ item.name }}</view
        >
      </view>
    </view>
    <view class="body">
      <view class="content">
        <uni-list>
          <uni-list-item
            :class="selectId != item.category_id ? 'noShow' : ''"
            v-for="(item, index) in titleList"
            :key="item.id"
            clickable
            @click="showDetailContent(item, index)"
            showArrow
            :title="item.title"
          />
        </uni-list>
      </view>
    </view>

    <view>
      <uni-popup ref="popup" background-color="#fff" type="top" height="100%">
        <view class="pop-info">
          <view></view>
          <view class="pop-title">{{ itemTitle }}</view>
          <view class="pop-content">
            <rich-text :nodes="itemContent"></rich-text>
          </view>
          <view class="pop-footer">
            <button @click="closePop">关闭</button>
          </view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<script>
import { getList } from '@/api/waiting.js'
import parse from 'mini-html-parser2'
export default {
  setup() {},
  mounted() {
    this.getPageList()
  },
  data() {
    return {
      pageIndex: 0,
      totalList: [],
      titleList: [],
      itemContent: '',
      itemTitle: '',
    }
  },
  methods: {
    showDetailContent(item, index) {
      parse(item.content, (err, nodesList) => {
        if (!err) {
          this.itemContent = nodesList
        }
      })
      this.itemTitle = item.title
      this.$refs.popup.open('top')
    },
    closePop() {
      this.$refs.popup.close()
    },
    changePageIndex(list, index) {
      this.pageIndex = index
      this.titleList = list
    },
    async getPageList() {
      const { statusCode, data } = await getList(null, { type: 'news' })
      if (statusCode !== 200) return
      this.totalList = data.data
      this.titleList = this.totalList[0].newslist
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../common/uni-nvue.css';
.container {
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
.header {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 15;
  overflow: hidden;
}
.comm_nav {
  display: flex;
  padding: 0 60rpx;
  /* justify-content: space-between; */
  justify-content: center;
  background-color: #fff;
  .nav_item {
    font-size: 30rpx;
    width: 40%;
    padding: 20rpx 0;
    box-sizing: border-box;
    color: #8a8f99;
    font-weight: 500;
    text-align: center;
  }
  .active {
    font-weight: bold;
    color: rgb(238, 102, 138);
    border-bottom: 4rpx solid rgb(238, 102, 138);
  }
}

.body {
  position: relative;
  margin-top: 90rpx;
  overflow: auto;
  padding: 15px;

  .content {
    height: 100%;
    overflow-x: hidden;
  }

  .uni-panel {
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 0;
  }
}

.pop-info {
  height: 100%;
  .pop-title {
    color: rgb(238, 102, 138);
    padding: 30rpx;
  }
  .pop-content {
    padding: 30rpx;
    height: 900rpx;
    overflow-x: hidden;
  }
  .pop-footer {
    padding: 30rpx;

    button {
      background-color: rgb(238, 102, 138);
      color: #fff;
    }
  }
}
</style>
