<template>
  <view class="healthcard-page">
    <view class="header"></view>
    <view class="content-box">
      <view class="info">就诊人信息</view>
      <view  v-if="cardStatue === 1" class="warning-info">请补全就诊人证件号，民族信息，否则无法获取电子健康卡，信息全请直接点击确定</view>
      <view class="form-box">
        <view class="qr-code-box" v-if="cardStatue === 2 && qrHealthCard && qrHealthCard.img"
          @click="getHealthCode">
          <view class="qr-code">
            <image :src="'data:image/png;base64,' + qrHealthCard.img" :width="size" :height="size"></image>
          </view>
        </view>

        <uni-forms ref="baseForm" :rules="rules" :modelValue="patientDetaile">
          <uni-forms-item label="姓名">
            <view class="pinfotxt">{{ patientDetaile.patient_name || '' }}</view>
          </uni-forms-item>
          <uni-forms-item label="性别">
            <view class="pinfotxt">{{ patientDetaile.patient_sex == 1 ? '男' : '女' }}</view>
          </uni-forms-item>
          <uni-forms-item  v-if="cardStatue === 1" label="证件类型" name="idcard_type">
            <uni-data-picker placeholder="请选择证件类型" popup-title="请选择证件类型" :localdata="idcard_info"
              v-model="patientDetaile.idcard_type" @change="changeIdCardType"
              @nodeclick="onnodeclickIdCardType"></uni-data-picker>
          </uni-forms-item>

          <uni-forms-item label="证件号" name="patient_idcard">
            <view v-if="cardStatue === 2" class="pinfotxt">
            {{ patientDetaile.patient_idcard || '' }}
            </view>
            <uni-easyinput v-else inputBorder v-model="patientDetaile.patient_idcard"
              placeholder="请输入证件号码" />
          </uni-forms-item>
          <uni-forms-item v-if="cardStatue === 1" label="民族" name="patient_nation" class="pick-comm">
            <uni-data-picker placeholder="请选择民族" popup-title="请选择民族" :localdata="nation_info"
              v-model="patientDetaile.patient_nation" @change="changeNation"
              @nodeclick="onnodeclickNation"></uni-data-picker>
          </uni-forms-item>
          <button  v-if="cardStatue === 1" type="primary" class="my-button" :class="canClick ? '' : 'disabled-btn'" @click="submit('baseForm')"> 确定 </button>
          <button  v-if="cardStatue === 2" type="primary" class="my-button" @click="enterCardBag"> 进入卡包 </button>
          <button v-if="cardStatue === 2" class="close-btn" @click="unBindHealthCard()"> 解除绑定 </button>
          <button class="close-btn" @click="goBack()"> 关闭 </button>
          <view v-if="cardStatue === 1" class="has-card-box" @click="hasCard">我已有卡，去关联</view>
        </uni-forms>
      </view>
    </view>
    <view class="footer"></view>
  </view>
</template>

<script>
import { getMyPatientDetail } from '@/api/waiting.js'
import { upPatientInfo } from '@/api/ucenter.js'
import { reqUpdatePatTx, reqGetHealthCode,reqCardBagUrl,reqUnBindHealthCard } from '@/api/wechat.js'
import { nationList } from './nation.js'
import { uniForms, uniFormsItem, uniEasyinput } from '@dcloudio/uni-ui'
import { reportCmPV } from '@/to/cloudMonitorHelper';
const monitor = require('@/to/alipayLogger.js')

export default {
  components: { uniForms, uniFormsItem, uniEasyinput },
  data() {
    return {
      cardStatue: 1,
      canClick: true,
      pat_id: '',
      patientDetaile: {
        idcard_type: '',
        patient_idcard: '',
        patient_nation: '',
      },
      qrHealthCard: {},
      size: 180,
      idcard_info: [
        { value: 0, text: '身份证' },
        { value: 1, text: '出生证' },
      ],
      nation_info: nationList,
      // 校验规则
      rules: {
        idcard_type: {
          rules: [{ required: true, errorMessage: '必须选择证件类型' }],
        },
        patient_idcard: {
          rules: [
            { required: true, errorMessage: '证件号不能为空' },
            {
              validateFunction: function (rule, value, data, callback) {
                if (value.length < 5) {
                  callback('请确认证件号格式')
                }
                return true
              },
            },
          ],
        },
        patient_nation: {
          rules: [{ required: true, errorMessage: '民族必须选择' }],
        },
      },
      
    }
  },
  watch: {
    patientDetaile: {
      handler(newVal, oldVal) {
        // console.log('newVal:', newVal, 'oldVal:', oldVal)
        this.canClick = true
      },
      deep: true,
    }
  },
  onLoad: function (option) {
    reportCmPV({ title: '电子健康卡', option });
    this.pat_id = option.id
    this.getDetail(option.id)
    console.log('option:', option)
    this.cardStatue = option.type ? Number(option.type) : 1
  },
  methods: {
    changeIdCardType(e) {
      // console.log('e:', e)
    },
    onnodeclickIdCardType(e) {
      // console.log(e)
      // this.patientDetaile.idcard_type = e.value
      // console.log(this.patientDetaile,999)
    },
    changeNation(e) {
      // console.log('e:', e)
    },
    onnodeclickNation(e) {
      // console.log(this.patientDetaile,888)
    },
    getHealthCode(){
      reqGetHealthCode({
        health_card: this.patientDetaile.tx_health_card,
        id_card: this.patientDetaile.patient_idcard,
        idcard_type: this.patientDetaile.idcard_type
      }).then((res) => {
        let rt = res.data
        if (rt.code == 1) {
          this.qrHealthCard = rt.data
        } else {
          uni.showToast({
            icon: 'none',
            title: rt.msg,
            duration: 2000,
          })
        }
      })
    },

    submit(ref) {
      if(!this.canClick) {
        return
      }

      console.log('this.patientDetaile:', this.patientDetaile)
      if (this.patientDetaile.idcard_type === '') {
        uni.showToast({
          icon: 'none',
          title: `请选择证件类型`,
        })
        return
      }
      if (this.patientDetaile.patient_idcard.length < 5) {
        uni.showToast({
          icon: 'none',
          title: `证件号格式错误`,
        })
        return
      }
      if (this.patientDetaile.idcard_type == 0 && this.patientDetaile.patient_idcard.length != 18) {
        uni.showToast({
          icon: 'none',
          title: `身份证为18位`,
        })
        return
      }
      if (!this.patientDetaile.patient_nation) {
        uni.showToast({
          icon: 'none',
          title: `请选择您的民族`,
        })
        return
      }

       // #ifdef MP-ALIPAY
      upPatientInfo(this.patientDetaile).then((res) => {
        let rt = res.data
       
        if (rt.code == 1) {
          monitor.api({ api: "电子健康卡", success: true, c1: "taSR_YL", time: 200 })
          let url = '/pages/ucenter/patient/detail?id=' + this.patientDetaile.id
          uni.navigateTo({
            url,
          })
        } else {
          uni.showToast({
            icon: 'none',
            title: rt.msg,
            duration: 3000,
          })
        }
      })
      // #endif
      // #ifdef H5
      this.updatePatTx()
      //#endif
    },
    updatePatTx(){
      reqUpdatePatTx(this.patientDetaile).then((res) => {
        let rt = res.data
        if (rt.code == 1) {
            uni.showToast({
              title: '标题',
              duration: 2000
            });
            window.history.go(-1)
        } else {
            if(rt.msg.split("|||")[0] == -10060 || rt.msg.split("|||")[0] == -10058){
                uni.showModal({
                  title: '提示',
                  content: '姓名和身份证号不一致，是否申诉？',
                  success: function (res) {
                    if (res.confirm) {
                      location.href = "https://01-h5-health.tengmed.com/h5/whitelist?hospitalId=34140&appId=66c7d0a5c1e25716908e8c7aff710479&orderId="+ rt.msg.split("|||")[1] +"&redirect_uri=" + encodeURIComponent(document.location);
                    } else if (res.cancel) {
                      console.log('用户点击取消');
                    }
                  }
                });
            }else{
                uni.showToast({
                  icon: 'none',
                  title: rt.msg,
                  duration: 2000,
                })
              this.canClick = false
            }
        }
      })
    },
    async getDetail(pat_id) {
      const userInfo = uni.getStorageSync('userInfo')

      const { statusCode, data } = await getMyPatientDetail(null, {
        patientid: pat_id,
        userid: userInfo.user_id,
        openid: userInfo.openid,
      })
      if (statusCode != 200) {
        uni.showToast({
          title: '暂无数据',
          duration: 2000,
          icon: 'none',
        })
        return
      }
      this.patientDetaile = data.data
      // console.log(this.patientDetaile)

      if (this.patientDetaile.tx_health_card.length > 5) {
        // this.getHQrCode(this.patientDetaile.tx_health_card)
        this.getHealthCode()
      }
    },
    goBack() {
      history.back(-1)
    },
    hasCard() {
      let domain = window.location.host
      let apiAddr = '//' + location.host + '/api/';
      let redirect_uri = document.location.protocol + apiAddr + "Txhealthcard/plist?from=wx&env=test&patid=" + this.pat_id;
      if (domain == "ertong.ynhdkc.com") {
        redirect_uri = document.location.protocol + apiAddr + "Txhealthcard/plist?from=wx&patid=" + this.pat_id;
      }
      location.href = "https://health.tengmed.com/open/getHealthCardList?redirect_uri=" + encodeURIComponent(redirect_uri) + "&hospitalId=34140";
    },
    enterCardBag(){
      let qrcodeText = this.qrHealthCard.qrCodeText;
      if (qrcodeText == '') {
          uni.showToast({
              icon: 'none',
              title: '缺少二维码文本信息，请点击 刷新卡片信息 后再试',
              duration: 2000,
          })
          return;
      }
      reqCardBagUrl({ qrCodeText: qrcodeText }).then((res) => {
        let cGetRet = res.data;
          if (cGetRet.code == 1) {
              // 跳转
              let domain = window.location.host
              let apiAddr =  '//' + location.host + '/api/';
              let redirect_uri = document.location.protocol + apiAddr + "Txhealthcard/goPatList?from=wx&env=test&patid=" + this.pat_id;
              if (domain == "ertong.ynhdkc.com") {
                  redirect_uri = document.location.protocol + apiAddr + "Txhealthcard/goPatList?from=wx&patid=" + this.pat_id;
              }

              location.href = "https://health.tengmed.com/open/takeMsCard?order_id=" + cGetRet.data.orderId + "&redirect_uri=" + encodeURIComponent(redirect_uri);
          } else {
              uni.showToast({
                  icon: 'none',
                  title: cGetRet.msg,
                  duration: 2000,
              })
          }
      });
    },
    unBindHealthCard() {
      let pat_id = this.pat_id;
      if (pat_id) {
          uni.showModal({
              title: '危险操作',
              content: '您确定要解除绑定吗？',
              success:  (res) => {
                  if (res.confirm) {
                    reqUnBindHealthCard({ id: pat_id }).then((reqRes) => {
                        let rt = reqRes.data
                        if (rt.code == 1) {
                            uni.showToast({
                                title: '解除绑定成功',
                                duration: 2000,
                            });
                            this.getDetail(pat_id)
                        } else {
                            uni.showToast({
                                icon: 'none',
                                title: rt.msg,
                                duration: 2000,
                            })
                        }
                    })
                  } else if (res.cancel) {
                      console.log('用户点击取消');
                  }
              }
          });
      } else {
          uni.showToast({
              icon: 'none',
              title: '缺少就诊人唯一标识',
              duration: 2000,
          })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.uni-stat__select {
  padding: 0 !important;
}

.healthcard-page {
  background-color: #fff;

  .content-box {
    .info {
      text-align: center;
      padding: 20rpx;
      background-color: rgb(237, 209, 216);
    }

    .warning-info {
      color: #e64340;
      font-size: 12px;
      margin: 0 15px;
    }

    .form-box {
      padding: 15px;
      background-color: #fff;


      .qr-code-box{
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        margin-bottom: 20px;
        width: 100%;

        .qr-code {
          width:120px;
          height:120px;
          border: 1px solid #000;

          >image{
            width: 100%;
            height: 100%;
          }
        }
      }

      .pinfotxt {
        display: flex;
        flex-shrink: 0;
        box-sizing: border-box;
        flex-direction: row;
        align-items: center;
        width: 65px;
        padding: 5px 0;
        height: 36px;
      }
    }

    .my-button {
      color: #fff;
      background-color: rgb(238, 102, 138);
    }

    .disabled-btn{
      color: rgba(255, 255, 255, 0.6);
    }

    .close-btn {
      margin: 20px 0 0;
    }

    .has-card-box{
      color: #007aff;
      font-size: 16px;
      margin: 0 15px;
      text-align: center;
      padding: 10px;
    }
  }

}
</style>
