<template>
  <view class="signa">
    <view class="btn">
      <view class="cancel-btn" @click="toBack">返回</view>
      <!-- <view class="hand-title">手写签名</view> -->
      <view @click="clear" class="rewrite-btn">重写</view>
      <view @click="save" class="save-btn">使用</view>
    </view>
    <view class="canvas-wrap">
      <canvas
        class="canvas"
        disable-scroll="true"
        id="designature"
        @touchstart="starts"
        @touchmove="moves"
        @touchend="end"
      ></canvas>
    </view>
    <Message ref="Message"></Message>
  </view>
</template>

<script>
  /*
   *	已兼容h5和小程序端
   */
  import boboMessage from '@/pages/component/bobo-message/bobo-message.vue'

  export default {
    data() {
      return {
        dom: null,
        line: [],
        radius: 0,
        isMove: false,
      }
    },
    components: {
      Message: boboMessage,
    },
    created() {
      this.dom = uni.createCanvasContext('designature', this)
    },
    onLoad() {},
    methods: {
      toBack() {
        uni.navigateBack()
      },
      end(e) {},
      distance(a, b) {
        let x = b.x - a.x
        let y = b.y - a.y
        return Math.sqrt(x * x + y * y)
      },
      starts(e) {
        this.line.push({
          points: [
            {
              time: new Date().getTime(),
              x: e.touches[0].x,
              y: e.touches[0].y,
              dis: 0,
            },
          ],
        })
        let currentPoint = {
          x: e.touches[0].x,
          y: e.touches[0].y,
        }
        this.currentPoint = currentPoint
        this.drawer(this.line[this.line.length - 1])
      },
      moves(e) {
        this.isMove = true
        let point = {
          x: e.touches[0].x,
          y: e.touches[0].y,
        }
        ;(this.lastPoint = this.currentPoint), (this.currentPoint = point)
        this.line[this.line.length - 1].points.push({
          time: new Date().getTime(),
          x: e.touches[0].x,
          y: e.touches[0].y,
          dis: this.distance(this.currentPoint, this.lastPoint),
        })
        this.drawer(this.line[this.line.length - 1])
      },
      drawer(item) {
        let x1,
          x2,
          y1,
          y2,
          len,
          radius,
          r,
          cx,
          cy,
          t = 0.5,
          x,
          y
        var time = 0
        if (item.points.length > 2) {
          let lines = item.points[item.points.length - 3]
          let line = item.points[item.points.length - 2]
          let end = item.points[item.points.length - 1]
          x = line.x
          y = line.y
          x1 = lines.x
          y1 = lines.y
          x2 = end.x
          y2 = end.y
          var dis = 0
          time = line.time - lines.time + (end.time - line.time)
          dis = line.dis + lines.dis + end.dis
          var dom = this.dom
          var or = Math.min((time / dis) * this.linePressure + this.lineMin, this.lineMax)
          cx = (x - Math.pow(1 - t, 2) * x1 - Math.pow(t, 2) * x2) / (2 * t * (1 - t))
          cy = (y - Math.pow(1 - t, 2) * y1 - Math.pow(t, 2) * y2) / (2 * t * (1 - t))
          dom.setLineCap('round')
          dom.beginPath()
          dom.setStrokeStyle('black')
          dom.setLineWidth(5)
          dom.moveTo(x1, y1)
          dom.quadraticCurveTo(cx, cy, x2, y2)
          dom.stroke()
          dom.draw(true)
        }
      },
      clear() {
        this.dom.clearRect(0, 0, 1000, 1000)
        this.dom.draw()
        this.isMove = false
      },
      save() {

        if (!this.isMove) return this.$refs.Message.error('尚未进行签名！')
        //const { type } = this.$route.query
        // my.canvasToTempFilePath({
        uni.canvasToTempFilePath({
          canvasId: 'designature',
          Id: 'designature',
          quality: 1, //图片质量
          success: async (res) => {
            try {
              //开始
              uni.getFileSystemManager().readFile({
                filePath: res.tempFilePath, //图片路径filePath
                encoding: 'base64', //编码格式
                success: (res) => {
                  //成功的回调
                  
                  uni.setStorageSync('name_sign', 'data:image/png;base64,' + res.data)
                  //uni.$emit('signData', {covd:'data:image/png;base64,' + res.data})
                  uni.navigateBack({
                    delta: 1
                  })
                },
              })
            } catch (error) {
              setTimeout(() => {
                uni.hideLoading()
              }, 2000)
            }
          },
        })
      }, //save方法结束
    },
  }
</script>

<style scoped lang="scss">
  .signa {
    position: relative;
    overflow: hidden;
    // background-color: #fbfbfb;
    height: 100vh;
    width: 100vw;
    z-index: 1;
    .canvas-wrap {
      display: flex;
      justify-content: center;
      align-items: center;
      height: calc(100% - 100px);
      width: 100vw;
      margin-top: 100px;
    }
    .canvas {
      width: 100%;
      height: 90vh;
      background-color: #f4f8fb;
      position: absolute;
      z-index: 9999;
      // left: 45px;
      // border: 1px solid #d6d6d6;
    }
    .btn {
      height: 100px;
      width: 100%;
      right: 0;
      position: fixed;
      font-size: 22px;
      top: 0;
      
      font-size: 40rpx;
      .cancel-btn {
        position: fixed;
        top: 50rpx;
        right: 60rpx;
        color: $uni-text-color-blue;
        transform: rotate(90deg);
      }
      .hand-title {
        position: fixed;
        top: 45%;
        right: -40rpx;
        color: $uni-text-color-blue;
        transform: rotate(90deg);
      }
      .rewrite-btn {
        position: fixed;
        top: 50rpx;
        left: 160rpx;
        color: $uni-text-color-blue;
        transform: rotate(90deg);
      }
      .save-btn {
        position: fixed;
        top: 50rpx;
        left: 60rpx;
        padding: 0 10rpx;
        color: #fff;
        background: $uni-color-primary;
        transform: rotate(90deg);
      }
    }
  }
</style>
