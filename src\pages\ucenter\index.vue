<template>
  <view class="container">
    <headerBAr title="个人中心"></headerBAr>
    <view class="uni-padding-wrap top-40">
      <cover-image v-if="userImg" class="controls-play img" :src="userImg"></cover-image>
      <cover-image v-else class="controls-play img" src="@/static/img/avatar.png"></cover-image>
    </view>

    <view class="content">
      <view class="body">
        <view class="list-title">就诊信息</view>
        <uni-list>
          <uni-list-item :clickable="true" showArrow v-for="(item, index) in toolsList" :key="item.id" @click="goDetailPage(index, item)"  :title="item.name" />
        </uni-list>
        <view class="list-title">其它问题</view>
        <uni-list>
          <uni-list-item :clickable="true" showArrow v-for="(item, index) in otherList" :key="item.id" @click="goDetailPage(index, item)"  :title="item.name" />
        </uni-list>
      </view>
    </view>

    <!-- <view class="minedictor">
      <view class="listTitle"> 我的就诊 </view>
      <view class="uni-panel" v-for="(item, index) in toolsList" :key="item.id">
        <view class="uni-panel-h" @click="goDetailPage(index, item)">
          <uni-icons :type="item.icon" size="20"></uni-icons>
          <text class="uni-panel-text">{{ item.name }}</text>
          <uni-icons type="forward" size="20"></uni-icons>
        </view>
      </view>
    </view>
    <view class="minedictor">
      <view class="listTitle"> 其他问题 </view>
      <view class="uni-panel" v-for="(item, index) in otherList" :key="item.id">
        <view class="uni-panel-h" @click="goDetailPage(index, item)">
          <uni-icons :type="item.icon" size="20"></uni-icons>
          <text class="uni-panel-text">{{ item.name }}</text>
          <uni-icons type="forward" size="20"></uni-icons>
        </view>
      </view>
    </view> -->
  </view>
</template>
<script>
import { mapState } from 'vuex'
import { uniList, uniListItem } from '@dcloudio/uni-ui'
import headerBAr from '@/pages/component/header_bar/header_bar.vue'

export default {
  components: { uniList, uniListItem,headerBAr },
  data() {
    return {
      userImg: '',
      title: 'title',
      toolsList: [
        {
          id: 'appoint',
          name: '预约记录',
          icon: 'wallet-filled',
          url: '/pages/ucenter/appoint/appoint',
        },
        {
          id: 'report',
          name: '我的报告',
          icon: 'calendar-filled',
          url: '/pages/ucenter/report/report',
        },
        {
          id: 'patient',
          name: '我的就诊人',
          icon: 'person-filled',
          url: '/pages/ucenter/patient/patient',
        },
      ],
      otherList: [
        {
          id: 'healthedu',
          name: '健康教育',
          icon: 'info-filled',
          url: '/pages/healthedu/index',
        },
        {
          id: 'news',
          name: '资讯消息',
          icon: 'info-filled',
          url: '/pages/news/index',
        },
        {
          id: 'manual',
          name: '使用指南',
          icon: 'info-filled',
          url: '/pages/guide/index',
        },
      ],
    }
  },
  computed: {
    ...mapState({
      hasLeftWin: (state) => !state.noMatchLeftWindow,
    }),
  },
  methods: {
    goDetailPage(panel, e) {
      const url = e.url
      uni.navigateTo({
        url: url,
        success(res) {
        },
        fail(err) {
        },
      })
    },
  },
  onLoad: function () {
    this.userImg = uni.getStorageSync('userInfo')?uni.getStorageSync('userInfo').headimgurl:null
  },
}
</script>
<style>
@import '../../common/uni-nvue.css';
</style>
<style>
.list-title{
  padding: 15px;
  color: #999999;
  font-size: 14px;
}
.controls-play {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto;
}

.uni-padding-wrap {
  padding: 22px 0;
  display: flex;
  display: -webkit-flex;
}

.uni-panel {
  margin-bottom: 0px;
}

.uni-panel-h {
  border-bottom: 1px solid #e5e5e5;
}

.listTitle {
  margin-top: 10px;
  margin-bottom: 4px;
  padding-left: 15px;
  padding-right: 15px;
  color: #999999;
  font-size: 14px;
}

.uni-panel-text {
  padding-left: 10px;
}
</style>
