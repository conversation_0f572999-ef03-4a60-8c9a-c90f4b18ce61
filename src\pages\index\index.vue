<template>
	<view class="container">
		<view class="header">
			<image class="banner" src="@/static/img/<EMAIL>" />
		</view>
		<view>
		<!--#ifdef MP-ALIPAY  -->
			<contact-button tnt-inst-id="RI1_U9Ra" scene="SCE01222096" />
		<!--#endif  -->
		</view>
		<view class="nav first">
			<view class="title">门诊服务</view>
			<view class="nav_container">
				<!-- <view class="nav_item" @tap="goto(`/pages/registration/registration_department_list`)"> -->
				<view class="nav_item" @tap="goto(`/pages/register/appoint`)">
					<view class="icon">
						<image class="image" src="@/static/img/<EMAIL>" />
					</view>
					<view class="name">预约挂号</view>
				</view>
				<view class="nav_item" @tap="goto(`/pages/special/index`)">
					<!-- #ifdef MP-ALIPAY -->
					<view class="icon building">
						<image class="image" src="@/static/img/<EMAIL>" />
					</view>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<view class="icon">
						<image class="image" src="@/static/img/<EMAIL>" />
					</view>
					<!-- #endif -->
					<view class="name">特需门诊预约</view>
				</view>
				<view class="nav_item"  @tap="goto(`/pages/special/multidisciplinary`)">
					<!-- #ifdef MP-ALIPAY -->
					<view class="icon building">
						<image class="image" src="@/static/img/<EMAIL>" />
					</view>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<view class="icon">
						<image class="image" src="@/static/img/<EMAIL>" />
					</view>
					<!-- #endif -->
					<view class="name">多学科联合门诊</view>
				</view>
				<!-- <view class="nav_item" @tap="goto(`/pages/mz/index`)">
					<view class="icon">
						<image class="image" src="@/static/img/<EMAIL>" />
					</view>
					<view class="name">门诊缴费</view>
				</view> -->
				<view class="nav_item" @tap="goMzPayList">
					<view class="icon">
						<image class="image" src="@/static/img/<EMAIL>" />
					</view>
					<view class="name">门诊缴费</view>
				</view>
				
				<view class="nav_item" @click="goto('/pages/check-appoint/index')">
					<view class="icon">
						<image class="image" src="@/static/img/<EMAIL>" />
					</view>
					<view class="name">检查预约</view>
				</view>
				<view class="nav_item" @tap="goto(`/pages/ucenter/report/report`)">
					<view class="icon">
						<image class="image" src="@/static/img/<EMAIL>" />
					</view>
					<view class="name">报告查询</view>
				</view>
				<!-- <view class="nav_item" @tap="goto(`/pages/covid/index`)">
          <view class="icon">
            <image class="image" src="/static/home/<USER>" />
          </view>
          <view class="name">核酸检测</view>
        </view> -->
			</view>
		</view>
		<view class="nav">
			<view class="title">个人中心</view>
			<view class="nav-container-card">
				<view class="card-item" @tap="goto('/pages/ucenter/patient/patient')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">宝宝信息</text>
				</view>
				<view class="card-item" @tap="goto('/pages/ucenter/appoint/appoint')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">预约记录</text>
				</view>
				<view class="card-item" @tap="goto(`/pages/mz/index?type=1`)">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">我的指引</text>
				</view>
				<!-- #ifdef MP-ALIPAY -->
				<view class="card-item building">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">医保电子凭证</text>
				</view>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<view class="card-item" @click="openUrl('https://mp.weixin.qq.com/insurance/card/creditbind?from=V2GZo78y2kKrbae28lRNnw.=#wechat_direct')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">医保电子凭证</text>
				</view>
				<!-- #endif -->
			</view>
		</view>
		<view class="nav">
			<view class="title">其他服务</view>
			<view class="nav-container-card">
				<!-- #ifdef MP-ALIPAY -->
				<view class="card-item building">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">互联网医院</text>
				</view>
				<view class="card-item building">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">滇护到家</text>
				</view>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<view class="card-item">
					<wx-open-launch-weapp id="launch-btn" appid="wx0fe5bdb36eacd903"
						path="pages/home/<USER>">
						<script type="text/wxtag-template">
						<style>
							.icon > img{
								width: 155px;
								height: 55px;
								
							}
							.card_text {
								font-family: Source Han Sans CN;
								font-weight: bold;
								font-size: 14px;
								color: #4D4D4D;
								position: relative;
								top: -40px;
								left: 15px;
							}
						</style>
							<div class="icon">
								<img src="https://tertong.ynhdkc.com/assets/img/<EMAIL>">
							</div>
							<div class="card_text">互联网医院</div>
						</script>
					</wx-open-launch-weapp>
				</view>
				<view class="card-item">
					<wx-open-launch-weapp id="launch-btn" appid="wx4e90b10482e899b2"
                            path="pages/index/tenant/enter?id=92c2d796788f36fde1ea7fed236da261&oid=3087">
						<script type="text/wxtag-template">
						<style>
							.icon > img{
								width: 155px;
								height: 55px;
							}
							.card_text {
								font-family: Source Han Sans CN;
								font-weight: bold;
								font-size: 14px;
								color: #4D4D4D;
								position: relative;
								top: -40px;
								left: 15px;
							}
						</style>
							<div class="icon">
								<img src="https://tertong.ynhdkc.com/assets/img/<EMAIL>">
							</div>
							<div class="card_text">滇护到家</div>
						</script>
					</wx-open-launch-weapp>
				</view>
				<!-- #endif -->
				<view class="card-item" @tap="goto('/pages/credit/index')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">信用就医</text>
				</view>
				<view class="card-item" @tap="goto(`/pages/mz/waitList`)">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">候诊查询</text>
				</view>
			</view>
		</view>
		<view class="nav">
			<view class="title">便民服务</view>
			<view class="nav-container small">
				<view class="nav-item" @tap="goto('/pages/hos-intro/hos-intro')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">医院简介</text>
				</view>
				
				<!-- #ifdef MP-ALIPAY -->
				<view class="nav-item" @tap="goto('/pages/panorama/panorama')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">全景导航</text>
				</view>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<view class="nav-item" @tap="openUrl('https://vr.justeasy.cn/view/2377594-**********.html?from=timeline&isappinstalled=')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">全景导航</text>
				</view>
			<!-- #endif -->
				<view class="nav-item" @tap="goto('/pages/appoint-rule/appoint-rule')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">预约规则</text>
				</view>
				<view class="nav-item" @tap="goto('/pages/medical-notice/medical-notice')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">就诊须知</text>
				</view>
				<view class="nav-item" @tap="goto('/pages/medical-guide/medical-guide')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">就诊指南</text>
				</view>
				<view class="nav-item" @tap="goto('/pages/healthedu/index')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">健康教育</text>
				</view>
				<!-- #ifdef MP-ALIPAY -->
				<view class="nav-item building">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">停车缴费</text>
				</view>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<view class="nav-item" @tap="openUrl('https://kunming.alb-sz.cmbchina.com/cs/static/park/index.html?type=1&park_code=**********&id=1')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">停车缴费</text>
				</view>
				<!-- #endif -->
				<view class="nav-item" @tap="goto('/pages/news/index')">
					<image class="bg" src="@/static/img/<EMAIL>" mode="aspectFill"></image>
					<text class="name">资讯消息</text>
				</view>
			</view>
		</view>
		<view class="nav">
			<view class="title">住院服务</view>
			<view class="nav_container">
				<view class="nav_item" @tap="openUrl('https://ertong.ynhdkc.com/html/zy/index.html')">
				<view class="icon">
					<image class="image" src="/static/home/<USER>" />
				</view>
				<view class="name">住院缴费</view>
				</view>
			</view>
		</view>
	<!-- <view class="nav">
      <view class="title">服务到家</view>
      <view class="nav_container">
        <view class="nav_item" @tap="goto('/pages/healthedu/index')">
          <view class="icon">
            <image class="image" src="/static/home/<USER>" />
          </view>
          <view class="name">健康教育</view>
        </view>
        <view class="nav_item" @tap="goto('/pages/news/index')">
          <view class="icon">
            <image class="image" src="/static/home/<USER>" />
          </view>
          <view class="name">资讯消息</view>
        </view>
        <view class="nav_item" @tap="goto('/pages/guide/index')">
          <view class="icon">
            <image class="image" src="/static/home/<USER>" />
          </view>
          <view class="name">就医指南</view>
        </view>
      </view>
    </view> -->
		<!-- <view class="testButton" @click="tap">预约挂号</view> -->
		<!-- <view class="testButton" @tap="goPayment">门诊缴费</view> -->
	</view>
</template>

<script>
	import {
		createUser
	} from '@/api/waiting'
import { reqGetJsSign } from '../../api/wechat'
	export default {
		data() {
			return {}
		},
		methods: {
			goto(url) {
				console.log(url)
				uni.navigateTo({
					url,
				})
			},

			openUrl(url) {
				window.location.href = url
			},
			goMzPayList() {
				uni.showModal({
				title: '温馨提示',
				content: '我院已开通医保移动支付功能，打开支付宝，搜索“昆明市儿童医院”小程序，点击＂门诊缴费＂即可完成；也可至门诊一楼收费室人工缴费或退费。',
				showCancel: false,
				success: (res) => {
					if(res.confirm){
						// window.location.href = 'https://ertong.ynhdkc.com/html/mz/unpaylist.html'
						uni.navigateTo({
							url: '/pages/mz/index'
						})
					}
				}
			})
			},
		},
		onLoad() {
			uni.showModal({
				title: '重要通知',
				content: '医保异地就医备案温馨提示：2025 年 1 月 1 日起，参保人员离开本州（市）到省内其他州（市）或离开云南省到省外就医的，使用医保电子凭证就医需提前向参保地医保部门进行异地就医备案，方可在就医地直接结算和保障其相应医保待遇。',
				showCancel: false,
				success: (res) => {
					
				}
			})
			// if (!uni.getStorageSync('userInfo')) {
			//   //如果没有登录,则会提示
			//   my.getAuthCode({
			//     scopes: ['auth_base'],
			//     success: (res) => {
			//       let query = {
			//         code: res.authCode,
			//         grant_type: 'authorization_code',
			//       }
			//       createUser(query).then((res) => {
			//         uni.setStorageSync('userInfo', res.data.data.data)
			//       })
			//     },
			//   })
			//   return false
			// }
			var loginRes = this.checkLogin();
			console.log(loginRes)
			if (!loginRes) {
				return false;
			}
			// #ifdef MP-ALIPAY
			if (loginRes.is_certified && loginRes.is_certified === "T") {
				uni.setStorage({
					key: "is_certified",
					data: true
				})
			} else {
				uni.setStorage({
					key: "is_certified",
					data: false
				})
				uni.showModal({
					title: '提示',
					content: '您的支付宝实名信息无效或已过期，您将无法正常使用医保支付等功能',
				})
			}
			// #endif
			this.configJsSign(location.href)
		},
	}
</script>

<style lang="scss" scoped>
	.container {
		box-sizing: border-box;
		height: 100%;
		background-color: #F7F7F7;
		overflow-y: scroll;
		overflow-x: hidden;

		.header {
			box-sizing: border-box;
			display: flex;
			justify-content: center;
			align-items: center;

			.banner {
				width: 100%;
				height: 300rpx;
			}
		}
	}

	.nav {
		margin: 0 30rpx;
		margin-bottom: 30rpx;
		border-radius: 20rpx;
		background-color: #fff;
		position: relative;

		&.first {
			margin-top: -100rpx;
		}

		.title {
			font-size: 38rpx;
			color: #000;
			font-weight: bold;
			margin-bottom: 10rpx;
			padding: 40rpx 30rpx 30rpx;
		}

		.nav_container {
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;

			.nav_item {
				position: relative;
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-bottom: 30rpx;
				width: 33%;
				flex-wrap: wrap;

				.icon {
					width: 100rpx;
					height: 100rpx;

					.image {
						width: 100%;
						height: 100%;
					}
				}

				.name {
					width: 100%;
					margin-top: 19rpx;
					font-size: 32rpx;
					color: #000;
					font-weight: 600;
					text-align: center;
					white-space: nowrap;
				}

				.aliIcon {
					position: absolute;
					width: 84rpx;
					height: 28rpx;
					overflow: hidden;
					right: -10%;
					top: -10%;

					image {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
				}
			}

			:nth-child(4n) {
				margin-right: 0;
			}
		}

		.nav-container-card {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			padding: 0 30rpx 20rpx;

			.card-item {
				width: 47%;
				height: 110rpx;
				position: relative;
				margin-bottom: 30rpx;
				border-radius: 20rpx;
				overflow: hidden;

				.bg {
					width: 100%;
					height: 100%;
				}

				.name {
					font-size: 28rpx;
					color: #4D4D4D;
					position: absolute;
					top: 50%;
					left: 30rpx;
					transform: translateY(-50%);
					font-weight: bold;
				}
			}
		}

		.nav-container {
			&.small {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				padding: 0 30rpx 20rpx;

				.nav-item {
					width: 23%;
					margin-bottom: 50rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;

					.bg {
						width: 64rpx;
						height: 64rpx;
						margin-bottom: 20rpx;
					}

					.name {
						font-size: 28rpx;
					}
				}
			}
		}
	}
	.building {
		position: relative;
		overflow: hidden;
		&::before {
			font-size: 20rpx;
			content: '未开通';
			position: absolute;
			padding: 0 20rpx;
			background-color: rgba(0, 0, 0, 0.5);
			color: #fff;
			transform: rotate(45deg);
			top: 15rpx;
			right: -20rpx;
			z-index: 1;
		}
	}
</style>