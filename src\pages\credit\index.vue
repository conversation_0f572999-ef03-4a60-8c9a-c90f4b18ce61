<template>
    <view class="credit-page">
        <headerBAr title="信用就医"></headerBAr>
        <view class="patient-list">
            <view class="patient-item" v-for="(item, index) in patientList" :key="index">
                <view class="patient-item-name">
                    <text>{{ item.patient_name }}</text>
                </view>
                <view class="patient-item-content">
                    <view class="info-item">
                        <text class="info-item-label">出生日期</text>
                        <text class="info-itme-value">{{ item.patient_birthday }}</text>
                    </view>
                    <view class="info-item">
                        <text class="info-item-label">手机号</text>
                        <text class="info-itme-value">{{ item.patient_phone }}</text>
                    </view>
                    <view class="info-item">
                        <text class="info-item-label">ID号</text>
                        <text class="info-itme-value">{{ item.jz_card }}</text>
                    </view>
                </view>
                <view class="patient-item-btn">
                    <button class="btn-item" @click="goCredit(item)"
                        style="color: #E64340">信用就医签约</button>
                    <button class="btn-item" @click="changeCredit(item)"
                        style="color: #E64340">
                        {{ item.is_credit ? '关闭信用就医' : '开启信用就医' }}
                    </button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>

import headerBAr from '@/pages/component/header_bar/header_bar.vue'
import { getMypatientList } from '@/api/waiting'
import { reqChangeCredit } from '../../api/wechat'
export default {
    components: {
        headerBAr,
    },
    data() {
        return {
            patientList: [],
        }
    },
    methods: {
        getPatientList() {
            const userInfo = uni.getStorageSync('userInfo')
            getMypatientList(null, {
                userid: userInfo.user_id,
                openid: userInfo.openid,
            }).then((res) => {
                console.log(res)
                this.patientList = res.data.data
            })
        },

        goCredit(item) {
            let pat_id = item.id
            let idcard = item.patient_idcard
            if(!idcard){
                // $.alert("身份证信息不存在，无法签约，可以点击 查看详情/修改信息 补全身份信息");
                uni.showToast({
                    title: '身份证信息不存在，无法签约，请先补全身份信息',
                    icon: 'none',
                })
                return
            }

            uni.showModal({
                title: '信用签约',
                content: `您是正在使用\n身份证号:'${idcard}'\n进行信用签约`,
                cancelText: '取消',
                confirmText: '确定',
                success: function (res) {
                    if (res.confirm) {
                        uni.navigateTo({
                        url: '/pages/credit/create?id=' + pat_id,
                        })
                    }
                },
            })
        },
        changeCredit(item) {
            console.log(item)
            let pat_id = item.id
            let idcard = item.patient_idcard
            if(!idcard){
                uni.shotToast({
                    title: '身份证信息不存在，无法开启信用就医，请先补全身份信息',
                    icon: 'none',
                })

                return
            }
            reqChangeCredit({pat_id: pat_id}).then((res) => {
                console.log(res)
                if (res.data.code!= 1) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                    })
                    return
                }
                this.getPatientList()
            })
        }
    },
    onLoad() {
        this.getPatientList()
    },
}
</script>

<style lang="scss" scoped>
.credit-page {
    background-color: #F8F8F8;
    min-height: 100vh;

    .patient-list {
        padding: 50px 15px;

        .patient-item {
            border-radius: 10px;
            background-color: #fff;
            border: 1px solid #e5e5e5;
            color: #666;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            &-name {
                position: relative;
                padding: 10px 15px;
                float: left;
                margin-right: .2em;
                line-height: 2.5em;
                font-size: 1.125em;
                color: #666;

                &::after {
                    content: " ";
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    right: 0;
                    height: 1px;
                    border-bottom: 1px solid #e5e5e5;
                    color: #e5e5e5;
                    transform-origin: 0 100%;
                    transform: scaleY(0.5);
                    left: 15px;
                    right: 15px;
                }
            }

            &-content {
                padding: 5px 10px;
                font-size: .9em;
                text-align: right;
                line-height: 2;

                .info-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    &-label {
                        font-size: 1.125em;
                        color: #666;
                    }

                    &-value {
                        font-size: .9em;
                        text-align: right;
                        color: #999999;
                        line-height: 2;
                    }
                }
            }

            &-btn{
                display: flex;
                justify-content: space-between;
                align-items: center;
                position: relative;

                &::before{
                    content: " ";
                    position: absolute;
                    height: 1px;
                    top: 0;
                    border-bottom: 1px solid #e5e5e5;
                    color: #e5e5e5;
                    transform-origin: 0 100%;
                    left: 0;
                    right: 0;
                }

                .btn-item{
                    flex: 1;
                    background-color: transparent;
                    border: none;
                    border-radius: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 50px;
                    font-size: inherit;
                    &::after{
                        border: none;
                    }
                }

                .btn-item + .btn-item {
                   position: relative;

                   &::before{
                        content: " ";
                        position: absolute;
                        left: 0;
                        bottom: 0;
                        height: 100%;
                        top: 0;
                        width: 1px;
                        background-color: #e5e5e5;
                        transform-origin: 0 100%;
                    }
                }
            }

        }

        .patient-item+.patient-item {
            margin-top: 10px;
        }
    }
}
</style>