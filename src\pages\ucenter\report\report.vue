<template>
  <view>
    <view class="warntext">只支持查询三个月内检验报告，线上不支持查询检查报告</view>
    <view class="uni-list">
      <view class="selPat">
        <uni-forms>
          <uni-forms-item label="就诊人" name="patient_nation" class="pick-comm">
            <uni-data-picker
              placeholder="请选择就诊人"
              popup-title="请选择就诊人"
              :localdata="patinetList"
              v-model="selPatid"
              @change="changePat"
              @nodeclick="onnodeclickPat"
            ></uni-data-picker>
          </uni-forms-item>
        </uni-forms>
      </view>
    </view>
    <view class="head-nav">
      <view :class="navIndex == 'week' ? 'activite' : ''" @click="checkIndex('week')">本周</view>
      <view :class="navIndex == 'month' ? 'activite' : ''" @click="checkIndex('month')"
        >一个月</view
      >
      <view :class="navIndex == 'month3' ? 'activite' : ''" @click="checkIndex('month3')"
        >三个月</view
      >
    </view>
    <view class="content">
      <view class="appoint_list" v-for="(item, index) in reportList" :key="index">
        <view class="appoint_list-top">
          <view>
            {{ item.lismain_rep_name }}
          </view>
          <view class="appoint_list_badge">{{ item.report_status == 1 ? '已出' : '未出' }}</view>
        </view>
        <view class="appoint_list-cent">
          <view class="appoint_list-cent-item">
            <view>报告时间</view>
            <view>{{ item.lismain_secondaudit_time }}</view>
          </view>
          <view class="appoint_list-cent-item">
            <view>患者姓名</view>
            <view>{{ new Array(item.patient_name.length).join('*') + item.patient_name.substr(-1)}} </view>
          </view>
          <view>
            <button
              style="color: rgb(238, 102, 138)"
              v-if="item.report_status == 1"
              @tap="
                goto(
                  `/pages/ucenter/report/detail?report_id=${item.lismain_repno}&pat_id=${item.patient_id}&report_name=${item.lismain_rep_name}&report_time=${item.lismain_secondaudit_time}`
                )
              "
              >查看详情</button
            >
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { getMypatientList, getReportList,refreshToken} from '@/api/waiting.js'
  import { reportCmPV } from '@/to/cloudMonitorHelper'
  export default {
    mounted() {},
    onInit() {},
    onLoad(query) {
      reportCmPV({ title: '检查检验报告查询', query })
      let userInfo = uni.getStorageSync('userInfo')
      let that = this
      // #ifdef MP-ALIPAY
      my.getAuthCode({
        scopes: ['mfrstre'],
        success: (res) => {
          console.log(res)
          if (res.authCode) {
            uni.showLoading({
              title: '加载中',
            })
            let data = {
              grant_type: 'authorization_code',
              code: res.authCode,
            }
            refreshToken(data).then((Tres) => {
              console.log(Tres)
              if (Tres.data.code == 1) {
                uni.setStorageSync('mfrstreCode', Tres.data.data.data.access_token)
                uni.hideLoading()
              }
            })
          }
        },
        fail: (res) => {
        },
      })
      // #endif
    },
    onShow (){
      this.getPatinets()
    },
    data() {
      return {
        navIndex: 'week',
        selString: '',
        selPatid: 0,
        patinetList: [],
        reportList: [],
      }
    },
    methods: {
      changePat(e) {},
      onnodeclickPat(e) {
        this.getList()
      },
      async getPatinets() {
        /* uni.setStorageSync('userInfo', {
          id: 1917921,
          openid: 'o9U-FtzV8TAwXujFqZcOvgDfYPTI',
          userid: 1917921,
        }) */
        const userInfo = uni.getStorageSync('userInfo')
        const { statusCode, data } = await getMypatientList(null, {
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '暂无数据',
            icon: 'none',
          })
          return
        }
        if (!data.data.length) {
          uni.showModal({
            title: '提示',
            content: '请选择就诊人，如还没有就诊卡请新建卡，如已经有就诊卡请绑定就诊卡',
            cancelText: '去绑定卡',
            confirmText: '去新建卡',
            success: function (res) {
              if (res.confirm) {
                //console.log('用户点击去新建卡')
                uni.navigateTo({
                  url: '/pages/ucenter/patient/add',
                })
              } else if (res.cancel) {
                //console.log('用户点击去绑定卡')
                uni.navigateTo({
                  url: '/pages/ucenter/patient/bind',
                })
              }
            },
          })
          return
        }
        data.data.forEach((element) => {
          // #ifdef MP-ALIPAY
          element.text = new Array(element.patient_name.length).join('*') + element.patient_name.substr(-1)
          // #endif
          // #ifdef H5
          element.text = element.patient_name
          // #endif
          element.value = element.id
        })

        this.patinetList = data.data
      },
      goto(url) {
        uni.navigateTo({
          url,
        })
      },
      checkIndex(index) {
        this.navIndex = index
        this.getList()
      },
      async getList() {
        /* uni.setStorageSync('userInfo', {
          id: 3,
          openid: 'o9U-Ft4X8xUVM3Sg9WqJX6H-qZhk',
          userid: 3,
        }) */
        const userInfo = uni.getStorageSync('userInfo')
        if (this.selPatid == 0) {
          uni.showToast({
            title: '请选择就诊人',
            duration: 2000,
            icon: 'none',
          })
          return
        }

        let postData = {
          datatype: this.navIndex,
          pat_id: this.selPatid,
          userid: userInfo.user_id,
          openid: userInfo.openid,
        }
        const { statusCode, data } = await getReportList(null, postData)
        if (statusCode != 200) {
          uni.showToast({
            title: '暂无数据',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        if (!data.data) {
          uni.showToast({
            title: '暂无数据',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        this.reportList = data.data
        // console.log(this.reportList)
      },
      patinetChange: function (e) {
        // 选中后会触发这个回调函数 e.target.value是你选中的下拉菜单数组的索引
        const index = e.detail.value
        this.selString = this.patinetList[index].patient_name
        this.selPatid = this.patinetList[index].id
        // console.log(this.selPatid)
        this.getList()
      },
    },
  }
</script>

<style scoped>
  .selPat {
    margin-top: 15px;
    padding: 0 15px;
  }
  .warntext {
    color: red;
    font-size: small;
    line-height: 1.6;
    padding: 5px;
    border-bottom: 1px solid #e5e5e5;
  }
  .head-nav {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    z-index: 500;
    top: 0;
    width: 100%;
    background-color: #fafafa;
  }
  .head-nav > view {
    position: relative;
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    padding: 7px 0;
    text-align: center;
    font-size: 15px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    border-right: 1px solid #cccccc;
    color: #888;
  }
  .head-nav > view:last-child {
    border-right: none;
  }

  .head-nav > view.activite {
    background-color: white;
    border-bottom: 2px solid rgb(238, 102, 138);
    color: rgb(238, 102, 138);
  }
  .content {
    /* background: #008000; */
    height: 100%;
    padding: 0 15px;
  }

  .appoint_list {
    border-radius: 10px;
    border: 1px solid #e5e5e5;
    margin-top: 10px;

    background-color: #ffffff;
    padding: 10px 15px;
  }
  .appoint_list-top,
  .appoint_list-cent-item {
    display: flex;
    display: -webkit-flex;
    -webkit-align-items: center;
    justify-content: space-between;
    line-height: 2.5em;
    font-size: 1.125em;
  }
  .appoint_list_badge {
    padding: 0.15em 0.4em;
    min-width: 8px;
    background-color: #ee668a;
    border-radius: 5px;
    color: #ffffff;
    line-height: 1.2;
    text-align: center;
    font-size: 12px;
  }
  .appoint_list-top {
    border-bottom: 1px solid #e5e5e5;
  }
</style>
