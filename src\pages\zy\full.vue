<template>
  <view class="container">
    <view class="header"> </view>
    <view class="body">
      <view class="info">
        <uni-list>
          <uni-list-item title="住院号" :rightText="detailInfo.InpatientNO" />
          <uni-list-item title="患者ID号" :rightText="detailInfo.jz_card" />
          <uni-list-item title="预交金额" :rightText="detailInfo.DepositAmount" />
          <uni-list-item title="总费用" :rightText="detailInfo.ChargeTotal" />
          <uni-list-item title="预交金余额" :rightText="detailInfo.balance + ''" />
        </uni-list>
      </view>

      <view class="sel-price">
        <uni-group title="您可以选择以下金额快速充值" margin-top="20">
          <view class="my-flex" v-for="(item, index) in priceArr" :key="index">
            <view class="my-flex-item" v-for="(it, ix) in item" :key="ix">
              <view
                class="my-btn-flex"
                @click="changePrice(it)"
                :class="selPrice == it ? 'active' : ''"
                v-if="it != 0"
                >{{ it }}元</view
              >
            </view>
          </view>
        </uni-group>
      </view>

      <view class="btn-submit" @click="submitCharge">
        <button class="my-btn-sub">充值</button>
      </view>
    </view>
    <view class="footer"> </view>
    <view id="insertMolian" style="display: none"></view>
  </view>
</template>
<script>
// import { defineComponent } from '@vue/composition-api'
import { getDetailZy, reqCreateOrderZy } from '@/api/ucenter.js'
export default {
  setup() {},
  onLoad(option) {
    //option为object类型，会序列化上个页⾯传递的参数
    this.queryParams = {
      pat_id: option.pat_id,
      inpatientno: option.inpatientno,
      admisstimes: option.admisstimes,
    }
    this.getDetailInfo(option.pat_id, option.inpatientno, option.admisstimes)
  },
  data() {
    return {
      detailInfo: {},
      queryParams: {
        pat_id: '',
        inpatientno: '',
        admisstimes: '',
      },
      priceArr: [
        [1000, 2000, 3000],
        [4000, 5000, 6000],
        [7000, 8000, 0],
      ],
      selPrice: 1000,
    }
  },
  methods: {
    changePrice(price) {
      this.selPrice = price
    },
    async getDetailInfo(pat_id, inpatientno, admisstimes) {
      const userInfo = uni.getStorageSync('userInfo')
      const { statusCode, data } = await getDetailZy(null, {
        userid: userInfo.user_id,
        openid: userInfo.openid,
        pat_id: pat_id,
        inpatientno: inpatientno,
        admisstimes: admisstimes,
      })
      if (statusCode != 200) {
        uni.showToast({
          title: '网络错误',
          icon: 'none',
        })
        return
      }
      if (data.code != 1) {
        uni.showToast({
          title: '暂无数据',
          icon: 'none',
        })
        return
      }
      this.detailInfo = data.data
      console.log(this.detailInfo)
    },
    submitCharge() {
      if (this.selPrice == 0) {
        uni.showToast({
          title: '请选择充值金额',
          icon: 'none',
        })
        return
      }     
      let fulldata = {
        pat_id: this.queryParams.pat_id,
        inpatientno: this.queryParams.inpatientno,
        admisstimes: this.queryParams.admisstimes,
        recharge: this.selPrice
      };
      console.log(fulldata)
      reqCreateOrderZy(fulldata).then((res) => {
        console.log(res)
          if (res.data.code != 1) {
              uni.showToast({
                  title: res.data.msg,
                  icon: 'none',
              })
          } else {
              this.molianPay(res.data.data);
          }
      });
    },
    molianPay(query) {
      let form = document.createElement('form');
      form.setAttribute('method','POST');
      form.setAttribute('id','goMolian');
      form.setAttribute('action','http://ertongpay.etyy.cn/MuWapPaymentsV3.aspx');

      let MerBillNo = document.createElement('input');
      MerBillNo.setAttribute('name','MerBillNo');
      MerBillNo.setAttribute('value',query.OrderNo);
      form.appendChild(MerBillNo);

      let UserId = document.createElement('input');
      UserId.setAttribute('name','UserId');
      UserId.setAttribute('value',pat_id);
      form.appendChild(UserId);

      let ValidTime = document.createElement('input');
      ValidTime.setAttribute('name','ValidTime');
      ValidTime.setAttribute('value',query.ValidTime);
      form.appendChild(ValidTime);

      let MerNo = document.createElement('input');
      MerNo.setAttribute('name','MerNo');
      MerNo.setAttribute('value',query.MerNo);
      form.appendChild(MerNo);

      let ProductId = document.createElement('input');
      ProductId.setAttribute('name','ProductId');
      ProductId.setAttribute('value',"-");
      form.appendChild(ProductId);

      let ProductName = document.createElement('input');
      ProductName.setAttribute('name','ProductName');
      ProductName.setAttribute('value',"充值");
      form.appendChild(ProductName);

      let OrderDate = document.createElement('input');
      OrderDate.setAttribute('name','OrderDate');
      OrderDate.setAttribute('value',query.OrderDate);
      form.appendChild(OrderDate);

      let OrderAmount = document.createElement('input');
      OrderAmount.setAttribute('name','OrderAmount');
      OrderAmount.setAttribute('value',query.OrderAmount);
      form.appendChild(OrderAmount);

      let OrdSourceIp = document.createElement('input');
      OrdSourceIp.setAttribute('name','OrdSourceIp');
      OrdSourceIp.setAttribute('value',"");
      form.appendChild(OrdSourceIp);

      let CurrencyType = document.createElement('input');
      CurrencyType.setAttribute('name','CurrencyType');
      CurrencyType.setAttribute('value',"RMB");
      form.appendChild(CurrencyType);

      let PayAppCode = document.createElement('input');
      PayAppCode.setAttribute('name','PayAppCode');
      PayAppCode.setAttribute('value',"04");
      form.appendChild(PayAppCode);

      let PaySourceCode = document.createElement('input');
      PaySourceCode.setAttribute('name','PaySourceCode');
      PaySourceCode.setAttribute('value',"04");
      form.appendChild(PaySourceCode);

      let PayAccount = document.createElement('input');
      PayAccount.setAttribute('name','PayAccount');
      PayAccount.setAttribute('value',query.MerNo);
      form.appendChild(PayAccount);

      let SignMD5 = document.createElement('input');
      SignMD5.setAttribute('name','SignMD5');
      SignMD5.setAttribute('value',query.SignMD5);
      form.appendChild(SignMD5);

      let url = window.location.protocol + "//" + window.location.host + "/ui/pages/zy/chargeDetail?id="+query.order_id;
      let Merchanturl = document.createElement('input');
      Merchanturl.setAttribute('name','Merchanturl');
      Merchanturl.setAttribute('value',url);
      form.appendChild(Merchanturl);

      let S2SMerchanturl = document.createElement('input');
      S2SMerchanturl.setAttribute('name','S2SMerchanturl');
      S2SMerchanturl.setAttribute('value',query.S2SMerchanturl);
      form.appendChild(S2SMerchanturl);

      let openidInput = document.createElement('input');
      openidInput.setAttribute('name','openid');
      openidInput.setAttribute('value',query.openid);
      form.appendChild(openidInput);

      let elPay = document.getElementById('insertMolian');
      elPay.append(form);
      let formEl = document.getElementById('goMolian');
      formEl.submit();
    }
  },
}
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
  overflow: auto;
}
.body {
  position: relative;
  .my-flex {
    display: flex;
    margin-top: 10px;
  }
  .my-flex-item {
    flex: 1;
    text-align: center;
  }
  .my-btn-flex {
    text-align: center;
    height: 32px;
    line-height: 32px;
    border: 1px solid rgb(1, 181, 206);
    width: 80%;
    margin: 0 auto;
  }
  .my-btn-flex.active {
    color: white;
    background-color: rgb(1, 181, 206);
  }

  .btn-submit{
    padding: 15px;
  }
  .my-btn-sub{
    background-color: rgb(238,102,138);
    color: #FFFFFF;
  }
}
</style>
