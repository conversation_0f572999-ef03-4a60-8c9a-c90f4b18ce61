<template>
  <view>
    <view class="patient_scroll">
      <view class="appoint_list" v-for="(item, index) in patientList" :key="index">
        <view
          class="appoint_list-info"
          @click="goto(`/pages/ucenter/patient/detail?id=${item.id}`)"
        >
          <view class="appoint_list-top">
            <view class="fl patient_name">{{ new Array(item.patient_name.length).join('*') + item.patient_name.substr(-1) }}</view>
            <view class="fl patient_type">[{{ item.patient_type_text }}]</view>
            <view class="fl health_card" v-if="item.tx_health_card">有电子健康卡</view>
            <view class="fr default_car" v-if="item.is_default">默认</view>
          </view>
          <view class="appoint_list-cent">
            <view class="appoint_list-cent-item">
              <view class="patient_info">出生日期</view>
              <view class="patient_infoText">{{ item.patient_birthday }}</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">手机号</view>
              <view class="patient_infoText"
                >{{ item.patient_phone.substr(0, 3) }}******{{ item.patient_phone.substr(9) }}</view
              >
            </view>
            <view class="appoint_list-cent-item"  v-if="item.patient_idcard">
              <view class="patient_info">身份证号</view>
              <view class="patient_infoText"
                >{{ item.patient_idcard.substr(0, 1) }}*************{{
                  item.patient_idcard.substr(item.patient_idcard.length-1,1)
                }}</view
              >
            </view>
          </view>
        </view>
        <view class="appoint_list-but">
          <view
            class="dp-f-1 appoint_but"
            @tap="goto(`/pages/ucenter/patient/detail?id=${item.id}`)"
            >查看详情</view
          >
          <view class="dp-f-1 appoint_but fc-ee668a" @click="setDefault(item.id)">默认就诊人</view>
          <view class="dp-f-1 appoint_but" @click="unBind(item.id)">解绑</view>
        </view>
      </view>
    </view>
    <view class="uni-flex uni-row footer-buttons">
      <view class="flex-item" style="margin-right: 20px">
        <button
          type="default"
          class="redcolor"
          plain="true"
          @click="goto('/pages/ucenter/patient/bind')"
          >绑定就诊卡</button
        >
      </view>
      <view class="flex-item">
        <button type="primary" class="pinkcolor" plain="true" @click="gotoAddPatient">
          新建档案
        </button>
      </view>
    </view>

    <!-- pop -->
    <uni-popup ref="popup" type="dialog">
      <uni-popup-dialog
        mode="base"
        title="常规同意告知书"
        :duration="2000"
        :before-close="true"
        @close="closeNoticeTxt"
        @confirm="confirmNoticeTxt"
      >
        <rich-text class="notic-content" :nodes="noticeArr"></rich-text>
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
  import { getMypatientList, setDefaultPatient, unBindPatient } from '@/api/waiting.js'
  import { noticeTxt } from './data.js'
  import parse from 'mini-html-parser2'
  export default {
    /* mounted() {
    this.getPatinets();
  }, */
    onShow() {
      this.getPatinets()
    },
    data() {
      return {
        noticeTxt: noticeTxt,
        noticeArr: [],
        patientList: [],
      }
    },
    methods: {
      unBind(pat_id) {
        let that = this
        uni.showModal({
          title: '危险操作',
          content: '您确定要解绑该就诊人吗？',
          success: async function (res) {
            if (res.confirm) {
              /* uni.setStorageSync('userInfo', {
              id: 3,
              openid: 'o9U-Ft4X8xUVM3Sg9WqJX6H-qZhk',
              userid: 3,
            }) */
              const userInfo = uni.getStorageSync('userInfo')

              const { statusCode, data } = await unBindPatient(null, {
                pat_id: pat_id,
                userid: userInfo.user_id,
                openid: userInfo.openid,
              })
              if (statusCode != 200) {
                uni.showToast({
                  title: '网络超时',
                  duration: 2000,
                  icon: 'none',
                })
                return
              }
              if (data.code == 1) {
                that.getPatinets()
              } else {
                uni.showToast({
                  title: '操作失败',
                  duration: 2000,
                  icon: 'none',
                })
                return
              }
            } else if (res.cancel) {
              return
            }
          },
        })
      },
      async setDefault(pat_id) {
        /* uni.setStorageSync('userInfo', {
        id: 3,
        openid: 'o9U-Ft4X8xUVM3Sg9WqJX6H-qZhk',
        userid: 3,
      }) */
        const userInfo = uni.getStorageSync('userInfo')

        const { statusCode, data } = await setDefaultPatient(null, {
          pat_id: pat_id,
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '网络超时',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        if (data.code == 1) {
          this.getPatinets()
        } else {
          uni.showToast({
            title: '设置失败',
            duration: 2000,
            icon: 'none',
          })
          return
        }
      },
      async getPatinets() {
        /* uni.setStorageSync('userInfo', {
        id: 3,
        openid: 'o9U-Ft4X8xUVM3Sg9WqJX6H-qZhk',
        userid: 3,
      }) */
        const userInfo = uni.getStorageSync('userInfo')

        const { statusCode, data } = await getMypatientList(null, {
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '暂无数据',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        data.data.forEach((element) => {
          if (element.patient_type == 0) {
            element.patient_type_text = '儿童'
          } else {
            element.patient_type_text = '成人'
          }
          if (element.patient_idcard && element.patient_idcard == userInfo.cert_no) {
            element.patient_type_text = '本人'
          }
        })

        this.patientList = data.data
      },
      goto(url) {
        uni.navigateTo({
          url,
        })
      },
      toDetail(panel, id) {
        const url = '/pages/component/patient/detail?id=' + id
        if (this.hasLeftWin) {
          uni.reLaunch({
            url: url,
          })
        } else {
          uni.navigateTo({
            url: url,

            success(res) {
              // console.log('成功啦', res)
            },
            fail(err) {
              // console.log('失败啦', err)
            },
          })
        }
      },
      gotoAddPatient() {
        parse(this.noticeTxt, (err, nodesList) => {
          if (!err) {
            this.noticeArr = nodesList
          }
        })
        this.$refs.popup.open()
        // goto('/pages/ucenter/patient/add')
      },
      closeNoticeTxt() {
        this.$refs.popup.close()
      },
      confirmNoticeTxt() {
        this.$refs.popup.close()
        this.goto('/pages/ucenter/patient/add')
      },
      goto(url) {
        uni.navigateTo({
          url: url,
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .notic-content {
    height: 250px;
    overflow: auto;
  }
  .redcolor {
    background-color: #e64340;
    color: #fff;
    border: none;
    border-radius: 5px;
  }
  .pinkcolor {
    background-color: rgb(238, 102, 138);
    color: #fff;
    border: none;
    border-radius: 5px;
  }
  .uni-flex {
    display: flex;
    margin: 0 0 20px 0;
    padding: 15px;
  }
  .flex-item {
    flex: 1;
  }
  .uni-row {
    flex-direction: row;
  }
  .patient_scroll {
    padding: 15px;
  }
  .appoint_list {
    border-radius: 10px;
    margin-top: 10px;
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
  }
  .appoint_list-info {
    padding: 10px 15px;
  }
  .appoint_list-cent-item {
    display: flex;
    display: -webkit-flex;
    -webkit-align-items: center;
    justify-content: space-between;
    font-size: 0.9em;
    line-height: 2;
  }
  .appoint_list-top {
    border-bottom: 1px solid #e5e5e5;
    overflow: hidden;
    padding: 10px 0;
  }
  .patient_name {
    margin-right: 0.2em;
    line-height: 40px;
    font-size: 18px;
    color: #666;
  }
  .patient_type {
    color: #666;
    font-size: 0.8125em;
    margin-top: 16px;
    margin-right: 1em;
  }
  .health_card {
    color: #aaa;
    font-size: 0.8125em;
    margin-top: 3%;
    border: 1px solid #aaa;
    padding: 0 12px;
    border-radius: 2px;
  }
  .patient_info {
    color: #aaa;
    font-size: 0.8125em;
    font-weight: 400;
  }
  .patient_infoText {
    text-align: right;
    color: #999999;
  }
  .fl {
    float: left;
  }
  .fr {
    float: right;
  }
  .dp-f-1 {
    flex: 1;
  }
  .appoint_list-but {
    line-height: 50px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    border-top: 1px solid #d5d5d6;
  }
  .appoint_but {
    text-align: center;
    border-left: 1px solid #d5d5d6;
    color: #999999;
    font-size: 16px;
  }
  .appoint_list-but .dp-f-1:first-child {
    border: none;
  }
  .fc-ee668a {
    color: rgb(238, 102, 138);
  }
  .uni-common-mt {
    padding-bottom: 20px;
  }
</style>
