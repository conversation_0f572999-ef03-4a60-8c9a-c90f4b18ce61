//import service from './storage'

let isTest = /(tertong|localhost|192\.168)/.test(window.location.hostname)
let baseUrl = ''
console.log('isTest', isTest) 
if (isTest) {
  baseUrl = 'https://tertong.ynhdkc.com/api'
} else {
  baseUrl = 'https://ertong.ynhdkc.com/api'
}

// const baseUrl = 'https://ertong.ynhdkc.com/api'
// const baseUrl = `https://ych-witmed-api.ynhdkc.com`

function request(url, data, method = 'get', contentType = 1, params) {
  if (params.showLoading) {
    uni.showLoading({
      //#ifdef H5
      mask: true,
      //#endif
      title: params.showLoadingTitle ? params.showLoadingTitle : '加载中...',
    })
  }
  //const TOKEN = service.getAccessToken() ? JSON.parse(service.getAccessToken())?.data : ''
  const header = {
    'content-type':
      contentType === 1 ? 'application/json;charset=utf-8' : 'application/x-www-form-urlencoded',
    //Authorization: TOKEN ? 'Bearer ' + TOKEN : '',
  }
  for (const property in data) {
    if (data[property] == null) {
      delete data[property]
    }
  }

  return new Promise((resolve, reject) => {
    uni.request({
      url: baseUrl + url,
      data,
      method,
      header,
      success: (res) => {
        //token过期,重新授权
        if (res.statusCode === 401) {
          // uni.showToast({
          //   title: 'TOKEN过期，重新获取授权',
          //   icon: 'none',
          // })
          setTimeout(function () {
            //service.removeAccessToken()
            // #ifdef H5
            window.location.reload()
            // #endif
            uni.reLaunch({
              url: '/pages/index/index',
            })
          }, 2000)
        } else if(res.statusCode === 404){
          uni.showToast({
            title: '请求的资源不存在',
            icon: 'none',
          })
        } else if (res.statusCode === 200) {
          resolve(res)
        } else {
          // reject(res)
          uni.showToast({
            title: '系统异常，请稍后再试',
            icon: 'none',
          })
        }
      },
      fail: (err) => {
        if (err.statusCode === 401) {
          // uni.showToast({
          //   title: 'TOKEN过期，重新获取授权',
          //   icon: 'none',
          // })
          console.log('TOKEN过期，重新获取授权')
          uni.removeStorageSync('userInfo')
          setTimeout(function () {
            //service.removeAccessToken()
            // #ifdef H5
            window.location.reload()
            // #endif
            uni.reLaunch({
              url: '/pages/index/index',
            })
          }, 2000)
        } else if (err.statusCode === 200) {
          resolve(err)
        } else {
          // reject(err)
          uni.showToast({
            title: '系统异常，请稍后再试',
            icon: 'none',
          })
        }
        // reject(err)
      },
      complete: () => {
        if (params.showLoading) {
          uni.hideLoading()
        }
      },
    })
  })
}
export function get(url, data, params) {
  return request(url, data, 'GET', 1, params)
}
export function post(url, data, params) {
  return request(url, data, 'POST', 2, params)
}
export function put(url, data, params) {
  return request(url, data, 'PUT', 1, params)
}
export function deletes(url, data, params) {
  return request(url, data, 'DELETE', 1, params)
}
