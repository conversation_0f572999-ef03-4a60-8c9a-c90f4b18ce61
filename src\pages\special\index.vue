<template>
	<view class="special-page">
		<headerBAr title="特需门诊"></headerBAr>

		<view class="dep-list">
			<view class="dep-item" @click="goDoc(item)" v-for="(item, index) in specialList" :key="index">
				<view class="title">{{ item.department_name }}</view>
				<view class="btn">预约</view>
			</view>
		</view>

		<uni-popup ref="tipPopup" type="center" :animation="false">
			<view class="popup-content">
				<view class="popup-title">温馨提示</view>
				<view class="popup-scroll" >
					<view class="popup-text">
						<view v-html="departmentNotify"></view>
					</view>
				</view>
				<view class="popup-btns">
					<view class="popup-btn" @click="closeTipPopup">取消</view>
					<view class="popup-btn primary" @click="confirmTipPopup">确认</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>

import headerBAr from '@/pages/component/header_bar/header_bar.vue'
import { reqSpecialDepList } from '@/api/wechat'
export default {
	components: {
		headerBAr,
	},
	data() {
		return {
			specialList: [],
			departmentNotify: '',
			itemInfo: {},
		}
	},
	methods: {
		goDoc(item) {
			this.itemInfo = item
			this.departmentNotify = item.department_notify
			if(this.departmentNotify){
				this.$refs.tipPopup.open()
			}else{
				uni.navigateTo({
					url: '/pages/register/doctorList?hos_code=' + item.hospital_code + '&dep_id=' + item.department_code,
				})
			}
		},
		closeTipPopup(type) {
			this.$refs.tipPopup.close();
		},
		confirmTipPopup() {
			this.$refs.tipPopup.close();
			uni.navigateTo({
					url: '/pages/register/doctorList?hos_code=' + this.itemInfo.hospital_code 
					+ '&dep_id=' + this.itemInfo.department_code,
				})
		},
		getSpecialList() {
			reqSpecialDepList({
				type: 1
			}).then((res) => {
				console.log(res)
				this.specialList = res.data.data
			})
		},
	},
	onLoad() {
		console.log('特需门诊')
		this.getSpecialList()
	},
}
</script>

<style lang="scss" scoped>
.special-page {
	background-color: #F8F8F8;
	min-height: 100vh;

	.dep-list {
		padding:60px 20px;

		.dep-item {
          border-radius: 8px;
          background-color: #fff;
          padding: 30px 20px;
          box-shadow: 0 3px 10px #E6E6E6;
          display: flex;
          justify-content: space-between;

			.title{
			font-size: 18px;
			}
			.btn{
			padding: 2px 8px;
			font-size: 14px;
			border-radius: 8px;
			color: #EE668A;
			border: 1px #EE668A solid;
			}
		
		}

		.dep-item + .dep-item {
			margin-top: 20px;
		}
	}

	.popup-content{
		margin: 0 auto;
		background-color: #FFF;
		border-radius: 32rpx;
		overflow: hidden;
		width: 80%;

		.popup-title{
			padding: 1.3em 1.6em 0.5em;
			font-size: 30rpx;
			line-height: 1.3;
			word-wrap: break-word;
			word-break: break-all;
			font-weight: 600;
			color: #333;
			text-align: left;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			

			&::after{
				content: '';
				position: absolute;
				display: block;
				width: 100%;
				height: 1rpx;
				bottom: 0;
				left: 0;
				background-color: #eee;
			}
		}

		.popup-scroll{
			max-height: 300px;
			min-height: 80px;
			overflow-y: scroll;
		}
		.popup-text{
			display: flex;
			flex-direction: column;
			padding: 24rpx;
			font-size: 28rpx;
			line-height: 1.8em;
			overflow: scroll;
		}

		.popup-btns{
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 96rpx;
			border-top: 1rpx solid #eee;

			.popup-btn{
				flex: 1;
				text-align: center;
				line-height: 96rpx;
				font-size: 36rpx;
				color: #5f646e;
			}

			.primary{
				color: #fff;
				background-color: rgb(238,102,138);
				margin-bottom: -.1em;
			}


			.disabled{
				color: #fff;
				background-color: #D7D7D7;
			}
		}
	}
}
</style>