<template>
  <view>
    <view class="notice">
      <view class="title">昆明市儿童医院</view>
      <view class="title">“新型冠状病毒肺炎”流行病学史承诺书</view>
      <view class="desc">患者姓名 {{ new Array(userinfo.patient_name.length).join('*') + userinfo.patient_name.substr(-1)}} 出生日期（8位数） {{ userinfo.patient_birthday }}</view>
      <view class="desc">性别 {{ userinfo.patient_sex == 1 ? "男" : "女" }} ID号或住院号 {{ userinfo.jz_card }}</view>
      <view class="notice-title">尊敬的患者及患者家属，您好！</view>
      <view class="notice-text">当前，我国新冠肺炎疫情防控工作进入新的阶段。为实现“外防输入、内防反弹”的目标，按照《关于进一步巩固成果提高医疗机构新冠肺炎防控和救治能力的通知》（联防联控机制综发〔2020〕141号）、《关于进一步加强医疗机构管理及感控工作的通知》（云卫办医发〔2020〕30号）、《云南省卫生健康委办公室关于进一步加强冬春季新冠肺炎医疗救治有关工作的通知》的精神，切实落实“应检尽检，愿检尽检”政策及“外防输入，内防反弹”的防疫工作新要求，根据中央应对新冠肺炎疫情工作领导小组最新会议精神要求，除了要对重点人群实行“应检尽检”外，对有检测要求的人员实行“愿检尽检”。</view>
      <view class="notice-text">我院作为云南省批准开展新冠核酸检测的医疗机构，我们真诚地建议您和您的孩子进行新冠病毒核酸检测，我院将给予您专业的医学检验支持和医疗支持。</view>
      <view class="notice-title">重要提示：</view>
      <view class="notice-text">应检尽检、愿检尽检的患者均需要在病历系统及就诊患者信息登记表上做好个人信息记录和转归去向登记，并签字承诺做好个人防护，24小时保持通讯畅通，确保第一时间可以将有异常的检测结果通知到患者家属。</view>
      <view class="notice-text">请患者家属准确提供在昆的居住地址（或暂住地址）、两个联系人和两个有效联系电话。</view>
      <view class="uni-form-item uni-column relation">
				<view class="title">在昆明居住地址</view>
				<input class="uni-input" type="text" v-model="address_now"/>
			</view>
      <view class="uni-form-item uni-column relation">
				<view class="title">联系人1</view>
				<input class="uni-input" type="text" v-model="translates1"/>
			</view>
      <view class="uni-form-item uni-column relation">
				<view class="title">联系人2</view>
				<input class="uni-input" type="text" v-model="translates2"/>
			</view>
      <view class="uni-form-item uni-column relation">
				<view class="title">联系电话1</view>
				<input class="uni-input" type="number" v-model="mobiles1"/>
			</view>
      <view class="uni-form-item uni-column relation">
				<view class="title">联系电话2</view>
				<input class="uni-input" type="number" v-model="mobiles2"/>
			</view>
      <view class="notice-title">患者家属意见：</view>
      <view class="notice-text">我已详细阅读以上内容，对医师的告知表示完全理解。经慎重考虑，我决定：</view>
      <view class="uni-form-item uni-column relation">
				<view class="title">与患者关系</view>
				<input class="uni-input" maxlength="10" v-model="relation" type="text" placeholder="本人,父子,父女,母子,母女..." />
			</view>
    </view>
    <view class="uni-padding-wrap uni-common-mt">
      <button type="primary" class="signBtn" @click="signBtn">同意并签名确认</button>
      <button @click="backto">不同意</button>
    </view>
  </view>     
</template>

<script>
import { getById,savePatientCovid19Notice } from '@/api/waiting'
  export default {
    data() {
			return {
        address_now:"",
        translates1:"",
        translates2:"",
        mobiles1:"",
        mobiles2:"",
        relation:"",//和本人关系
        userinfo:{},
				signImg: ""
			}
		},
    onLoad: function (option) {
      let query = {
          userid: uni.getStorageSync('userInfo').userid,
          openid: uni.getStorageSync('userInfo').openid,
          patientid: option.id,
        }
        getById(query).then((res) => {
          this.userinfo = res.data.data
        })
      
    },
		onShow() {
			let that = this
      if(uni.getStorageSync('name_sign')){
        that.signImg = uni.getStorageSync('name_sign')
        that.saveNotice()
      }
			// uni.$on('signData',function(data){
			// 	that.signImg = data
      //   if(data.covd){
      //     that.saveNotice()
      //   }
			// })
		},
    methods: {
      saveNotice(){
        let query = {
            address_now:this.address_now,
            translates: this.translates1+','+ this.translates2+'',
            mobiles: this.mobiles1+','+ this.mobiles2+'',
            relation:this.relation,
            name_sign:this.signImg,
            jz_card:  this.userinfo.jz_card,
            patient_id:  this.userinfo.id,
            user_id: uni.getStorageSync('userInfo').userid,
          }
          savePatientCovid19Notice(query).then((res) => {
            if (res.data.code == 1) {
            } else {
              uni.showToast({
                title: res.data.msg,
                icon: 'none',
              })
            }
          })
      },
      signBtn() {
        if (!this.address_now) {
          uni.showToast({
            title: '现居地地址信息缺失',
            duration: 2000,
            icon: 'none',
          })
            return;
        }
        if (!this.translates1) {
          uni.showToast({
            title: '联系人1信息缺失',
            duration: 2000,
            icon: 'none',
          })
            return;
        }
        if (!this.translates2) {
          uni.showToast({
            title: '联系人2信息缺失',
            duration: 2000,
            icon: 'none',
          })
            return;
        }

        if (!this.mobiles1) {
          uni.showToast({
            title: '联系电话1信息缺失',
            duration: 2000,
            icon: 'none',
          })
            return;
        }
        if (!(/^1[3456789]\d{9}$/.test(this.mobiles1))) {
          uni.showToast({
            title: '联系电话1格式不正确',
            duration: 2000,
            icon: 'none',
          })
            return;
        }
        if (!this.mobiles2) {
          uni.showToast({
            title: '联系电话2信息缺失',
            duration: 2000,
            icon: 'none',
          })
            return;
        }
        if (!(/^1[3456789]\d{9}$/.test(this.mobiles2))) {
          uni.showToast({
            title: '联系电话2格式不正确',
            duration: 2000,
            icon: 'none',
          })
            return;
        }
        if (!this.relation) {
          uni.showToast({
            title: '与患者关系缺失',
            duration: 2000,
            icon: 'none',
          })
            return;
        }
        uni.navigateTo({
          url: '/pages/signature/signature',
          animationType: 'slide-in-bottom',
          animationDuration: 200,
        })
        // if(this.relation){
        //   uni.navigateTo({
        //     url: '/pages/signature/signature',
        //     animationType: 'slide-in-bottom',
        //     animationDuration: 200,
        //   })
        // }else{
        //   uni.showToast({
        //     title: '与患者关系缺失',
        //     duration: 2000,
        //     icon: 'none',
        //   })
        // }
        
      },
      backto(){
        uni.navigateBack({
          delta: 1
        });
      },
    },
  }
</script>
<style lang="scss" scoped>
  .uni-column{
    background: #fff;
  }
  .notice {
    text-align: center;
    color: #3d4145;
    padding: 5px;
    .title {
      margin-bottom: 5px;
      font-weight: 400;
      font-size: 18px;
    }
    .desc {
      font-size: 14px;
      color: #999999;
    }
    .notice-title {
      margin-top: 10px;
      margin-bottom: 5px;
      font-size: 17px;
      font-weight: bold;
      text-align: left;
    }
    .notice-text {
      font-size: 16px;
      text-align: left;
    }
    .relation{
      border-top:  1px solid #e5e5e5;
      border-bottom:  1px solid #e5e5e5;
      display: flex;
      align-items: center;
      padding: 10px 0;
      .title{
        margin: 0;
        margin-right: 10px;
        font-size: 14px;
      }
    }
  }
  .uni-padding-wrap{
    padding: 15px;
    .signBtn{
      background: #ee668a;
      margin-bottom: 10px;
    }
  }
</style>
