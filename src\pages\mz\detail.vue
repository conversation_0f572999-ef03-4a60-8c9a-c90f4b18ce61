<template>
  <view class="container">
    <view class="header">
      <view class="title">订单详情</view>
    </view>
    <view class="body">
      <view class="appoint_list">
        <view class="appoint_list-info">
          <view class="appoint_list-cent">
            <view class="appoint_list-cent-item">
              <view class="patient_info">订单号：</view>
              <view class="patient_infoText">{{ item.order_no }}</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">就诊院区：</view>
              <view class="patient_infoText">{{ item.hos_name }}</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">就诊医生：</view>
              <view class="patient_infoText">{{ item.doc_name }}</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">就诊人</view>
              <view class="patient_infoText">{{ new Array(item.patient_name.length).join('*') + item.patient_name.substr(-1) }}(ID：{{item.jz_card}})</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">支付时间</view>
              <view class="patient_infoText">{{ item.pay_time }}</view>
            </view>
            <view class="appoint_list-cent-item">
              <view class="patient_info">支付费用</view>
              <view class="patient_infoText">{{ item.real_pay_money }}</view>
            </view>
          </view>
        </view>
      </view>

      <view class="detail-title-content">
        <span class="detail-title">指引信息</span>
      </view>
      <view class="appoint_list" style="margin-top:0px;"> 
        <view class="appoint_list-info" v-html="item.res_msg"></view>
      </view>

      <view class="detail-title-content">
        <span class="detail-title">费用明细</span>
      </view>

      <view class="appoint_list" style="margin-top:0px;">
        <view class="appoint_list-info">
          <view class="appoint_list-cent">
            <view class="appoint_list-cent-item" v-for="(it, index) in item.bill_list" :key="index">
              <view class="patient_info">{{ it.name }}</view>
              <view class="patient_infoText">￥{{ it.price }}</view>
            </view>
          </view>
        </view>
      </view>

      <view class="qrcode">
        <view style="justify-content: center; width: 230px; margin: 0 auto">
          <u-qrcode ref="qrcode" canvas-id="qrcode" size="230" :value="item.jz_card"></u-qrcode>
        </view>
      </view>
    </view>
    <view class="footer"> </view>
  </view>
</template>

<script>
// import { defineComponent } from '@vue/composition-api'
import { payedDetailMz } from '@/api/ucenter.js'
import monitor from '/src/to/alipayLogger';
export default {
  setup() {},
  onLoad(option) {
    this.getDetail(option.id)
    monitor.api({api:"缴费记录查询",success:true,c1:"taSR_YL",time:200})
    //option为object类型，会序列化上个页⾯传递的参数
  },
  data() {
    return {
      item: {
        jz_card:'12345'
      },
    }
  },
  methods: {
    async getDetail(id) {
      const userInfo = uni.getStorageSync('userInfo')
      const { statusCode, data } = await payedDetailMz(null, {
        userid: userInfo.user_id,
        openid: userInfo.openid,
        id,
      })
      if (statusCode != 200) {
        uni.showToast({
          title: '请求网络失败',
          duration: 2000,
          icon: 'none',
        })
        return
      }
      if (data.code != 1) {
        uni.showToast({
          title: '暂无数据',
          duration: 2000,
          icon: 'none',
        })
        return
      }
      this.item = data.data

      // console.log('res', res)
      //const res = await XXXX
      // res.statusP
      // res.data
    },
  },
}
</script>

<style lang="scss" scoped>
.container{
  height: 100%;
  overflow: auto;
}
.header {
  .title {
    line-height: 45px;
    text-align: center;
    color: #3d4145;
    background-color: #edd1d8;
    font-size: 17px;
  }
}

.body {
  .appoint_list {
    border-radius: 10px;
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
    margin: 30rpx;
  }
  .appoint_list-info {
    padding: 10px 15px;
  }
  .appoint_list-cent-item {
    display: flex;
    display: -webkit-flex;
    -webkit-align-items: center;
    justify-content: space-between;
    font-size: 0.9em;
    line-height: 2.5;
  }

  .detail-title-content {
    width: 65%;
    margin: 1em auto 0;
    line-height: 1em;
    font-size: 14px;
    text-align: center;
    border-top: 2px solid #ffffff;
  }
  .detail-title {
    position: relative;
    top: -0.9em;
    padding: 0 0.55em;
    color: #000;
  }
}
</style>
