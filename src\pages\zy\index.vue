<template>
  <view class="container">
    <view class="header"> </view>
    <view class="body">
      <view class="sel-patient">
        <uni-forms>
          <uni-forms-item label="就诊人" name="patient_nation" class="pick-comm">
            <uni-data-picker
              placeholder="请选择就诊人"
              popup-title="请选择就诊人"
              :localdata="patinetList"
              v-model="selPatid"
              @change="changePat"
              @nodeclick="onnodeclickPat"
            ></uni-data-picker>
          </uni-forms-item>
        </uni-forms>
      </view>

      <view class="zy-list">
        <view class="appoint_list" v-for="(item, index) in zyList" :key="index">
          <view class="appoint_list-info">
            <view class="appoint_list-top">
              <view class="fl patient_name">{{ item.PatName }}</view>
            </view>
            <view class="appoint_list-cent">
              <view class="appoint_list-cent-item">
                <view class="patient_info">住院号</view>
                <view class="patient_infoText">{{ item.InpatientNO }}</view>
              </view>
              <view class="appoint_list-cent-item">
                <view class="patient_info">住院次数</view>
                <view class="patient_infoText">{{ item.AdmissTimes }}</view>
              </view>
              <view class="appoint_list-cent-item">
                <view class="patient_info">患者ID号</view>
                <view class="patient_infoText">{{ item.jz_card }}</view>
              </view>
              <view class="appoint_list-cent-item">
                <view class="patient_info">入院日期</view>
                <view class="patient_infoText">{{ item.AdmissDate }}</view>
              </view>
              <view class="appoint_list-cent-item">
                <view class="patient_info">当前科室</view>
                <view class="patient_infoText">{{ item.DeptName }}</view>
              </view>
              <view class="appoint_list-cent-item">
                <view class="patient_info">入院科室</view>
                <view class="patient_infoText">{{ item.AdmissWardName }}</view>
              </view>
              <view class="appoint_list-cent-item">
                <view class="patient_info">预交金额</view>
                <view class="patient_infoText">{{ item.EffDepoAmount }}</view>
              </view>
              <view class="appoint_list-cent-item">
                <view class="patient_info">总费用</view>
                <view class="patient_infoText">{{ item.EffChargeTotal }}</view>
              </view>
              <view class="appoint_list-cent-item">
                <view class="patient_info">预交金余额</view>
                <view class="patient_infoText">{{ item.balance }}</view>
              </view>
              <view v-if="item.yb_type" class="appoint_list-cent-item">
                <view class="patient_info">人员身份</view>
                <view class="patient_infoText">{{ item.yb_type_info }}</view>
              </view>
              <view v-if="item.yb_type" style="font-size: 14px; color: red; text-align: right;">您不允许线上充值住院预交金</view>
            </view>
          </view>
          <view class="appoint_list-but">
            <view
              class="dp-f-1 appoint_but"
              @click="
                goto(
                  `/pages/zy/dailyfee?pat_id=${item.patient_id}&inpatientno=${item.InpatientNO}&admisstimes=${item.AdmissTimes}`
                )
              "
              >查看费用清单</view
            >
            <view v-if="item.yb_type"
              class="dp-f-1 appoint_but"
              >住院充值</view>
            <view v-else
              class="dp-f-1 appoint_but et-color"
              @click="
                goto(
                  `/pages/zy/full?pat_id=${item.patient_id}&inpatientno=${item.InpatientNO}&admisstimes=${item.AdmissTimes}`
                )
              "
              >住院充值</view>
          </view>
        </view>
        <view style="height: 100px"></view>
      </view>
    </view>
    <view class="footer"> </view>
  </view>
</template>
<script>
  // import { defineComponent } from '@vue/composition-api'
  import { getMypatientList } from '@/api/waiting.js'
  import { getlistZy } from '@/api/ucenter.js'
  import { reportCmPV } from '@/to/cloudMonitorHelper'
  export default {
    setup() {},
    onLoad(option) {
      reportCmPV({ title: '住院缴费', option })
    },
    onShow(){
      this.getPatinets()
    },
    data() {
      return {
        selPatid: 0,
        patinetList: [],
        zyList: [],
      }
    },
    methods: {
      changePat(e) {},
      onnodeclickPat(e) {
        this.getList()
      },
      async getList() {
        const userInfo = uni.getStorageSync('userInfo')
        const { statusCode, data } = await getlistZy(null, {
          userid: userInfo.user_id,
          openid: userInfo.openid,
          pat_id: this.selPatid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '网络错误',
            icon: 'none',
          })
          return
        }
        if (data.code != 1 || data.data.length == 0) {
          uni.showToast({
            title: '暂无数据',
            icon: 'none',
          })
          return
        }
        this.zyList = data.data
      },
      async getPatinets() {
        const userInfo = uni.getStorageSync('userInfo')
        // 如果未授权，直接返回，否则会导致页面弹出多次提示窗，才能成功加载数据，影响用户体验
        if (!userInfo) {
          return
        }
        const { statusCode, data } = await getMypatientList(null, {
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '网络错误',
            icon: 'none',
          })
          return
        }
        if (!data.data.length) {
          uni.showModal({
            title: '提示',
            content: '暂无就诊人，如还没有就诊卡请新建卡，如已经有就诊卡请绑定就诊卡',
            cancelText: '去绑定卡',
            confirmText: '去新建卡',
            success: function (res) {
              if (res.confirm) {
                //console.log('用户点击去新建卡')
                uni.navigateTo({
                  url: '/pages/ucenter/patient/add',
                })
              } else if (res.cancel) {
                //console.log('用户点击去绑定卡')
                uni.navigateTo({
                  url: '/pages/ucenter/patient/bind',
                })
              }
            },
          })
          return
        }
        data.data.forEach((element) => {
          // #ifdef MP-ALIPAY
          element.text = new Array(element.patient_name.length).join('*') + element.patient_name.substr(-1)
          // #endif
          // #ifdef H5
          element.text = element.patient_name
          // #endif
          element.value = element.id
        })
        this.patinetList = data.data
      },
      goto(url) {
        uni.navigateTo({
          url,
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  .container {
    height: 100%;
    overflow: auto;
  }
  .body {
    .sel-patient {
      margin-top: 15px;
      padding: 0 15px;
    }

    .zy-list {
      padding: 0 15px;
    }

    .appoint_list {
      border-radius: 10px;
      margin-top: 10px;
      background-color: #ffffff;
      border: 1px solid #e5e5e5;
    }
    .appoint_list-info {
      padding: 10px 15px;
    }
    .appoint_list-cent-item {
      display: flex;
      display: -webkit-flex;
      -webkit-align-items: center;
      justify-content: space-between;
      font-size: 0.9em;
      line-height: 2;
    }
    .appoint_list-top {
      border-bottom: 1px solid #e5e5e5;
      overflow: hidden;
      padding: 10px 0;
    }
    .dp-f-1 {
      flex: 1;
    }
    .appoint_list-but {
      line-height: 50px;
      display: -webkit-box;
      display: -webkit-flex;
      display: flex;
      border-top: 1px solid #d5d5d6;
    }
    .appoint_but {
      text-align: center;
      border-left: 1px solid #d5d5d6;
      color: #999999;
      font-size: 16px;
    }
    .appoint_list-but .dp-f-1:first-child {
      border: none;
    }
    .et-color {
      color: rgb(238, 102, 138);
    }
  }
</style>
