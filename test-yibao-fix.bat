@echo off
echo ========================================
echo 医保缴费修复验证测试
echo ========================================
echo.

echo 1. 检查Docker容器状态...
docker ps | findstr "ertong-uni-app-dev"
if %errorlevel% equ 0 (
    echo ✅ 容器正在运行
) else (
    echo ❌ 容器未运行
    goto :error
)
echo.

echo 2. 检查端口监听状态...
netstat -an | findstr ":8080" | findstr "LISTENING"
if %errorlevel% equ 0 (
    echo ✅ 端口8080正在监听
) else (
    echo ❌ 端口8080未监听
    goto :error
)
echo.

echo 3. 检查应用启动日志...
echo 最近的应用日志:
docker logs ertong-uni-app-dev --tail 5
echo.

echo 4. 测试访问地址...
echo 正在打开浏览器测试...
start http://localhost:8080/ui/
echo ✅ 浏览器已打开，请手动验证页面是否正常加载
echo.

echo 5. 医保回调测试URL...
echo 测试医保回调处理，请在浏览器中访问以下URL:
echo http://localhost:8080/ui/pages/mz/index?billId=1^&patId=3306812^&isGT=0
echo.
echo 预期行为: 页面应该自动跳转到支付详情页面
echo.

echo 6. 支付页面直接测试URL...
echo 测试支付页面，请在浏览器中访问以下URL:
echo http://localhost:8080/ui/pages/mz/paydetail?bill_id=1^&pat_id=3306812^&isGT=0
echo.

echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 📋 验证清单:
echo □ 1. 页面是否正常加载
echo □ 2. 医保回调是否自动跳转
echo □ 3. 支付页面是否显示正常
echo □ 4. 浏览器控制台是否有新的日志输出
echo □ 5. 医保缴费按钮是否可以点击
echo.
echo 💡 调试提示:
echo - 打开浏览器开发者工具查看控制台日志
echo - 查看Network标签页监控API请求
echo - 如有问题请查看容器日志: docker logs ertong-uni-app-dev
echo.
goto :end

:error
echo.
echo ❌ 测试失败，请检查Docker容器状态
echo 尝试重新启动: docker-compose -f docker-compose.dev.yml up -d
echo.

:end
pause
