version: '3.8'

services:
  uni-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ertong-uni-app
    ports:
      - "8080:8080"
    volumes:
      # 挂载源代码目录，支持热重载
      - ./src:/app/src
      - ./public:/app/public
      - ./vue.config.js:/app/vue.config.js
      - ./babel.config.js:/app/babel.config.js
      - ./postcss.config.js:/app/postcss.config.js
      # 排除 node_modules 避免冲突
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - UNI_PLATFORM=h5
      - HOST=0.0.0.0
      - PORT=8080
    networks:
      - uni-network
    restart: unless-stopped
    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  uni-network:
    driver: bridge
