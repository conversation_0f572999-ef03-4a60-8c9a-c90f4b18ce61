lockfileVersion: 5.4

overrides:
  '@babel/runtime': ~7.17.9

specifiers:
  '@babel/runtime': ~7.17.9
  '@dcloudio/types': ^3.3.2
  '@dcloudio/uni-app': ^2.0.2-4050720250324001
  '@dcloudio/uni-app-plus': ^2.0.2-4050720250324001
  '@dcloudio/uni-automator': ^2.0.2-4050720250324001
  '@dcloudio/uni-cli-i18n': ^2.0.2-4050720250324001
  '@dcloudio/uni-cli-shared': ^2.0.2-4050720250324001
  '@dcloudio/uni-h5': ^2.0.2-4050720250324001
  '@dcloudio/uni-helper-json': '*'
  '@dcloudio/uni-i18n': ^2.0.2-4050720250324001
  '@dcloudio/uni-migration': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-360': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-alipay': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-baidu': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-harmony': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-jd': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-kuaishou': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-lark': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-qq': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-toutiao': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-vue': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-weixin': ^2.0.2-4050720250324001
  '@dcloudio/uni-mp-xhs': ^2.0.2-4050720250324001
  '@dcloudio/uni-quickapp-native': ^2.0.2-4050720250324001
  '@dcloudio/uni-quickapp-webview': ^2.0.2-4050720250324001
  '@dcloudio/uni-stacktracey': ^2.0.2-4050720250324001
  '@dcloudio/uni-stat': ^2.0.2-4050720250324001
  '@dcloudio/uni-template-compiler': ^2.0.2-4050720250324001
  '@dcloudio/uni-ui': ^1.4.16
  '@dcloudio/vue-cli-plugin-hbuilderx': ^2.0.2-4050720250324001
  '@dcloudio/vue-cli-plugin-uni': ^2.0.2-4050720250324001
  '@dcloudio/vue-cli-plugin-uni-optimize': ^2.0.2-4050720250324001
  '@dcloudio/webpack-uni-mp-loader': ^2.0.2-4050720250324001
  '@dcloudio/webpack-uni-pages-loader': ^2.0.2-4050720250324001
  '@vue/cli-plugin-babel': ~5.0.0
  '@vue/cli-service': ~5.0.0
  '@vue/shared': ^3.0.0
  autoprefixer: ^8.0.0
  babel-plugin-import: ^1.11.0
  chalk: ^4.1.2
  core-js: ^3.8.3
  cross-env: ^7.0.2
  dayjs: ^1.11.13
  flyio: ^0.6.2
  husky: ^8.0.1
  jest: ^25.4.0
  lint-staged: ^13.0.3
  mini-html-parser2: ^0.3.0
  postcss-comment: ^2.0.0
  postcss-loader: ^8.1.1
  prettier: ^2.7.1
  query-string: ^7.1.1
  regenerator-runtime: ^0.12.1
  sass: ^1.53.0
  sass-loader: ^13.0.2
  vue: '>= 2.6.14 < 2.7'
  vue-template-compiler: '>= 2.6.14 < 2.7'
  vuex: ^3.2.0

dependencies:
  '@dcloudio/uni-app': 2.0.2-4060420250429001_@dcloudio+types@3.4.15
  '@dcloudio/uni-app-plus': 2.0.2-4060420250429001
  '@dcloudio/uni-h5': 2.0.2-4060420250429001
  '@dcloudio/uni-i18n': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-360': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-alipay': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-baidu': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-harmony': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-jd': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-kuaishou': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-lark': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-qq': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-toutiao': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-vue': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-weixin': 2.0.2-4060420250429001
  '@dcloudio/uni-mp-xhs': 2.0.2-4060420250429001
  '@dcloudio/uni-quickapp-native': 2.0.2-4060420250429001_prettier@2.8.8
  '@dcloudio/uni-quickapp-webview': 2.0.2-4060420250429001
  '@dcloudio/uni-stacktracey': 2.0.2-4060420250429001
  '@dcloudio/uni-stat': 2.0.2-4060420250429001
  '@dcloudio/uni-ui': 1.5.7
  '@vue/shared': 3.5.13
  autoprefixer: 8.6.5
  chalk: 4.1.2
  core-js: 3.42.0
  dayjs: 1.11.13
  flyio: 0.6.14
  postcss-loader: 8.1.1
  query-string: 7.1.3
  regenerator-runtime: 0.12.1
  vue: 2.6.14
  vuex: 3.6.2_vue@2.6.14

devDependencies:
  '@babel/runtime': 7.17.9
  '@dcloudio/types': 3.4.15
  '@dcloudio/uni-automator': 2.0.2-4060420250429001
  '@dcloudio/uni-cli-i18n': 2.0.2-4060420250429001
  '@dcloudio/uni-cli-shared': 2.0.2-4060420250429001
  '@dcloudio/uni-helper-json': 1.0.13
  '@dcloudio/uni-migration': 2.0.2-4060420250429001
  '@dcloudio/uni-template-compiler': 2.0.2-4060420250429001
  '@dcloudio/vue-cli-plugin-hbuilderx': 2.0.2-4060420250429001
  '@dcloudio/vue-cli-plugin-uni': 2.0.2-4060420250429001
  '@dcloudio/vue-cli-plugin-uni-optimize': 2.0.2-4060420250429001
  '@dcloudio/webpack-uni-mp-loader': 2.0.2-4060420250429001
  '@dcloudio/webpack-uni-pages-loader': 2.0.2-4060420250429001
  '@vue/cli-plugin-babel': 5.0.8_sqt6tuffjadljs7xoqwshujxxu
  '@vue/cli-service': 5.0.8_upvy4r7hct7s4hgklxc7ih6yii
  babel-plugin-import: 1.13.8
  cross-env: 7.0.3
  husky: 8.0.3
  jest: 25.5.4
  lint-staged: 13.3.0
  mini-html-parser2: 0.3.0
  postcss-comment: 2.0.0
  prettier: 2.8.8
  sass: 1.88.0
  sass-loader: 13.3.3_sass@1.88.0
  vue-template-compiler: 2.6.14

packages:

  /@achrinza/node-ipc/9.2.9:
    resolution: {integrity: sha512-7s0VcTwiK/0tNOVdSX9FWMeFdOEcsAOz9HesBldXxFMaGvIak7KC2z9tV9EgsQXn6KUsWsfIkViMNuIo0GoZDQ==}
    engines: {node: 8 || 9 || 10 || 11 || 12 || 13 || 14 || 15 || 16 || 17 || 18 || 19 || 20 || 21 || 22}
    dependencies:
      '@node-ipc/js-queue': 2.0.3
      event-pubsub: 4.3.0
      js-message: 1.0.7
    dev: true

  /@ampproject/remapping/2.3.0:
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  /@babel/code-frame/7.27.1:
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  /@babel/compat-data/7.27.2:
    resolution: {integrity: sha512-TUtMJYRPyUb/9aU8f3K0mjmjf6M9N5Woshn2CS6nqJSeJtTtQcpLUXjGt9vbF8ZGff0El99sWkLgzwW3VXnxZQ==}
    engines: {node: '>=6.9.0'}

  /@babel/core/7.27.1:
    resolution: {integrity: sha512-IaaGWsQqfsQWVLqMn9OB92MNN7zukfVA4s7KKAI0KfrrDsZ0yhi5uV4baBuLuN7n3vsZpwP8asPPcVwApxvjBQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.1
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.1_@babel+core@7.27.1
      '@babel/helpers': 7.27.1
      '@babel/parser': 7.27.2
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.1
      '@babel/types': 7.27.1
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  /@babel/generator/7.27.1:
    resolution: {integrity: sha512-UnJfnIpc/+JO0/+KRVQNGU+y5taA5vCbwN8+azkX6beii/ZF+enZJSOKo11ZSzGJjlNfJHfQtmQT8H+9TXPG2w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  /@babel/helper-annotate-as-pure/7.27.1:
    resolution: {integrity: sha512-WnuuDILl9oOBbKnb4L+DyODx7iC47XfzmNCpTttFsSp6hTG7XZxu60+4IO+2/hPfcGOoKbFiwoI/+zwARbNQow==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.27.1

  /@babel/helper-compilation-targets/7.27.2:
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': 7.27.2
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.24.5
      lru-cache: 5.1.1
      semver: 6.3.1

  /@babel/helper-create-class-features-plugin/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-QwGAmuvM17btKU5VqXfb+Giw4JcN0hjuufz3DYnpeVDvZLAObloM77bhMXiqry3Iio+Ai4phVRDwl6WU10+r5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-annotate-as-pure': 7.27.1
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/helper-replace-supers': 7.27.1_@babel+core@7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/traverse': 7.27.1
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  /@babel/helper-create-regexp-features-plugin/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-uVDC72XVf8UbrH5qQTc18Agb8emwjTiZrQE11Nv3CuBEZmVvTwwE9CBUEvHku06gQCAyYf8Nv6ja1IN+6LMbxQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-annotate-as-pure': 7.27.1
      regexpu-core: 6.2.0
      semver: 6.3.1

  /@babel/helper-define-polyfill-provider/0.6.4_@babel+core@7.27.1:
    resolution: {integrity: sha512-jljfR1rGnXXNWnmQg2K3+bvhkxB51Rl32QRaOTuwwjviGrHzIbSc8+x9CpraDtbT7mfyjXObULP4w/adunNwAw==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      debug: 4.4.1
      lodash.debounce: 4.0.8
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  /@babel/helper-member-expression-to-functions/7.27.1:
    resolution: {integrity: sha512-E5chM8eWjTp/aNoVpcbfM7mLxu9XGLWYise2eBKGQomAk/Mb4XoxyqXTZbuTohbsl8EKqdlMhnDI2CCLfcs9wA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.27.1
      '@babel/types': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/helper-module-imports/7.27.1:
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.27.1
      '@babel/types': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/helper-module-transforms/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-9yHn519/8KvTU5BjTVEEeIM3w9/2yXNKoD82JifINImhpKkARMJKPP59kLo+BafpdN5zgNeIcS4jsGDmd3l58g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/helper-optimise-call-expression/7.27.1:
    resolution: {integrity: sha512-URMGH08NzYFhubNSGJrpUEphGKQwMQYBySzat5cAByY1/YgIRkULnIy3tAMeszlL/so2HbeilYloUmSpd7GdVw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.27.1

  /@babel/helper-plugin-utils/7.27.1:
    resolution: {integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-remap-async-to-generator/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-7fiA521aVw8lSPeI4ZOD3vRFkoqkJcS+z4hFo82bFSH/2tNd6eJ5qCVMS5OzDmZh/kaHQeBaeyxK6wljcPtveA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-annotate-as-pure': 7.27.1
      '@babel/helper-wrap-function': 7.27.1
      '@babel/traverse': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/helper-replace-supers/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-7EHz6qDZc8RYS5ElPoShMheWvEgERonFCs7IAonWLLUTXW59DP14bCZt89/GKyreYn8g3S83m21FelHKbeDCKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/traverse': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/helper-skip-transparent-expression-wrappers/7.27.1:
    resolution: {integrity: sha512-Tub4ZKEXqbPjXgWLl2+3JpQAYBJ8+ikpQ2Ocj/q/r0LwE3UhENh7EUabyHjz2kCEsrRY83ew2DQdHluuiDQFzg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.27.1
      '@babel/types': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/helper-string-parser/7.27.1:
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-identifier/7.27.1:
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-option/7.27.1:
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-wrap-function/7.27.1:
    resolution: {integrity: sha512-NFJK2sHUvrjo8wAU/nQTWU890/zB2jj0qBcCbZbbf+005cAsv6tMjXz31fBign6M5ov1o0Bllu+9nbqkfsjjJQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.1
      '@babel/types': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/helpers/7.27.1:
    resolution: {integrity: sha512-FCvFTm0sWV8Fxhpp2McP5/W53GPllQ9QeQ7SiqGWjMf/LVG07lFa5+pgK05IRhVwtvafT22KF+ZSnM9I545CvQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.1

  /@babel/parser/7.27.2:
    resolution: {integrity: sha512-QYLs8299NA7WM/bZAdp+CviYYkVoYXlDW2rzliy3chxd1PQjej7JORuMJDJXJUb9g0TT+B99EwaVLKmX+sPXWw==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.27.1

  /@babel/plugin-bugfix-firefox-class-in-computed-class-key/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-QPG3C9cCVRQLxAVwmefEmwdTanECuUBMQZ/ym5kiw3XKCGA7qkuQLcjWWHcrD/GKbn/WmJwaezfuuAOcyKlRPA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-bugfix-safari-class-field-initializer-scope/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-qNeq3bCKnGgLkEXUuFry6dPlGfCdQNZbn7yUAPCInwAJHMU7THJfrBSozkcWq5sNM6RcF3S8XyQL2A52KNR9IA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-g4L7OYun04N1WyqMNjldFwlfPCLVkgB54A/YCXICZYBsvJJE3kByKv9c9+R/nAfmIfjl2rKYLNyMHboYbZaWaA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-oO02gcONcD5O1iTLi/6frMJBIwWEHceWGSGqrpCmEL8nogiS6J9PBlE48CaK20/Jx1LuRml9aDftLgdjXT8+Cw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/plugin-transform-optional-chaining': 7.27.1_@babel+core@7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-6BpaYGDavZqkI6yT+KSPdpZFfpnd68UKXbcjI9pJ13pvHhPrCKWOOLp+ysvMeA+DxnhuPpgIaRpxRxo5A9t5jw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-proposal-class-properties/7.18.6_@babel+core@7.27.1:
    resolution: {integrity: sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-decorators/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-DTxe4LBPrtFdsWzgpmbBKevg3e9PBy+dXRt19kSbucbZvL2uqtdqwwpluL1jfxYE0wIDTFp1nTy/q6gNLsxXrg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-decorators': 7.27.1_@babel+core@7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-private-property-in-object/7.21.0-placeholder-for-preset-env.2_@babel+core@7.27.1:
    resolution: {integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1

  /@babel/plugin-syntax-async-generators/7.8.4_@babel+core@7.27.1:
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-bigint/7.8.3_@babel+core@7.27.1:
    resolution: {integrity: sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-class-properties/7.12.13_@babel+core@7.27.1:
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-decorators/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-YMq8Z87Lhl8EGkmb0MwYkt36QnxC+fzCgrl66ereamPlYToRpIk5nUjKUY3QKLWq8mwUB1BgbeXcTJhZOCDg5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-dynamic-import/7.8.3_@babel+core@7.27.1:
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-import-assertions/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-UT/Jrhw57xg4ILHLFnzFpPDlMbcdEicaAtjPQpbj9wa8T4r5KVWCimHcL/460g8Ht0DMxDyjsLgiWSkVjnwPFg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-syntax-import-attributes/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-syntax-import-meta/7.10.4_@babel+core@7.27.1:
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-json-strings/7.8.3_@babel+core@7.27.1:
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-jsx/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-logical-assignment-operators/7.10.4_@babel+core@7.27.1:
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3_@babel+core@7.27.1:
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-numeric-separator/7.10.4_@babel+core@7.27.1:
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-object-rest-spread/7.8.3_@babel+core@7.27.1:
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-optional-catch-binding/7.8.3_@babel+core@7.27.1:
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-optional-chaining/7.8.3_@babel+core@7.27.1:
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-syntax-unicode-sets-regex/7.18.6_@babel+core@7.27.1:
    resolution: {integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-arrow-functions/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-8Z4TGic6xW70FKThA5HYEKKyBpOOsucTOD1DjU3fZxDg+K3zBJcXMFnt/4yQiZnf5+MiOMSXQ9PaEK/Ilh1DeA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-async-generator-functions/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-eST9RrwlpaoJBDHShc+DS2SG4ATTi2MYNb4OxYkf3n+7eb49LWpnS+HSpVfW4x927qQwgk8A2hGNVaajAEw0EA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-remap-async-to-generator': 7.27.1_@babel+core@7.27.1
      '@babel/traverse': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-async-to-generator/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-NREkZsZVJS4xmTr8qzE5y8AfIPqsdQfRuUiLRTEzb7Qii8iFWCyDKaUV2c0rCuh4ljDZ98ALHP/PetiBV2nddA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-remap-async-to-generator': 7.27.1_@babel+core@7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-block-scoped-functions/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-cnqkuOtZLapWYZUYM5rVIdv1nXYuFVIltZ6ZJ7nIj585QsjKM5dhL2Fu/lICXZ1OyIAFc7Qy+bvDAtTXqGrlhg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-block-scoping/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-QEcFlMl9nGTgh1rn2nIeU5bkfb9BAjaQcWbiP4LvKxUot52ABcTkpcyJ7f2Q2U2RuQ84BNLgts3jRme2dTx6Fw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-class-properties/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-D0VcalChDMtuRvJIu3U/fwWjf8ZMykz5iZsg77Nuj821vCKI3zCyRLwRdWbsuJ/uRwZhZ002QtCqIkwC/ZkvbA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-class-static-block/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-s734HmYU78MVzZ++joYM+NkJusItbdRcbm+AGRgJCt3iA+yux0QpD9cBVdz3tKyrjVYWRl7j0mHSmv4lhV0aoA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-classes/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-7iLhfFAubmpeJe/Wo2TVuDrykh/zlWXLzPNdL0Jqn/Xu8R3QQ8h9ff8FQoISZOsw74/HFqFI7NX63HN7QFIHKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-annotate-as-pure': 7.27.1
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1_@babel+core@7.27.1
      '@babel/traverse': 7.27.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-computed-properties/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-lj9PGWvMTVksbWiDT2tW68zGS/cyo4AkZ/QTp0sQT0mjPopCmrSkzxeXkznjqBxzDI6TclZhOJbBmbBLjuOZUw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/template': 7.27.2

  /@babel/plugin-transform-destructuring/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-ttDCqhfvpE9emVkXbPD8vyxxh4TWYACVybGkDj+oReOGwnp066ITEivDlLwe0b1R0+evJ13IXQuLNB5w1fhC5Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-dotall-regex/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-gEbkDVGRvjj7+T1ivxrfgygpT7GUd4vmODtYpbs0gZATdkX8/iSnOtZSxiZnsgm1YjTgjI6VKBGSJJevkrclzw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-duplicate-keys/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-MTyJk98sHvSs+cvZ4nOauwTTG1JeonDjSGvGGUNHreGQns+Mpt6WX/dVzWBHgg+dYZhkC4X+zTDfkTU+Vy9y7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-duplicate-named-capturing-groups-regex/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-hkGcueTEzuhB30B3eJCbCYeCaaEQOmQR0AdvzpD4LoN0GXMWzzGSuRrxR2xTnCrvNbVwK9N6/jQ92GSLfiZWoQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-dynamic-import/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-MHzkWQcEmjzzVW9j2q8LGjwGWpG2mjwaaB0BNQwst3FIjqsg8Ct/mIZlvSPJvfi9y2AC8mi/ktxbFVL9pZ1I4A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-exponentiation-operator/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-uspvXnhHvGKf2r4VVtBpeFnuDWsJLQ6MF6lGJLC89jBR1uoVeqM416AZtTuhTezOfgHicpJQmoD5YUakO/YmXQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-export-namespace-from/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-tQvHWSZ3/jH2xuq/vZDy0jNn+ZdXJeM8gHvX4lnJmsc3+50yPlWdZXIc5ay+umX+2/tJIqHqiEqcJvxlmIvRvQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-for-of/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-BfbWFFEJFQzLCQ5N8VocnCtA8J1CLkNTe2Ms2wocj75dd6VpiqS5Z5quTYcUoo4Yq+DN0rtikODccuv7RU81sw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-function-name/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-1bQeydJF9Nr1eBCMMbC+hdwmRlsv5XYOMu03YSWFwNs0HsAmtSxxF1fyuYPqemVldVyFmlCU7w8UE14LupUSZQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-json-strings/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-6WVLVJiTjqcQauBhn1LkICsR2H+zm62I3h9faTDKt1qP4jn2o72tSvqMwtGFKGTpojce0gJs+76eZ2uCHRZh0Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-literals/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-0HCFSepIpLTkLcsi86GG3mTUzxV5jpmbv97hTETW3yzrAij8aqlD36toB1D0daVFJM8NK6GvKO0gslVQmm+zZA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-logical-assignment-operators/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-SJvDs5dXxiae4FbSL1aBJlG4wvl594N6YEVVn9e3JGulwioy6z3oPjx/sQBO3Y4NwUu5HNix6KJ3wBZoewcdbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-member-expression-literals/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-hqoBX4dcZ1I33jCSWcXrP+1Ku7kdqXf1oeah7ooKOIiAdKQ+uqftgCFNOSzA5AMS2XIHEYeGFg4cKRCdpxzVOQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-modules-amd/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-iCsytMg/N9/oFq6n+gFTvUYDZQOMK5kEdeYxmxt91fcJGycfxVP9CnrxoliM0oumFERba2i8ZtwRUCMhvP1LnA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-module-transforms': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-modules-commonjs/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-OJguuwlTYlN0gBZFRPqwOGNWssZjfIUdS7HMYtN8c1KmwpwHFBwTeFZrg9XZa+DFTitWOW5iTAG7tyCUPsCCyw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-module-transforms': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-modules-systemjs/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-w5N1XzsRbc0PQStASMksmUeqECuzKuTJer7kFagK8AXgpCMkeDMO5S+aaFb7A51ZYDF7XI34qsTX+fkHiIm5yA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-module-transforms': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-modules-umd/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-iQBE/xC5BV1OxJbp6WG7jq9IWiD+xxlZhLrdwpPkTX3ydmXdvoCpyfJN7acaIBZaOqTfr76pgzqBJflNbeRK+w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-module-transforms': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-named-capturing-groups-regex/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-SstR5JYy8ddZvD6MhV0tM/j16Qds4mIpJTOd1Yu9J9pJjH93bxHECF7pgtc28XvkzTD6Pxcm/0Z73Hvk7kb3Ng==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-new-target/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-f6PiYeqXQ05lYq3TIfIDu/MtliKUbNwkGApPUvyo6+tc7uaR4cPjPe7DFPr15Uyycg2lZU6btZ575CuQoYh7MQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-nullish-coalescing-operator/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-aGZh6xMo6q9vq1JGcw58lZ1Z0+i0xB2x0XaauNIUXd6O1xXc3RwoWEBlsTQrY4KQ9Jf0s5rgD6SiNkaUdJegTA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-numeric-separator/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-fdPKAcujuvEChxDBJ5c+0BTaS6revLV7CJL08e4m3de8qJfNIuCc2nc7XJYOjBoTMJeqSmwXJ0ypE14RCjLwaw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-object-rest-spread/7.27.2_@babel+core@7.27.1:
    resolution: {integrity: sha512-AIUHD7xJ1mCrj3uPozvtngY3s0xpv7Nu7DoUSnzNY6Xam1Cy4rUznR//pvMHOhQ4AvbCexhbqXCtpxGHOGOO6g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-transform-destructuring': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-parameters': 7.27.1_@babel+core@7.27.1

  /@babel/plugin-transform-object-super/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-SFy8S9plRPbIcxlJ8A6mT/CxFdJx/c04JEctz4jf8YZaVS2px34j7NXRrlGlHkN/M2gnpL37ZpGRGVFLd3l8Ng==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1_@babel+core@7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-optional-catch-binding/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-txEAEKzYrHEX4xSZN4kJ+OfKXFVSWKB2ZxM9dpcE3wT7smwkNmXo5ORRlVzMVdJbD+Q8ILTgSD7959uj+3Dm3Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-optional-chaining/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-BQmKPPIuc8EkZgNKsv0X4bPmOoayeu4F1YCwx2/CfmDSXDbp7GnzlUH+/ul5VGfRg1AoFPsrIThlEBj2xb4CAg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-parameters/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-018KRk76HWKeZ5l4oTj2zPpSh+NbGdt0st5S6x0pga6HgrjBOJb24mMDHorFopOOd6YHkLgOZ+zaCjZGPO4aKg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-private-methods/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-10FVt+X55AjRAYI9BrdISN9/AQWHqldOeZDUoLyif1Kn05a56xVBXb8ZouL8pZ9jem8QpXaOt8TS7RHUIS+GPA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-private-property-in-object/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-5J+IhqTi1XPa0DXF83jYOaARrX+41gOewWbkPyjMNRDqgOCqdffGh8L3f/Ek5utaEBZExjSAzcyjmV9SSAWObQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-annotate-as-pure': 7.27.1
      '@babel/helper-create-class-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-property-literals/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-oThy3BCuCha8kDZ8ZkgOg2exvPYUlprMukKQXI1r1pJ47NCvxfkEy8vK+r/hT9nF0Aa4H1WUPZZjHTFtAhGfmQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-regenerator/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-B19lbbL7PMrKr52BNPjCqg1IyNUIjTcxKj8uX9zHO+PmWN93s19NDr/f69mIkEp2x9nmDJ08a7lgHaTTzvW7mw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-regexp-modifiers/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-TtEciroaiODtXvLZv4rmfMhkCv8jx3wgKpL68PuiPh2M4fvz5jhsA7697N1gMvkvr/JTF13DrFYyEbY9U7cVPA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-reserved-words/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-V2ABPHIJX4kC7HegLkYoDpfg9PVmuWy/i6vUM5eGK22bx4YVFD3M5F0QQnWQoDs6AGsUWTVOopBiMFQgHaSkVw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-runtime/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-TqGF3desVsTcp3WrJGj4HfKokfCXCLcHpt4PJF0D8/iT6LPd9RS82Upw3KPeyr6B22Lfd3DO8MVrmp0oRkUDdw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      babel-plugin-polyfill-corejs2: 0.4.13_@babel+core@7.27.1
      babel-plugin-polyfill-corejs3: 0.11.1_@babel+core@7.27.1
      babel-plugin-polyfill-regenerator: 0.6.4_@babel+core@7.27.1
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-shorthand-properties/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-N/wH1vcn4oYawbJ13Y/FxcQrWk63jhfNa7jef0ih7PHSIHX2LB7GWE1rkPrOnka9kwMxb6hMl19p7lidA+EHmQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-spread/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-kpb3HUqaILBJcRFVhFUs6Trdd4mkrzcGXss+6/mxUd273PfbWqSDHRzMT2234gIg2QYfAjvXLSquP1xECSg09Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  /@babel/plugin-transform-sticky-regex/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-lhInBO5bi/Kowe2/aLdBAawijx+q1pQzicSgnkB6dUPc1+RC8QmJHKf2OjvU+NZWitguJHEaEmbV6VWEouT58g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-template-literals/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-fBJKiV7F2DxZUkg5EtHKXQdbsbURW3DZKQUWphDum0uRP6eHGGa/He9mc0mypL680pb+e/lDIthRohlv8NCHkg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-typeof-symbol/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-RiSILC+nRJM7FY5srIyc4/fGIwUhyDuuBSdWn4y6yT6gm652DpCHZjIipgn6B7MQ1ITOUnAKWixEUjQRIBIcLw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-unicode-escapes/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-Ysg4v6AmF26k9vpfFuTZg8HRfVWzsh1kVfowA23y9j/Gu6dOuahdUVhkLqpObp3JIv27MLSii6noRnuKN8H0Mg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-unicode-property-regex/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-uW20S39PnaTImxp39O5qFlHLS9LJEmANjMG7SxIhap8rCHqu0Ik+tLEPX5DKmHn6CsWQ7j3lix2tFOa5YtL12Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-unicode-regex/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-xvINq24TRojDuyt6JGtHmkVkrfVV3FPT16uytxImLeBZqW3/H52yN+kM1MGuyPkIQxrzKwPHs5U/MP3qKyzkGw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/plugin-transform-unicode-sets-regex/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-EtkOujbc4cgvb0mlpQefi4NTPBzhSIevblFevACNLUspmrALgmEBdL/XfnyyITfd8fKBZrZys92zOWcik7j9Tw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-create-regexp-features-plugin': 7.27.1_@babel+core@7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  /@babel/preset-env/7.27.2_@babel+core@7.27.1:
    resolution: {integrity: sha512-Ma4zSuYSlGNRlCLO+EAzLnCmJK2vdstgv+n7aUP+/IKZrOfWHOJVdSJtuub8RzHTj3ahD37k5OKJWvzf16TQyQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.27.2
      '@babel/core': 7.27.1
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-bugfix-safari-class-field-initializer-scope': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2_@babel+core@7.27.1
      '@babel/plugin-syntax-import-assertions': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-syntax-import-attributes': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6_@babel+core@7.27.1
      '@babel/plugin-transform-arrow-functions': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-async-generator-functions': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-async-to-generator': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-block-scoped-functions': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-block-scoping': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-class-properties': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-class-static-block': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-classes': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-computed-properties': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-destructuring': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-dotall-regex': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-duplicate-keys': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-duplicate-named-capturing-groups-regex': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-dynamic-import': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-exponentiation-operator': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-export-namespace-from': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-for-of': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-function-name': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-json-strings': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-literals': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-logical-assignment-operators': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-member-expression-literals': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-modules-amd': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-modules-commonjs': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-modules-systemjs': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-modules-umd': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-named-capturing-groups-regex': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-new-target': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-nullish-coalescing-operator': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-numeric-separator': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-object-rest-spread': 7.27.2_@babel+core@7.27.1
      '@babel/plugin-transform-object-super': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-optional-catch-binding': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-optional-chaining': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-parameters': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-private-methods': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-private-property-in-object': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-property-literals': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-regenerator': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-regexp-modifiers': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-reserved-words': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-shorthand-properties': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-spread': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-sticky-regex': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-template-literals': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-typeof-symbol': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-unicode-escapes': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-unicode-property-regex': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-unicode-regex': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-unicode-sets-regex': 7.27.1_@babel+core@7.27.1
      '@babel/preset-modules': 0.1.6-no-external-plugins_@babel+core@7.27.1
      babel-plugin-polyfill-corejs2: 0.4.13_@babel+core@7.27.1
      babel-plugin-polyfill-corejs3: 0.11.1_@babel+core@7.27.1
      babel-plugin-polyfill-regenerator: 0.6.4_@babel+core@7.27.1
      core-js-compat: 3.42.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  /@babel/preset-modules/0.1.6-no-external-plugins_@babel+core@7.27.1:
    resolution: {integrity: sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/types': 7.27.1
      esutils: 2.0.3

  /@babel/register/7.27.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-K13lQpoV54LATKkzBpBAEu1GGSIRzxR9f4IN4V8DCDgiUMo2UDGagEZr3lPeVNJPLkWUi5JE4hCHKneVTwQlYQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      clone-deep: 4.0.1
      find-cache-dir: 2.1.0
      make-dir: 2.1.0
      pirates: 4.0.7
      source-map-support: 0.5.21
    dev: false

  /@babel/runtime/7.17.9:
    resolution: {integrity: sha512-lSiBBvodq29uShpWGNbgFdKYNiFDo5/HIYsaCEY9ff4sb10x9jizo2+pRrSyF4jKZCXqgzuqBOQKbUm90gQwJg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.13.11

  /@babel/template/7.27.2:
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1

  /@babel/traverse/7.27.1:
    resolution: {integrity: sha512-ZCYtZciz1IWJB4U61UPu4KEaqyfj+r5T1Q5mqPo+IBpcG9kHv30Z0aD8LXPgC1trYa6rK0orRyAhqUgk4MjmEg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.1
      '@babel/parser': 7.27.2
      '@babel/template': 7.27.2
      '@babel/types': 7.27.1
      debug: 4.4.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  /@babel/types/7.27.1:
    resolution: {integrity: sha512-+EzkxvLNfiUeKMgy/3luqfsCWFRXLb7U6wNQTk60tovuckwB15B191tJWvpp4HjiQWdJkCxO3Wbvc6jlk3Xb2Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  /@bcoe/v8-coverage/0.2.3:
    resolution: {integrity: sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==}
    dev: true

  /@cnakazawa/watch/1.0.4:
    resolution: {integrity: sha512-v9kIhKwjeZThiWrLmj0y17CWoyddASLj9O2yvbZkbvw/N3rWOYy9zkV66ursAoVr0mV15bL8g0c4QZUE6cdDoQ==}
    engines: {node: '>=0.1.95'}
    hasBin: true
    dependencies:
      exec-sh: 0.3.6
      minimist: 1.2.8
    dev: true

  /@dcloudio/types/3.4.15:
    resolution: {integrity: sha512-Y8n/Z7USQoYFWUYZBLVjLXV3G5E/Odnvy3Ldgri4ikCcyQqHRk6RRaLh0WWnuIkxQxz2MXqLH4JNN65sb00XIA==}
    dev: true

  /@dcloudio/uni-app-plus/2.0.2-4060420250429001:
    resolution: {integrity: sha512-mcpPe+54Wipg/icg1wooqJ1GedL7LFGAJJtWeUzFaBglMv23pb8w7kvLmmHKnDq3+ztEZac2BotZphlKLi2fww==}
    dev: false

  /@dcloudio/uni-app/2.0.2-4060420250429001_@dcloudio+types@3.4.15:
    resolution: {integrity: sha512-52yw16cBAUu+Idp//RRVL9OolRXppjYZWOpRY7l2J4bl7aBimq21/7lPzTR9CmaetR0O2jbobTIMZSZJIfBQsQ==}
    peerDependencies:
      '@dcloudio/types': ^3.0.15
      '@vue/composition-api': ^1.7.0
    dependencies:
      '@dcloudio/types': 3.4.15
    dev: false

  /@dcloudio/uni-automator/2.0.2-4060420250429001:
    resolution: {integrity: sha512-ToKTWCAH/cqTde7BaVO3xIBkbKevEtvfJF0yxvvzo8MLQZ6Pn7whFxv7CGh6q+QsddYaCyPZpZUvKy8mX5gnBw==}
    peerDependencies:
      adbkit: ^2.11.1
      node-simctl: ^6.1.0
      puppeteer: ^3.0.1
    dependencies:
      address: 1.2.2
      debug: 4.4.1
      default-gateway: 6.0.3
      jimp: 0.10.3_debug@4.4.1
      jsonc-parser: 3.3.1
      kill-port: 1.6.1
      licia: 1.48.0
      postcss-selector-parser: 6.1.2
      qrcode-reader: 1.0.4
      qrcode-terminal: 0.12.0
      ws: 7.5.10
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: true

  /@dcloudio/uni-cli-i18n/2.0.2-4060420250429001:
    resolution: {integrity: sha512-cqJl2TuchoSKL1akyojELAU2jcHMYzU/shNfxXqUKsZOLUhS1dOf16Uu/fORvkB3AnIfLUUeqD+90QTJiETvLA==}
    dependencies:
      i18n: 0.13.4
      os-locale-s-fix: 1.0.8-fix-1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@dcloudio/uni-cli-shared/2.0.2-4060420250429001:
    resolution: {integrity: sha512-+NV5OaDXLmXrrHpW2axuz1kOuxXTldcudKbcqECxvtt85qyx1b90FpRWyjb0fckJwlUf25C8EJbbTNdaADl6JA==}
    dependencies:
      escape-string-regexp: 4.0.0
      fast-glob: 3.3.3
      fs-extra: 10.1.0
      glob-escape: 0.0.2
      hash-sum: 1.0.2
      postcss-urlrewrite: 0.2.2
      strip-json-comments: 2.0.1
    dev: true

  /@dcloudio/uni-h5/2.0.2-4060420250429001:
    resolution: {integrity: sha512-EAPcP5m+4gozxvrev2bG0dhqDxIYo2cEvxNrzsXqaGmpqSoeiYyLjalyfertjdFz3pkVU8oqJmCOXxD3Utczhg==}
    dependencies:
      base64-arraybuffer: 0.2.0
      intersection-observer: 0.7.0
      pako: 1.0.11
      postcss-urlrewrite: 0.3.0
      safe-area-insets: 1.4.1
    dev: false

  /@dcloudio/uni-helper-json/1.0.13:
    resolution: {integrity: sha512-FO9Iu4zW4td3Tr+eiCDWuele2ehkJ4qxQ/UhpAMLjso+ZdWz6NagK5Syh6cdy1hoDqbxpNoqnLynuJXe81Ereg==}
    dev: true

  /@dcloudio/uni-i18n/2.0.2-4060420250429001:
    resolution: {integrity: sha512-xWbUsRTaT4iKc7swSY2wS/JZGtHi3J66jrbvYNOGqlIwCgRBw0Kju8v7JumFzGX6KSoJqCyMTsO0j7/+Ijgxbw==}
    dev: false

  /@dcloudio/uni-migration/2.0.2-4060420250429001:
    resolution: {integrity: sha512-gWEDOyGaAQtjYrYzxwR4nJAGdEkunFWdtfJ0+g1u+v9KpyDkLWB5toGDwg744zjaFiwfoG/tO6QVc+zdXNrNvw==}
    hasBin: true
    dependencies:
      commander: 4.1.1
      fs-extra: 8.1.0
      mustache: 3.2.1
      recast: 0.23.11
      stricter-htmlparser2: 3.9.6
    dev: true

  /@dcloudio/uni-mp-360/2.0.2-4060420250429001:
    resolution: {integrity: sha512-Q5ptll/L+Lnrvf4pdoV8pk6qqwqReHpr8gsCV78ykL2fOLLHdZOpdSG4gidx5yE2AWHV52ooYFp9gLH9tap+Kw==}
    dev: false

  /@dcloudio/uni-mp-alipay/2.0.2-4060420250429001:
    resolution: {integrity: sha512-3mJ8OFkDJAwMJ9otd+zFH5AuMFnxhlkAkaTjdThAIfjkn/F3rxO/Uv3YydRBszpMCgkaZZVY2aWx1ejp6ZpdJg==}
    dev: false

  /@dcloudio/uni-mp-baidu/2.0.2-4060420250429001:
    resolution: {integrity: sha512-AgKcqgii42p53Z8TYuw8y5/bzLFS39F9/y3JSKb5d9YBcBn19REUkQDSrP11VMRYmemdZGf8EZMXa6SraxUgwg==}
    dev: false

  /@dcloudio/uni-mp-harmony/2.0.2-4060420250429001:
    resolution: {integrity: sha512-lS24oVOgxsmkgZZ6yaCT/yCndh3cu6bL6WPolL2APqDlT0/zL9nLKuR9quokd5PjJAbAaxThSZRGogRWp6g5ug==}
    dev: false

  /@dcloudio/uni-mp-jd/2.0.2-4060420250429001:
    resolution: {integrity: sha512-mx15un2WfOlwmnsBz0hWpzRNodnEejcJb83WxWMY0qK8RUHZCxEJYyQUQOAy+zfXfStc1vi/pCcdHKdOfDcR5w==}
    dev: false

  /@dcloudio/uni-mp-kuaishou/2.0.2-4060420250429001:
    resolution: {integrity: sha512-gK3cuEa3rJ3+IpCuNKxK8WvSdxjuZjMI3cSN88U/3Ti//XK4OqFhO3fyDtdeN2AK4vtDnTxXRmeGikEQ/Xy6+w==}
    dev: false

  /@dcloudio/uni-mp-lark/2.0.2-4060420250429001:
    resolution: {integrity: sha512-M9Up1TdKIx0KF2RYGsk3zmV6X8xbDsb0ow568ybPuSbfmnDhu21GO039/ffRI7effBB3KwDhKn5ojOepnoTsww==}
    dev: false

  /@dcloudio/uni-mp-qq/2.0.2-4060420250429001:
    resolution: {integrity: sha512-vFTHf6TATqibKke4oPVveBcXNqnlDJ8MFVyuuRi3LFnaS7zq3E/3B6kOhAfMv2wePydlgclp+xOPk8yGHLg5EQ==}
    dev: false

  /@dcloudio/uni-mp-toutiao/2.0.2-4060420250429001:
    resolution: {integrity: sha512-aVCgLmQg72C/BpWDwTzQt6lsFxLxgTDEdy8OsePH/jnL4nOL2hjUZs9879Xa89eGKhig3vRuKXMy2xUdRpT1bg==}
    dev: false

  /@dcloudio/uni-mp-vue/2.0.2-4060420250429001:
    resolution: {integrity: sha512-g5r6UwEa2oPv0RTI4F5dT/ULOTDitp6qSFt3uyfP8/Pn3UXDNaLDqwavRFta0EHgIy6Mq7Xrmk6azMcX3OEpDw==}
    dev: false

  /@dcloudio/uni-mp-weixin/2.0.2-4060420250429001:
    resolution: {integrity: sha512-LLPeTqQz0ITGFVjAhwskd85PMqSvsKLIQD5aYZYHr9vrKQXtWd7bu1Sq/PJI4gXCUIgrq4bNId5jS6SpKEyPQg==}
    dev: false

  /@dcloudio/uni-mp-xhs/2.0.2-4060420250429001:
    resolution: {integrity: sha512-eXxOl7q3mW3x1rqFKL+zXsVzF5WngYymEjVu0on9eY0TxH1w+ifoJlES60ca2nGr2nzY8ytblGdhFGUaRgHr2g==}
    dev: false

  /@dcloudio/uni-quickapp-native/2.0.2-4060420250429001_prettier@2.8.8:
    resolution: {integrity: sha512-Gr+0HqhvJ88G2/MJWuXDRs/frnq0inSQ5sYIdEvxylwc48ZBgq9isUAYxFmekVFXcSd5yJi8cB/dWoTCjPeO1g==}
    dependencies:
      '@hap-toolkit/dsl-vue': 0.6.13_prettier@2.8.8
      '@hap-toolkit/packager': 0.6.13
      '@hap-toolkit/server': 0.6.13
      module-alias: 2.2.3
    transitivePeerDependencies:
      - bufferutil
      - cache-loader
      - file-loader
      - prettier
      - supports-color
      - utf-8-validate
      - webpack
      - webpack-cli
      - webpack-command
    dev: false

  /@dcloudio/uni-quickapp-webview/2.0.2-4060420250429001:
    resolution: {integrity: sha512-lJ4RsnRoRd7oLDiGBtYsrz6DcX36IZuaaYmHbmcP3qluCGEk8MeHTEyUnL5FKKUaRPbago5rg68zL0lCgMBbcw==}
    dev: false

  /@dcloudio/uni-stacktracey/2.0.2-4060420250429001:
    resolution: {integrity: sha512-rfbUmcHXT/QpX8aFxWywZc/EYPKQeke4Ibc+JBaoQxAdSbrGFSKKK33IQm880X4a/bpTBVn4ovv789YNzmkglA==}
    dev: false

  /@dcloudio/uni-stat/2.0.2-4060420250429001:
    resolution: {integrity: sha512-4g88+RsGKi7g9P+lSC9zn+62FRJQ5hdtHyq5fLyIMIpKrZDKqapYOqct5phTdIvlV32mIiwzEx42yu3fy+nFgA==}

  /@dcloudio/uni-template-compiler/2.0.2-4060420250429001:
    resolution: {integrity: sha512-Usx01sssI3L50ynoe6Qgcsjg4PP4+mQ4GTpQhv72aI1SuGxmDIciHIMQZU3SKSfF/Xm9LHSt2DkCghRnLLI2aQ==}
    dependencies:
      '@babel/parser': 7.27.2
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.1
      '@babel/types': 7.27.1
      vue-template-compiler: 2.6.14
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@dcloudio/uni-ui/1.5.7:
    resolution: {integrity: sha512-DugxSIrQrze1FLdUOj9a+JEQ0bHGjnJTcGUK1mN/MivKg7nuKJBRWk5Ipa9sUdoBznX6ndz5h2e7Uao6x1CdCw==}
    dev: false

  /@dcloudio/vue-cli-plugin-hbuilderx/2.0.2-4060420250429001:
    resolution: {integrity: sha512-srfFL7Oeb8JgobjB1bP9jrFbVdsnPCCEYMpmwXP/nw40qB2buywn/N/XiLhpOMZYv8MlX0xIRnJUn4A6wGO7YQ==}
    dependencies:
      acorn: 5.7.4
      css: 2.2.4
      escodegen: 1.14.3
    dev: true

  /@dcloudio/vue-cli-plugin-uni-optimize/2.0.2-4060420250429001:
    resolution: {integrity: sha512-5HClntvwjJs5yDoracekHFN0dlexd/q76QOQqACoOFVWHrzFOq7DREbdwNKL79hMb9cnGzO7V9Uva6zLUgx++g==}
    dev: true

  /@dcloudio/vue-cli-plugin-uni/2.0.2-4060420250429001:
    resolution: {integrity: sha512-zofGgitYND+chUdrZVY+xYsB1GA+PE7gYQuK9FSKnOSQ9jqypc48InQy58WlwQCSbgTrbl5mOm1fa5cfi3xFYA==}
    hasBin: true
    peerDependencies:
      copy-webpack-plugin: '>=5'
      postcss: '>=7'
    dependencies:
      '@dcloudio/uni-stat': 2.0.2-4060420250429001
      buffer-json: 2.0.0
      clone-deep: 4.0.1
      cross-env: 5.2.1
      envinfo: 6.0.1
      hash-sum: 1.0.2
      loader-utils: 1.4.2
      lru-cache: 4.1.5
      mkdirp: 0.5.6
      module-alias: 2.2.3
      neo-async: 2.6.2
      postcss-import: 12.0.1
      postcss-selector-parser: 5.0.0
      postcss-value-parser: 3.3.1
      strip-json-comments: 2.0.1
      update-check: 1.5.4
      webpack-merge: 4.2.2
      wrap-loader: 0.2.0
      xregexp: 4.0.0
    dev: true

  /@dcloudio/webpack-uni-mp-loader/2.0.2-4060420250429001:
    resolution: {integrity: sha512-LSZLYyT6zKaEBnK51pRUdXBIGQZMMRQMyZ4JlIrPXpzSrE+sMc6Ew7BmMY1XHD+ayYksXXal8Y3AFBhCxlxDmA==}
    dev: true

  /@dcloudio/webpack-uni-pages-loader/2.0.2-4060420250429001:
    resolution: {integrity: sha512-hRfaTiVBC3T+5FznpCGCAqp9OUfGJDiBnUtqmMAlCxcmY8qDWkDzPvsz3p2q49jjjYI9tRb348v07m2xPC2yfQ==}
    dependencies:
      merge: 2.1.1
      strip-json-comments: 2.0.1
    dev: true

  /@discoveryjs/json-ext/0.5.7:
    resolution: {integrity: sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==}
    engines: {node: '>=10.0.0'}
    dev: true

  /@hap-toolkit/compiler/0.6.15:
    resolution: {integrity: sha512-ivGKaBhC5NoNrb9qXhzrqPyWVG1YDEUxkTeveL2oqve4WLlK7h7fJfGjMx/jSvGvLGyhquofF5mxkQzLdCCieg==}
    engines: {node: '>=8.0.0'}
    dependencies:
      '@babel/core': 7.27.1
      '@babel/plugin-transform-template-literals': 7.27.1_@babel+core@7.27.1
      '@hap-toolkit/shared-utils': 0.6.15
      css: 2.2.4
      css-what: 2.1.3
      escodegen: 1.14.3
      esprima: 4.0.1
      hash-sum: 1.0.2
      loader-utils: 1.4.2
      parse5: 3.0.3
      source-map: 0.7.4
      webpack: 4.47.0
    transitivePeerDependencies:
      - supports-color
      - webpack-cli
      - webpack-command
    dev: false

  /@hap-toolkit/debugger/0.6.15:
    resolution: {integrity: sha512-AO2ZHJiqS9gP3Xi/6S63Sl2Tgf81NEhNVjY81RHIhraoEPuTsPPUsUQoYEvU6tO9Y69OXCVT4C1O+kY/LrwY+g==}
    engines: {node: '>=8.0.0'}
    dependencies:
      '@hap-toolkit/shared-utils': 0.6.15
      adb-commander: 0.1.9
      adb-devices-emitter: 0.1.9
      chrome-simple-launcher: 0.1.3
      koa: 2.16.1
      koa-body: 4.2.0
      koa-router: 7.4.0
      koa-static: 5.0.0
      qr-image: 3.2.0
      socket.io: 2.5.1
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
    dev: false

  /@hap-toolkit/dsl-vue/0.6.13_prettier@2.8.8:
    resolution: {integrity: sha512-mIupVZ6gseenCAY+BFLNGttFPcmuy3pGgB5dUUPBxnCvXcZhgAWQenAI/Y3/tz0ubObVjWLfbZKXDGfpLPTd9g==}
    engines: {node: '>=8.0.0'}
    dependencies:
      '@hap-toolkit/compiler': 0.6.15
      '@hap-toolkit/packager': 0.6.13
      '@hap-toolkit/shared-utils': 0.6.15
      css-loader: 2.1.1
      md5: 2.3.0
      mini-css-extract-plugin: 0.5.0
      url-loader: 2.3.0
      vue-loader: 15.11.1_jlzinulpsogv7ychuggq4lj26m
      vue-template-compiler: 2.6.14
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - cache-loader
      - file-loader
      - prettier
      - supports-color
      - webpack
      - webpack-cli
      - webpack-command
    dev: false

  /@hap-toolkit/packager/0.6.13:
    resolution: {integrity: sha512-x0PoC+6rxAcRjryuNbW5o7LBjRxTFUkgbWl2ovOTjrKaChw51JjSqcLR28klk4SiiPLUPpAJxMjz/ooc35yZ3Q==}
    engines: {node: '>=8.0.0'}
    dependencies:
      '@babel/core': 7.27.1
      '@babel/preset-env': 7.27.2_@babel+core@7.27.1
      '@babel/register': 7.27.1_@babel+core@7.27.1
      '@babel/runtime': 7.17.9
      '@hap-toolkit/compiler': 0.6.15
      '@hap-toolkit/shared-utils': 0.6.15
      aaptjs: 1.3.2
      babel-loader: 8.4.1_3rhk6tbchh5vbic6six75ixo2u
      fs-extra: 7.0.1
      hash-sum: 1.0.2
      jsrsasign: 7.2.2
      jszip: 3.10.1
      koa-bodyparser: 4.4.1
      koa-router: 7.4.0
      loader-utils: 1.4.2
      moment: 2.30.1
      qr-image: 3.2.0
      webpack: 4.47.0
    transitivePeerDependencies:
      - supports-color
      - webpack-cli
      - webpack-command
    dev: false

  /@hap-toolkit/server/0.6.13:
    resolution: {integrity: sha512-d29Ivum+MvPL0AEnjHW+jNa8FUqnmCPvwHii8la8OGg9HzTj7Oq7pr9n+VjksbfNI0aWdlf+uzyZEuh9rUxJ7g==}
    engines: {node: '>=8.0.0'}
    dependencies:
      '@babel/runtime': 7.17.9
      '@hap-toolkit/debugger': 0.6.15
      '@hap-toolkit/packager': 0.6.13
      '@hap-toolkit/shared-utils': 0.6.15
      jszip: 3.10.1
      koa: 2.16.1
      koa-body: 4.2.0
      koa-mount: 4.2.0
      koa-router: 7.4.0
      koa-send: 5.0.1
      koa-static: 5.0.0
      opn: 5.5.0
      portfinder: 1.0.37
      qr-image: 3.2.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate
      - webpack-cli
      - webpack-command
    dev: false

  /@hap-toolkit/shared-utils/0.6.15:
    resolution: {integrity: sha512-LxjISCLd4opthpkPIQNkMnVY+P5iWG8X6PwexET8tzWYvIUVNa66NR5lOCeOYMoLaMzxQkLZf1HTw14S7zRezQ==}
    engines: {node: '>=8.0.0'}
    dependencies:
      chalk: 2.4.2
      qrcode-terminal: 0.12.0
    dev: false

  /@hapi/bourne/3.0.0:
    resolution: {integrity: sha512-Waj1cwPXJDucOib4a3bAISsKJVb15MKi9IvmTI/7ssVEm6sywXGjVJDhl6/umt1pK1ZS7PacXU3A1PmFKHEZ2w==}
    dev: false

  /@hapi/hoek/9.3.0:
    resolution: {integrity: sha512-/c6rf4UJlmHlC9b5BaNvzAcFv7HZ2QHaV0D4/HNlBdvFnvQq8RI4kYdhyPCl7Xj+oWvTWQ8ujhqS53LIgAe6KQ==}
    dev: true

  /@hapi/topo/5.1.0:
    resolution: {integrity: sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg==}
    dependencies:
      '@hapi/hoek': 9.3.0
    dev: true

  /@istanbuljs/load-nyc-config/1.1.0:
    resolution: {integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==}
    engines: {node: '>=8'}
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0
    dev: true

  /@istanbuljs/schema/0.1.3:
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==}
    engines: {node: '>=8'}
    dev: true

  /@jest/console/25.5.0:
    resolution: {integrity: sha512-T48kZa6MK1Y6k4b89sexwmSF4YLeZS/Udqg3Jj3jG/cHH+N/sLFCEoXEDMOKugJQ9FxPN1osxIknvKkxt6MKyw==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      chalk: 3.0.0
      jest-message-util: 25.5.0
      jest-util: 25.5.0
      slash: 3.0.0
    dev: true

  /@jest/core/25.5.4:
    resolution: {integrity: sha512-3uSo7laYxF00Dg/DMgbn4xMJKmDdWvZnf89n8Xj/5/AeQ2dOQmn6b6Hkj/MleyzZWXpwv+WSdYWl4cLsy2JsoA==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/console': 25.5.0
      '@jest/reporters': 25.5.1
      '@jest/test-result': 25.5.0
      '@jest/transform': 25.5.1
      '@jest/types': 25.5.0
      ansi-escapes: 4.3.2
      chalk: 3.0.0
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-changed-files: 25.5.0
      jest-config: 25.5.4
      jest-haste-map: 25.5.1
      jest-message-util: 25.5.0
      jest-regex-util: 25.2.6
      jest-resolve: 25.5.1
      jest-resolve-dependencies: 25.5.4
      jest-runner: 25.5.4
      jest-runtime: 25.5.4
      jest-snapshot: 25.5.1
      jest-util: 25.5.0
      jest-validate: 25.5.0
      jest-watcher: 25.5.0
      micromatch: 4.0.8
      p-each-series: 2.2.0
      realpath-native: 2.0.0
      rimraf: 3.0.2
      slash: 3.0.0
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@jest/environment/25.5.0:
    resolution: {integrity: sha512-U2VXPEqL07E/V7pSZMSQCvV5Ea4lqOlT+0ZFijl/i316cRMHvZ4qC+jBdryd+lmRetjQo0YIQr6cVPNxxK87mA==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/fake-timers': 25.5.0
      '@jest/types': 25.5.0
      jest-mock: 25.5.0
    dev: true

  /@jest/fake-timers/25.5.0:
    resolution: {integrity: sha512-9y2+uGnESw/oyOI3eww9yaxdZyHq7XvprfP/eeoCsjqKYts2yRlsHS/SgjPDV8FyMfn2nbMy8YzUk6nyvdLOpQ==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      jest-message-util: 25.5.0
      jest-mock: 25.5.0
      jest-util: 25.5.0
      lolex: 5.1.2
    dev: true

  /@jest/globals/25.5.2:
    resolution: {integrity: sha512-AgAS/Ny7Q2RCIj5kZ+0MuKM1wbF0WMLxbCVl/GOMoCNbODRdJ541IxJ98xnZdVSZXivKpJlNPIWa3QmY0l4CXA==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/environment': 25.5.0
      '@jest/types': 25.5.0
      expect: 25.5.0
    dev: true

  /@jest/reporters/25.5.1:
    resolution: {integrity: sha512-3jbd8pPDTuhYJ7vqiHXbSwTJQNavczPs+f1kRprRDxETeE3u6srJ+f0NPuwvOmk+lmunZzPkYWIFZDLHQPkviw==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@bcoe/v8-coverage': 0.2.3
      '@jest/console': 25.5.0
      '@jest/test-result': 25.5.0
      '@jest/transform': 25.5.1
      '@jest/types': 25.5.0
      chalk: 3.0.0
      collect-v8-coverage: 1.0.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-instrument: 4.0.3
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.7
      jest-haste-map: 25.5.1
      jest-resolve: 25.5.1
      jest-util: 25.5.0
      jest-worker: 25.5.0
      slash: 3.0.0
      source-map: 0.6.1
      string-length: 3.1.0
      terminal-link: 2.1.1
      v8-to-istanbul: 4.1.4
    optionalDependencies:
      node-notifier: 6.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@jest/source-map/25.5.0:
    resolution: {integrity: sha512-eIGx0xN12yVpMcPaVpjXPnn3N30QGJCJQSkEDUt9x1fI1Gdvb07Ml6K5iN2hG7NmMP6FDmtPEssE3z6doOYUwQ==}
    engines: {node: '>= 8.3'}
    dependencies:
      callsites: 3.1.0
      graceful-fs: 4.2.11
      source-map: 0.6.1
    dev: true

  /@jest/test-result/25.5.0:
    resolution: {integrity: sha512-oV+hPJgXN7IQf/fHWkcS99y0smKLU2czLBJ9WA0jHITLst58HpQMtzSYxzaBvYc6U5U6jfoMthqsUlUlbRXs0A==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/console': 25.5.0
      '@jest/types': 25.5.0
      '@types/istanbul-lib-coverage': 2.0.6
      collect-v8-coverage: 1.0.2
    dev: true

  /@jest/test-sequencer/25.5.4:
    resolution: {integrity: sha512-pTJGEkSeg1EkCO2YWq6hbFvKNXk8ejqlxiOg1jBNLnWrgXOkdY6UmqZpwGFXNnRt9B8nO1uWMzLLZ4eCmhkPNA==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/test-result': 25.5.0
      graceful-fs: 4.2.11
      jest-haste-map: 25.5.1
      jest-runner: 25.5.4
      jest-runtime: 25.5.4
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /@jest/transform/25.5.1:
    resolution: {integrity: sha512-Y8CEoVwXb4QwA6Y/9uDkn0Xfz0finGkieuV0xkdF9UtZGJeLukD5nLkaVrVsODB1ojRWlaoD0AJZpVHCSnJEvg==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@babel/core': 7.27.1
      '@jest/types': 25.5.0
      babel-plugin-istanbul: 6.1.1
      chalk: 3.0.0
      convert-source-map: 1.9.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.11
      jest-haste-map: 25.5.1
      jest-regex-util: 25.2.6
      jest-util: 25.5.0
      micromatch: 4.0.8
      pirates: 4.0.7
      realpath-native: 2.0.0
      slash: 3.0.0
      source-map: 0.6.1
      write-file-atomic: 3.0.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@jest/types/25.5.0:
    resolution: {integrity: sha512-OXD0RgQ86Tu3MazKo8bnrkDRaDXXMGUqd+kTtLtK1Zb7CRzQcaSRPPPV37SvYTdevXEBVxe0HXylEjs8ibkmCw==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 1.1.2
      '@types/yargs': 15.0.19
      chalk: 3.0.0
    dev: true

  /@jimp/bmp/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-keMOc5woiDmONXsB/6aXLR4Z5Q+v8lFq3EY2rcj2FmstbDMhRuGbmcBxlEgOqfRjwvtf/wOtJ3Of37oAWtVfLg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      bmp-js: 0.1.0
      core-js: 3.42.0
    dev: true

  /@jimp/core/0.10.3_debug@4.4.1:
    resolution: {integrity: sha512-Gd5IpL3U2bFIO57Fh/OA3HCpWm4uW/pU01E75rI03BXfTdz3T+J7TwvyG1XaqsQ7/DSlS99GXtLQPlfFIe28UA==}
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/utils': 0.10.3
      any-base: 1.1.0
      buffer: 5.7.1
      core-js: 3.42.0
      exif-parser: 0.1.12
      file-type: 9.0.0
      load-bmfont: 1.4.2_debug@4.4.1
      mkdirp: 0.5.6
      phin: 2.9.3
      pixelmatch: 4.0.2
      tinycolor2: 1.6.0
    transitivePeerDependencies:
      - debug
    dev: true

  /@jimp/custom/0.10.3_debug@4.4.1:
    resolution: {integrity: sha512-nZmSI+jwTi5IRyNLbKSXQovoeqsw+D0Jn0SxW08wYQvdkiWA8bTlDQFgQ7HVwCAKBm8oKkDB/ZEo9qvHJ+1gAQ==}
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/core': 0.10.3_debug@4.4.1
      core-js: 3.42.0
    transitivePeerDependencies:
      - debug
    dev: true

  /@jimp/gif/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-vjlRodSfz1CrUvvrnUuD/DsLK1GHB/yDZXHthVdZu23zYJIW7/WrIiD1IgQ5wOMV7NocfrvPn2iqUfBP81/WWA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
      omggif: 1.0.10
    dev: true

  /@jimp/jpeg/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-AAANwgUZOt6f6P7LZxY9lyJ9xclqutYJlsxt3JbriXUGJgrrFAIkcKcqv1nObgmQASSAQKYaMV9KdHjMlWFKlQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
      jpeg-js: 0.3.7
    dev: true

  /@jimp/plugin-blit/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-5zlKlCfx4JWw9qUVC7GI4DzXyxDWyFvgZLaoGFoT00mlXlN75SarlDwc9iZ/2e2kp4bJWxz3cGgG4G/WXrbg3Q==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-blur/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-cTOK3rjh1Yjh23jSfA6EHCHjsPJDEGLC8K2y9gM7dnTUK1y9NNmkFS23uHpyjgsWFIoH9oRh2SpEs3INjCpZhQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-circle/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-51GAPIVelqAcfuUpaM5JWJ0iWl4vEjNXB7p4P7SX5udugK5bxXUjO6KA2qgWmdpHuCKtoNgkzWU9fNSuYp7tCA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-color/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-RgeHUElmlTH7vpI4WyQrz6u59spiKfVQbsG/XUzfWGamFSixa24ZDwX/yV/Ts+eNaz7pZeIuv533qmKPvw2ujg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
      tinycolor2: 1.6.0
    dev: true

  /@jimp/plugin-contain/0.10.3_gbysilybqlln2zzvva5cesir7y:
    resolution: {integrity: sha512-bYJKW9dqzcB0Ihc6u7jSyKa3juStzbLs2LFr6fu8TzA2WkMS/R8h+ddkiO36+F9ILTWHP0CIA3HFe5OdOGcigw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blit': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
      '@jimp/plugin-scale': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/plugin-blit': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-resize': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-scale': 0.10.3_bokkgd4ue6io5t5nqdyd3t3ie4
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-cover/0.10.3_j5v5phehpdwz2oywu3fuwsjdjm:
    resolution: {integrity: sha512-pOxu0cM0BRPzdV468n4dMocJXoMbTnARDY/EpC3ZW15SpMuc/dr1KhWQHgoQX5kVW1Wt8zgqREAJJCQ5KuPKDA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-crop': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
      '@jimp/plugin-scale': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/plugin-crop': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-resize': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-scale': 0.10.3_bokkgd4ue6io5t5nqdyd3t3ie4
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-crop/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-nB7HgOjjl9PgdHr076xZ3Sr6qHYzeBYBs9qvs3tfEEUeYMNnvzgCCGtUl6eMakazZFCMk3mhKmcB9zQuHFOvkg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-displace/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-8t3fVKCH5IVqI4lewe4lFFjpxxr69SQCz5/tlpDLQZsrNScNJivHdQ09zljTrVTCSgeCqQJIKgH2Q7Sk/pAZ0w==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-dither/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-JCX/oNSnEg1kGQ8ffZ66bEgQOLCY3Rn+lrd6v1jjLy/mn9YVZTMsxLtGCXpiCDC2wG/KTmi4862ysmP9do9dAQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-fisheye/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-RRZb1wqe+xdocGcFtj2xHU7sF7xmEZmIa6BmrfSchjyA2b32TGPWKnP3qyj7p6LWEsXn+19hRYbjfyzyebPElQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-flip/0.10.3_er4ccqdvpmjzdatuaf42td42ii:
    resolution: {integrity: sha512-0epbi8XEzp0wmSjoW9IB0iMu0yNF17aZOxLdURCN3Zr+8nWPs5VNIMqSVa1Y62GSyiMDpVpKF/ITiXre+EqrPg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-rotate': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/plugin-rotate': 0.10.3_val3q4qgj5oh6ukcard42sxi6q
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-gaussian/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-25eHlFbHUDnMMGpgRBBeQ2AMI4wsqCg46sue0KklI+c2BaZ+dGXmJA5uT8RTOrt64/K9Wz5E+2n7eBnny4dfpQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-invert/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-effYSApWY/FbtlzqsKXlTLkgloKUiHBKjkQnqh5RL4oQxh/33j6aX+HFdDyQKtsXb8CMd4xd7wyiD2YYabTa0g==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-mask/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-twrg8q8TIhM9Z6Jcu9/5f+OCAPaECb0eKrrbbIajJqJ3bCUlj5zbfgIhiQIzjPJ6KjpnFPSqHQfHkU1Vvk/nVw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-normalize/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-xkb5eZI/mMlbwKkDN79+1/t/+DBo8bBXZUMsT4gkFgMRKNRZ6NQPxlv1d3QpRzlocsl6UMxrHnhgnXdLAcgrXw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-print/0.10.3_nlsjaks3ttts4nvwhrzjqsgkwe:
    resolution: {integrity: sha512-wjRiI6yjXsAgMe6kVjizP+RgleUCLkH256dskjoNvJzmzbEfO7xQw9g6M02VET+emnbY0CO83IkrGm2q43VRyg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blit': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/plugin-blit': 0.10.3_@jimp+custom@0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
      load-bmfont: 1.4.2_debug@4.4.1
    transitivePeerDependencies:
      - debug
    dev: true

  /@jimp/plugin-resize/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-rf8YmEB1d7Sg+g4LpqF0Mp+dfXfb6JFJkwlAIWPUOR7lGsPWALavEwTW91c0etEdnp0+JB9AFpy6zqq7Lwkq6w==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-rotate/0.10.3_val3q4qgj5oh6ukcard42sxi6q:
    resolution: {integrity: sha512-YXLlRjm18fkW9MOHUaVAxWjvgZM851ofOipytz5FyKp4KZWDLk+dZK1JNmVmK7MyVmAzZ5jsgSLhIgj+GgN0Eg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blit': '>=0.3.5'
      '@jimp/plugin-crop': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/plugin-blit': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-crop': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-resize': 0.10.3_@jimp+custom@0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-scale/0.10.3_bokkgd4ue6io5t5nqdyd3t3ie4:
    resolution: {integrity: sha512-5DXD7x7WVcX1gUgnlFXQa8F+Q3ThRYwJm+aesgrYvDOY+xzRoRSdQvhmdd4JEEue3lyX44DvBSgCIHPtGcEPaw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/plugin-resize': 0.10.3_@jimp+custom@0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-shadow/0.10.3_6w4bogtstk4ko7nb73twxuezqq:
    resolution: {integrity: sha512-/nkFXpt2zVcdP4ETdkAUL0fSzyrC5ZFxdcphbYBodqD7fXNqChS/Un1eD4xCXWEpW8cnG9dixZgQgStjywH0Mg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blur': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/plugin-blur': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-resize': 0.10.3_@jimp+custom@0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugin-threshold/0.10.3_rhidij2fqubngmyim7jkn7kfbi:
    resolution: {integrity: sha512-Dzh0Yq2wXP2SOnxcbbiyA4LJ2luwrdf1MghNIt9H+NX7B+IWw/N8qA2GuSm9n4BPGSLluuhdAWJqHcTiREriVA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-color': '>=0.8.0'
      '@jimp/plugin-resize': '>=0.8.0'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/plugin-color': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-resize': 0.10.3_@jimp+custom@0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
    dev: true

  /@jimp/plugins/0.10.3_6b7n5g776ny5s7kpuvjlc4hz4a:
    resolution: {integrity: sha512-jTT3/7hOScf0EIKiAXmxwayHhryhc1wWuIe3FrchjDjr9wgIGNN2a7XwCgPl3fML17DXK1x8EzDneCdh261bkw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/plugin-blit': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-blur': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-circle': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-color': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-contain': 0.10.3_gbysilybqlln2zzvva5cesir7y
      '@jimp/plugin-cover': 0.10.3_j5v5phehpdwz2oywu3fuwsjdjm
      '@jimp/plugin-crop': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-displace': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-dither': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-fisheye': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-flip': 0.10.3_er4ccqdvpmjzdatuaf42td42ii
      '@jimp/plugin-gaussian': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-invert': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-mask': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-normalize': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-print': 0.10.3_nlsjaks3ttts4nvwhrzjqsgkwe
      '@jimp/plugin-resize': 0.10.3_@jimp+custom@0.10.3
      '@jimp/plugin-rotate': 0.10.3_val3q4qgj5oh6ukcard42sxi6q
      '@jimp/plugin-scale': 0.10.3_bokkgd4ue6io5t5nqdyd3t3ie4
      '@jimp/plugin-shadow': 0.10.3_6w4bogtstk4ko7nb73twxuezqq
      '@jimp/plugin-threshold': 0.10.3_rhidij2fqubngmyim7jkn7kfbi
      core-js: 3.42.0
      timm: 1.7.1
    transitivePeerDependencies:
      - debug
    dev: true

  /@jimp/png/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-YKqk/dkl+nGZxSYIDQrqhmaP8tC3IK8H7dFPnnzFVvbhDnyYunqBZZO3SaZUKTichClRw8k/CjBhbc+hifSGWg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/utils': 0.10.3
      core-js: 3.42.0
      pngjs: 3.4.0
    dev: true

  /@jimp/tiff/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-7EsJzZ5Y/EtinkBGuwX3Bi4S+zgbKouxjt9c82VJTRJOQgLWsE/RHqcyRCOQBhHAZ9QexYmDz34medfLKdoX0g==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      core-js: 3.42.0
      utif: 2.0.1
    dev: true

  /@jimp/types/0.10.3_@jimp+custom@0.10.3:
    resolution: {integrity: sha512-XGmBakiHZqseSWr/puGN+CHzx0IKBSpsKlmEmsNV96HKDiP6eu8NSnwdGCEq2mmIHe0JNcg1hqg59hpwtQ7Tiw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/bmp': 0.10.3_@jimp+custom@0.10.3
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/gif': 0.10.3_@jimp+custom@0.10.3
      '@jimp/jpeg': 0.10.3_@jimp+custom@0.10.3
      '@jimp/png': 0.10.3_@jimp+custom@0.10.3
      '@jimp/tiff': 0.10.3_@jimp+custom@0.10.3
      core-js: 3.42.0
      timm: 1.7.1
    dev: true

  /@jimp/utils/0.10.3:
    resolution: {integrity: sha512-VcSlQhkil4ReYmg1KkN+WqHyYfZ2XfZxDsKAHSfST1GEz/RQHxKZbX+KhFKtKflnL0F4e6DlNQj3vznMNXCR2w==}
    dependencies:
      '@babel/runtime': 7.17.9
      core-js: 3.42.0
      regenerator-runtime: 0.13.11
    dev: true

  /@jridgewell/gen-mapping/0.3.8:
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  /@jridgewell/resolve-uri/3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/set-array/1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/source-map/0.3.6:
    resolution: {integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
    dev: true

  /@jridgewell/sourcemap-codec/1.5.0:
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  /@jridgewell/trace-mapping/0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  /@leichtgewicht/ip-codec/2.0.5:
    resolution: {integrity: sha512-Vo+PSpZG2/fmgmiNzYK9qWRh8h/CHrwD0mo1h1DzL4yzHNSfWYujGTYsWGreD000gcgmZ7K4Ys6Tx9TxtsKdDw==}
    dev: true

  /@node-ipc/js-queue/2.0.3:
    resolution: {integrity: sha512-fL1wpr8hhD5gT2dA1qifeVaoDFlQR5es8tFuKqjHX+kdOtdNHnxkVZbtIrR2rxnMFvehkjaZRNV2H/gPXlb0hw==}
    engines: {node: '>=1.0.0'}
    dependencies:
      easy-stack: 1.0.1
    dev: true

  /@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: true

  /@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}
    dev: true

  /@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1
    dev: true

  /@parcel/watcher-android-arm64/2.5.1:
    resolution: {integrity: sha512-KF8+j9nNbUN8vzOFDpRMsaKBHZ/mcjEjMToVMJOhTozkDonQFFrRcfdLWn6yWKCmJKmdVxSgHiYvTCef4/qcBA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-darwin-arm64/2.5.1:
    resolution: {integrity: sha512-eAzPv5osDmZyBhou8PoF4i6RQXAfeKL9tjb3QzYuccXFMQU0ruIc/POh30ePnaOyD1UXdlKguHBmsTs53tVoPw==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-darwin-x64/2.5.1:
    resolution: {integrity: sha512-1ZXDthrnNmwv10A0/3AJNZ9JGlzrF82i3gNQcWOzd7nJ8aj+ILyW1MTxVk35Db0u91oD5Nlk9MBiujMlwmeXZg==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-freebsd-x64/2.5.1:
    resolution: {integrity: sha512-SI4eljM7Flp9yPuKi8W0ird8TI/JK6CSxju3NojVI6BjHsTyK7zxA9urjVjEKJ5MBYC+bLmMcbAWlZ+rFkLpJQ==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-linux-arm-glibc/2.5.1:
    resolution: {integrity: sha512-RCdZlEyTs8geyBkkcnPWvtXLY44BCeZKmGYRtSgtwwnHR4dxfHRG3gR99XdMEdQ7KeiDdasJwwvNSF5jKtDwdA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-linux-arm-musl/2.5.1:
    resolution: {integrity: sha512-6E+m/Mm1t1yhB8X412stiKFG3XykmgdIOqhjWj+VL8oHkKABfu/gjFj8DvLrYVHSBNC+/u5PeNrujiSQ1zwd1Q==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-linux-arm64-glibc/2.5.1:
    resolution: {integrity: sha512-LrGp+f02yU3BN9A+DGuY3v3bmnFUggAITBGriZHUREfNEzZh/GO06FF5u2kx8x+GBEUYfyTGamol4j3m9ANe8w==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-linux-arm64-musl/2.5.1:
    resolution: {integrity: sha512-cFOjABi92pMYRXS7AcQv9/M1YuKRw8SZniCDw0ssQb/noPkRzA+HBDkwmyOJYp5wXcsTrhxO0zq1U11cK9jsFg==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-linux-x64-glibc/2.5.1:
    resolution: {integrity: sha512-GcESn8NZySmfwlTsIur+49yDqSny2IhPeZfXunQi48DMugKeZ7uy1FX83pO0X22sHntJ4Ub+9k34XQCX+oHt2A==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-linux-x64-musl/2.5.1:
    resolution: {integrity: sha512-n0E2EQbatQ3bXhcH2D1XIAANAcTZkQICBPVaxMeaCVBtOpBZpWJuf7LwyWPSBDITb7In8mqQgJ7gH8CILCURXg==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-win32-arm64/2.5.1:
    resolution: {integrity: sha512-RFzklRvmc3PkjKjry3hLF9wD7ppR4AKcWNzH7kXR7GUe0Igb3Nz8fyPwtZCSquGrhU5HhUNDr/mKBqj7tqA2Vw==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-win32-ia32/2.5.1:
    resolution: {integrity: sha512-c2KkcVN+NJmuA7CGlaGD1qJh1cLfDnQsHjE89E60vUEMlqduHGCdCLJCID5geFVM0dOtA3ZiIO8BoEQmzQVfpQ==}
    engines: {node: '>= 10.0.0'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher-win32-x64/2.5.1:
    resolution: {integrity: sha512-9lHBdJITeNR++EvSQVUcaZoWupyHfXe1jZvGZ06O/5MflPcuPLtEphScIBL+AiCWBO46tDSHzWyD0uDmmZqsgA==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@parcel/watcher/2.5.1:
    resolution: {integrity: sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==}
    engines: {node: '>= 10.0.0'}
    requiresBuild: true
    dependencies:
      detect-libc: 1.0.3
      is-glob: 4.0.3
      micromatch: 4.0.8
      node-addon-api: 7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': 2.5.1
      '@parcel/watcher-darwin-arm64': 2.5.1
      '@parcel/watcher-darwin-x64': 2.5.1
      '@parcel/watcher-freebsd-x64': 2.5.1
      '@parcel/watcher-linux-arm-glibc': 2.5.1
      '@parcel/watcher-linux-arm-musl': 2.5.1
      '@parcel/watcher-linux-arm64-glibc': 2.5.1
      '@parcel/watcher-linux-arm64-musl': 2.5.1
      '@parcel/watcher-linux-x64-glibc': 2.5.1
      '@parcel/watcher-linux-x64-musl': 2.5.1
      '@parcel/watcher-win32-arm64': 2.5.1
      '@parcel/watcher-win32-ia32': 2.5.1
      '@parcel/watcher-win32-x64': 2.5.1
    dev: true
    optional: true

  /@polka/url/1.0.0-next.29:
    resolution: {integrity: sha512-wwQAWhWSuHaag8c4q/KN/vCoeOJYshAIvMQwD4GpSb3OiZklFfvAgmj0VCBBImRpuF/aFgIRzllXlVX93Jevww==}
    dev: true

  /@sideway/address/4.1.5:
    resolution: {integrity: sha512-IqO/DUQHUkPeixNQ8n0JA6102hT9CmaljNTPmQ1u8MEhBo/R4Q8eKLN/vGZxuebwOroDB4cbpjheD4+/sKFK4Q==}
    dependencies:
      '@hapi/hoek': 9.3.0
    dev: true

  /@sideway/formula/3.0.1:
    resolution: {integrity: sha512-/poHZJJVjx3L+zVD6g9KgHfYnb443oi7wLu/XKojDviHy6HOEOA6z1Trk5aR1dGcmPenJEgb2sK2I80LeS3MIg==}
    dev: true

  /@sideway/pinpoint/2.0.0:
    resolution: {integrity: sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ==}
    dev: true

  /@sinonjs/commons/1.8.6:
    resolution: {integrity: sha512-Ky+XkAkqPZSm3NLBeUng77EBQl3cmeJhITaGHdYH8kjVB+aun3S4XBRti2zt17mtt0mIUDiNxYeoJm6drVvBJQ==}
    dependencies:
      type-detect: 4.0.8
    dev: true

  /@soda/friendly-errors-webpack-plugin/1.8.1_webpack@5.99.8:
    resolution: {integrity: sha512-h2ooWqP8XuFqTXT+NyAFbrArzfQA7R6HTezADrvD9Re8fxMLTPPniLdqVTdDaO0eIoLaAwKT+d6w+5GeTk7Vbg==}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      chalk: 3.0.0
      error-stack-parser: 2.1.4
      string-width: 4.2.3
      strip-ansi: 6.0.1
      webpack: 5.99.8
    dev: true

  /@soda/get-current-script/1.0.2:
    resolution: {integrity: sha512-T7VNNlYVM1SgQ+VsMYhnDkcGmWhQdL0bDyGm5TlQ3GBXnJscEClUUOKduWTmm2zCnvNLC1hc3JpuXjs/nFOc5w==}
    dev: true

  /@trysound/sax/0.2.0:
    resolution: {integrity: sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==}
    engines: {node: '>=10.13.0'}
    dev: true

  /@types/babel__core/7.20.5:
    resolution: {integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==}
    dependencies:
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1
      '@types/babel__generator': 7.27.0
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.7
    dev: true

  /@types/babel__generator/7.27.0:
    resolution: {integrity: sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==}
    dependencies:
      '@babel/types': 7.27.1
    dev: true

  /@types/babel__template/7.4.4:
    resolution: {integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==}
    dependencies:
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1
    dev: true

  /@types/babel__traverse/7.20.7:
    resolution: {integrity: sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==}
    dependencies:
      '@babel/types': 7.27.1
    dev: true

  /@types/body-parser/1.19.5:
    resolution: {integrity: sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==}
    dependencies:
      '@types/connect': 3.4.38
      '@types/node': 22.15.17
    dev: true

  /@types/bonjour/3.5.13:
    resolution: {integrity: sha512-z9fJ5Im06zvUL548KvYNecEVlA7cVDkGUi6kZusb04mpyEFKCIZJvloCcmpmLaIahDpOQGHaHmG6imtPMmPXGQ==}
    dependencies:
      '@types/node': 22.15.17
    dev: true

  /@types/connect-history-api-fallback/1.5.4:
    resolution: {integrity: sha512-n6Cr2xS1h4uAulPRdlw6Jl6s1oG8KrVilPN2yUITEs+K48EzMJJ3W1xy8K5eWuFvjp3R74AOIGSmp2UfBJ8HFw==}
    dependencies:
      '@types/express-serve-static-core': 5.0.6
      '@types/node': 22.15.17
    dev: true

  /@types/connect/3.4.38:
    resolution: {integrity: sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==}
    dependencies:
      '@types/node': 22.15.17
    dev: true

  /@types/eslint-scope/3.7.7:
    resolution: {integrity: sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==}
    dependencies:
      '@types/eslint': 9.6.1
      '@types/estree': 1.0.7
    dev: true

  /@types/eslint/9.6.1:
    resolution: {integrity: sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==}
    dependencies:
      '@types/estree': 1.0.7
      '@types/json-schema': 7.0.15
    dev: true

  /@types/estree/1.0.7:
    resolution: {integrity: sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==}
    dev: true

  /@types/express-serve-static-core/4.19.6:
    resolution: {integrity: sha512-N4LZ2xG7DatVqhCZzOGb1Yi5lMbXSZcmdLDe9EzSndPV2HpWYWzRbaerl2n27irrm94EPpprqa8KpskPT085+A==}
    dependencies:
      '@types/node': 22.15.17
      '@types/qs': 6.9.18
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.4
    dev: true

  /@types/express-serve-static-core/5.0.6:
    resolution: {integrity: sha512-3xhRnjJPkULekpSzgtoNYYcTWgEZkp4myc+Saevii5JPnHNvHMRlBSHDbs7Bh1iPPoVTERHEZXyhyLbMEsExsA==}
    dependencies:
      '@types/node': 22.15.17
      '@types/qs': 6.9.18
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.4
    dev: true

  /@types/express/4.17.21:
    resolution: {integrity: sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==}
    dependencies:
      '@types/body-parser': 1.19.5
      '@types/express-serve-static-core': 4.19.6
      '@types/qs': 6.9.18
      '@types/serve-static': 1.15.7
    dev: true

  /@types/formidable/1.2.8:
    resolution: {integrity: sha512-6psvrUy5VDYb+yaPJReF1WrRsz+FBwyJutK9Twz1Efa27tm07bARNIkK2B8ZPWq80dXqpKfrxTO96xrtPp+AuA==}
    dependencies:
      '@types/node': 22.15.17
    dev: false

  /@types/graceful-fs/4.1.9:
    resolution: {integrity: sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==}
    dependencies:
      '@types/node': 22.15.17
    dev: true

  /@types/html-minifier-terser/6.1.0:
    resolution: {integrity: sha512-oh/6byDPnL1zeNXFrDXFLyZjkr1MsBG667IM792caf1L2UPOOMf65NFzjUH/ltyfwjAGfs1rsX1eftK0jC/KIg==}
    dev: true

  /@types/http-errors/2.0.4:
    resolution: {integrity: sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==}
    dev: true

  /@types/http-proxy/1.17.16:
    resolution: {integrity: sha512-sdWoUajOB1cd0A8cRRQ1cfyWNbmFKLAqBB89Y8x5iYyG/mkJHc0YUH8pdWBy2omi9qtCpiIgGjuwO0dQST2l5w==}
    dependencies:
      '@types/node': 22.15.17
    dev: true

  /@types/istanbul-lib-coverage/2.0.6:
    resolution: {integrity: sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==}
    dev: true

  /@types/istanbul-lib-report/3.0.3:
    resolution: {integrity: sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
    dev: true

  /@types/istanbul-reports/1.1.2:
    resolution: {integrity: sha512-P/W9yOX/3oPZSpaYOCQzGqgCQRXn0FFO/V8bWrCQs+wLmvVVxk6CRBXALEvNs9OHIatlnlFokfhuDo2ug01ciw==}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-lib-report': 3.0.3
    dev: true

  /@types/json-schema/7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  /@types/mime/1.3.5:
    resolution: {integrity: sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==}
    dev: true

  /@types/minimist/1.2.5:
    resolution: {integrity: sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==}
    dev: true

  /@types/node-forge/1.3.11:
    resolution: {integrity: sha512-FQx220y22OKNTqaByeBGqHWYz4cl94tpcxeFdvBo3wjG6XPBuZ0BNgNZRV5J5TFmmcsJ4IzsLkmGRiQbnYsBEQ==}
    dependencies:
      '@types/node': 22.15.17
    dev: true

  /@types/node/22.15.17:
    resolution: {integrity: sha512-wIX2aSZL5FE+MR0JlvF87BNVrtFWf6AE6rxSE9X7OwnVvoyCQjpzSRJ+M87se/4QCkCiebQAqrJ0y6fwIyi7nw==}
    dependencies:
      undici-types: 6.21.0

  /@types/normalize-package-data/2.4.4:
    resolution: {integrity: sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==}
    dev: true

  /@types/parse-json/4.0.2:
    resolution: {integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==}
    dev: true

  /@types/prettier/1.19.1:
    resolution: {integrity: sha512-5qOlnZscTn4xxM5MeGXAMOsIOIKIbh9e85zJWfBRVPlRMEVawzoPhINYbRGkBZCI8LxvBe7tJCdWiarA99OZfQ==}
    dev: true

  /@types/qs/6.9.18:
    resolution: {integrity: sha512-kK7dgTYDyGqS+e2Q4aK9X3D7q234CIZ1Bv0q/7Z5IwRDoADNU81xXJK/YVyLbLTZCoIwUoDoffFeF+p/eIklAA==}
    dev: true

  /@types/range-parser/1.2.7:
    resolution: {integrity: sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==}
    dev: true

  /@types/retry/0.12.0:
    resolution: {integrity: sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==}
    dev: true

  /@types/send/0.17.4:
    resolution: {integrity: sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==}
    dependencies:
      '@types/mime': 1.3.5
      '@types/node': 22.15.17
    dev: true

  /@types/serve-index/1.9.4:
    resolution: {integrity: sha512-qLpGZ/c2fhSs5gnYsQxtDEq3Oy8SXPClIXkW5ghvAvsNuVSA8k+gCONcUCS/UjLEYvYps+e8uBtfgXgvhwfNug==}
    dependencies:
      '@types/express': 4.17.21
    dev: true

  /@types/serve-static/1.15.7:
    resolution: {integrity: sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw==}
    dependencies:
      '@types/http-errors': 2.0.4
      '@types/node': 22.15.17
      '@types/send': 0.17.4
    dev: true

  /@types/sockjs/0.3.36:
    resolution: {integrity: sha512-MK9V6NzAS1+Ud7JV9lJLFqW85VbC9dq3LmwZCuBe4wBDgKC0Kj/jd8Xl+nSviU+Qc3+m7umHHyHg//2KSa0a0Q==}
    dependencies:
      '@types/node': 22.15.17
    dev: true

  /@types/stack-utils/1.0.1:
    resolution: {integrity: sha512-l42BggppR6zLmpfU6fq9HEa2oGPEI8yrSPL3GITjfRInppYFahObbIQOQK3UGxEnyQpltZLaPe75046NOZQikw==}
    dev: true

  /@types/ws/8.18.1:
    resolution: {integrity: sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==}
    dependencies:
      '@types/node': 22.15.17
    dev: true

  /@types/yargs-parser/21.0.3:
    resolution: {integrity: sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==}
    dev: true

  /@types/yargs/15.0.19:
    resolution: {integrity: sha512-2XUaGVmyQjgyAZldf0D0c14vvo/yv0MhQBSTJcejMMaitsn3nxCB6TmH4G0ZQf+uxROOa9mpanoSm8h6SG/1ZA==}
    dependencies:
      '@types/yargs-parser': 21.0.3
    dev: true

  /@vue/babel-helper-vue-jsx-merge-props/1.4.0:
    resolution: {integrity: sha512-JkqXfCkUDp4PIlFdDQ0TdXoIejMtTHP67/pvxlgeY+u5k3LEdKuWZ3LK6xkxo52uDoABIVyRwqVkfLQJhk7VBA==}
    dev: true

  /@vue/babel-helper-vue-transform-on/1.4.0:
    resolution: {integrity: sha512-mCokbouEQ/ocRce/FpKCRItGo+013tHg7tixg3DUNS+6bmIchPt66012kBMm476vyEIJPafrvOf4E5OYj3shSw==}
    dev: true

  /@vue/babel-plugin-jsx/1.4.0_@babel+core@7.27.1:
    resolution: {integrity: sha512-9zAHmwgMWlaN6qRKdrg1uKsBKHvnUU+Py+MOCTuYZBoZsopa90Di10QRjB+YPnVss0BZbG/H5XFwJY1fTxJWhA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    peerDependenciesMeta:
      '@babel/core':
        optional: true
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1_@babel+core@7.27.1
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.1
      '@babel/types': 7.27.1
      '@vue/babel-helper-vue-transform-on': 1.4.0
      '@vue/babel-plugin-resolve-type': 1.4.0_@babel+core@7.27.1
      '@vue/shared': 3.5.13
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@vue/babel-plugin-resolve-type/1.4.0_@babel+core@7.27.1:
    resolution: {integrity: sha512-4xqDRRbQQEWHQyjlYSgZsWj44KfiF6D+ktCuXyZ8EnVDYV3pztmXJDf1HveAjUAXxAnR8daCQT51RneWWxtTyQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/core': 7.27.1
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/parser': 7.27.2
      '@vue/compiler-sfc': 3.5.13
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@vue/babel-plugin-transform-vue-jsx/1.4.0_@babel+core@7.27.1:
    resolution: {integrity: sha512-Fmastxw4MMx0vlgLS4XBX0XiBbUFzoMGeVXuMV08wyOfXdikAFqBTuYPR0tlk+XskL19EzHc39SgjrPGY23JnA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-module-imports': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1_@babel+core@7.27.1
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      html-tags: 2.0.0
      lodash.kebabcase: 4.1.1
      svg-tags: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@vue/babel-preset-app/5.0.8_vue@2.6.14:
    resolution: {integrity: sha512-yl+5qhpjd8e1G4cMXfORkkBlvtPCIgmRf3IYCWYDKIQ7m+PPa5iTm4feiNmCMD6yGqQWMhhK/7M3oWGL9boKwg==}
    peerDependencies:
      vue: ^2 || ^3.2.13
    peerDependenciesMeta:
      core-js:
        optional: true
      vue:
        optional: true
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-imports': 7.27.1
      '@babel/plugin-proposal-class-properties': 7.18.6_@babel+core@7.27.1
      '@babel/plugin-proposal-decorators': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-syntax-dynamic-import': 7.8.3_@babel+core@7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1_@babel+core@7.27.1
      '@babel/plugin-transform-runtime': 7.27.1_@babel+core@7.27.1
      '@babel/preset-env': 7.27.2_@babel+core@7.27.1
      '@babel/runtime': 7.17.9
      '@vue/babel-plugin-jsx': 1.4.0_@babel+core@7.27.1
      '@vue/babel-preset-jsx': 1.4.0_hug53otkmncm3o44d4xm44faay
      babel-plugin-dynamic-import-node: 2.3.3
      core-js: 3.42.0
      core-js-compat: 3.42.0
      semver: 7.7.2
      vue: 2.6.14
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@vue/babel-preset-jsx/1.4.0_hug53otkmncm3o44d4xm44faay:
    resolution: {integrity: sha512-QmfRpssBOPZWL5xw7fOuHNifCQcNQC1PrOo/4fu6xlhlKJJKSA3HqX92Nvgyx8fqHZTUGMPHmFA+IDqwXlqkSA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
      vue: '*'
    peerDependenciesMeta:
      vue:
        optional: true
    dependencies:
      '@babel/core': 7.27.1
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0_@babel+core@7.27.1
      '@vue/babel-sugar-composition-api-inject-h': 1.4.0_@babel+core@7.27.1
      '@vue/babel-sugar-composition-api-render-instance': 1.4.0_@babel+core@7.27.1
      '@vue/babel-sugar-functional-vue': 1.4.0_@babel+core@7.27.1
      '@vue/babel-sugar-inject-h': 1.4.0_@babel+core@7.27.1
      '@vue/babel-sugar-v-model': 1.4.0_@babel+core@7.27.1
      '@vue/babel-sugar-v-on': 1.4.0_@babel+core@7.27.1
      vue: 2.6.14
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@vue/babel-sugar-composition-api-inject-h/1.4.0_@babel+core@7.27.1:
    resolution: {integrity: sha512-VQq6zEddJHctnG4w3TfmlVp5FzDavUSut/DwR0xVoe/mJKXyMcsIibL42wPntozITEoY90aBV0/1d2KjxHU52g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1_@babel+core@7.27.1
    dev: true

  /@vue/babel-sugar-composition-api-render-instance/1.4.0_@babel+core@7.27.1:
    resolution: {integrity: sha512-6ZDAzcxvy7VcnCjNdHJ59mwK02ZFuP5CnucloidqlZwVQv5CQLijc3lGpR7MD3TWFi78J7+a8J56YxbCtHgT9Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1_@babel+core@7.27.1
    dev: true

  /@vue/babel-sugar-functional-vue/1.4.0_@babel+core@7.27.1:
    resolution: {integrity: sha512-lTEB4WUFNzYt2In6JsoF9sAYVTo84wC4e+PoZWSgM6FUtqRJz7wMylaEhSRgG71YF+wfLD6cc9nqVeXN2rwBvw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1_@babel+core@7.27.1
    dev: true

  /@vue/babel-sugar-inject-h/1.4.0_@babel+core@7.27.1:
    resolution: {integrity: sha512-muwWrPKli77uO2fFM7eA3G1lAGnERuSz2NgAxuOLzrsTlQl8W4G+wwbM4nB6iewlKbwKRae3nL03UaF5ffAPMA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1_@babel+core@7.27.1
    dev: true

  /@vue/babel-sugar-v-model/1.4.0_@babel+core@7.27.1:
    resolution: {integrity: sha512-0t4HGgXb7WHYLBciZzN5s0Hzqan4Ue+p/3FdQdcaHAb7s5D9WZFGoSxEZHrR1TFVZlAPu1bejTKGeAzaaG3NCQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1_@babel+core@7.27.1
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0_@babel+core@7.27.1
      camelcase: 5.3.1
      html-tags: 2.0.0
      svg-tags: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@vue/babel-sugar-v-on/1.4.0_@babel+core@7.27.1:
    resolution: {integrity: sha512-m+zud4wKLzSKgQrWwhqRObWzmTuyzl6vOP7024lrpeJM4x2UhQtRDLgYjXAw9xBXjCwS0pP9kXjg91F9ZNo9JA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1_@babel+core@7.27.1
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0_@babel+core@7.27.1
      camelcase: 5.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@vue/cli-overlay/5.0.8:
    resolution: {integrity: sha512-KmtievE/B4kcXp6SuM2gzsnSd8WebkQpg3XaB6GmFh1BJGRqa1UiW9up7L/Q67uOdTigHxr5Ar2lZms4RcDjwQ==}
    dev: true

  /@vue/cli-plugin-babel/5.0.8_sqt6tuffjadljs7xoqwshujxxu:
    resolution: {integrity: sha512-a4qqkml3FAJ3auqB2kN2EMPocb/iu0ykeELwed+9B1c1nQ1HKgslKMHMPavYx3Cd/QAx2mBD4hwKBqZXEI/CsQ==}
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0 || ^5.0.0-0
    dependencies:
      '@babel/core': 7.27.1
      '@vue/babel-preset-app': 5.0.8_vue@2.6.14
      '@vue/cli-service': 5.0.8_upvy4r7hct7s4hgklxc7ih6yii
      '@vue/cli-shared-utils': 5.0.8
      babel-loader: 8.4.1_bm7m7jjqcfz7ytm4b3ejdei32u
      thread-loader: 3.0.4_webpack@5.99.8
      webpack: 5.99.8
    transitivePeerDependencies:
      - '@swc/core'
      - encoding
      - esbuild
      - supports-color
      - uglify-js
      - vue
      - webpack-cli
    dev: true

  /@vue/cli-plugin-router/5.0.8_@vue+cli-service@5.0.8:
    resolution: {integrity: sha512-Gmv4dsGdAsWPqVijz3Ux2OS2HkMrWi1ENj2cYL75nUeL+Xj5HEstSqdtfZ0b1q9NCce+BFB6QnHfTBXc/fCvMg==}
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0 || ^5.0.0-0
    dependencies:
      '@vue/cli-service': 5.0.8_upvy4r7hct7s4hgklxc7ih6yii
      '@vue/cli-shared-utils': 5.0.8
    transitivePeerDependencies:
      - encoding
    dev: true

  /@vue/cli-plugin-vuex/5.0.8_@vue+cli-service@5.0.8:
    resolution: {integrity: sha512-HSYWPqrunRE5ZZs8kVwiY6oWcn95qf/OQabwLfprhdpFWAGtLStShjsGED2aDpSSeGAskQETrtR/5h7VqgIlBA==}
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0 || ^5.0.0-0
    dependencies:
      '@vue/cli-service': 5.0.8_upvy4r7hct7s4hgklxc7ih6yii
    dev: true

  /@vue/cli-service/5.0.8_upvy4r7hct7s4hgklxc7ih6yii:
    resolution: {integrity: sha512-nV7tYQLe7YsTtzFrfOMIHc5N2hp5lHG2rpYr0aNja9rNljdgcPZLyQRb2YRivTHqTv7lI962UXFURcpStHgyFw==}
    engines: {node: ^12.0.0 || >= 14.0.0}
    hasBin: true
    peerDependencies:
      cache-loader: '*'
      less-loader: '*'
      pug-plain-loader: '*'
      raw-loader: '*'
      sass-loader: '*'
      stylus-loader: '*'
      vue-template-compiler: ^2.0.0
      webpack-sources: '*'
    peerDependenciesMeta:
      cache-loader:
        optional: true
      less-loader:
        optional: true
      pug-plain-loader:
        optional: true
      raw-loader:
        optional: true
      sass-loader:
        optional: true
      stylus-loader:
        optional: true
      vue-template-compiler:
        optional: true
      webpack-sources:
        optional: true
    dependencies:
      '@babel/helper-compilation-targets': 7.27.2
      '@soda/friendly-errors-webpack-plugin': 1.8.1_webpack@5.99.8
      '@soda/get-current-script': 1.0.2
      '@types/minimist': 1.2.5
      '@vue/cli-overlay': 5.0.8
      '@vue/cli-plugin-router': 5.0.8_@vue+cli-service@5.0.8
      '@vue/cli-plugin-vuex': 5.0.8_@vue+cli-service@5.0.8
      '@vue/cli-shared-utils': 5.0.8
      '@vue/component-compiler-utils': 3.3.0
      '@vue/vue-loader-v15': /vue-loader/15.11.1_ykdgxedkhcf5eunzkwd2dxqjea
      '@vue/web-component-wrapper': 1.3.0
      acorn: 8.14.1
      acorn-walk: 8.3.4
      address: 1.2.2
      autoprefixer: 10.4.21_postcss@8.5.3
      browserslist: 4.24.5
      case-sensitive-paths-webpack-plugin: 2.4.0
      cli-highlight: 2.1.11
      clipboardy: 2.3.0
      cliui: 7.0.4
      copy-webpack-plugin: 9.1.0_webpack@5.99.8
      css-loader: 6.11.0_webpack@5.99.8
      css-minimizer-webpack-plugin: 3.4.1_webpack@5.99.8
      cssnano: 5.1.15_postcss@8.5.3
      debug: 4.4.1
      default-gateway: 6.0.3
      dotenv: 10.0.0
      dotenv-expand: 5.1.0
      fs-extra: 9.1.0
      globby: 11.1.0
      hash-sum: 2.0.0
      html-webpack-plugin: 5.6.3_webpack@5.99.8
      is-file-esm: 1.0.0
      launch-editor-middleware: 2.10.0
      lodash.defaultsdeep: 4.6.1
      lodash.mapvalues: 4.6.0
      mini-css-extract-plugin: 2.9.2_webpack@5.99.8
      minimist: 1.2.8
      module-alias: 2.2.3
      portfinder: 1.0.37
      postcss: 8.5.3
      postcss-loader: 6.2.1_qhv7z3lwctzesdbw7aunk53kba
      progress-webpack-plugin: 1.0.16_webpack@5.99.8
      sass-loader: 13.3.3_sass@1.88.0
      ssri: 8.0.1
      terser-webpack-plugin: 5.3.14_webpack@5.99.8
      thread-loader: 3.0.4_webpack@5.99.8
      vue-loader: 17.4.2_vue@2.6.14+webpack@5.99.8
      vue-style-loader: 4.1.3
      vue-template-compiler: 2.6.14
      webpack: 5.99.8
      webpack-bundle-analyzer: 4.10.2
      webpack-chain: 6.5.1
      webpack-dev-server: 4.15.2_debug@4.4.1+webpack@5.99.8
      webpack-merge: 5.10.0
      webpack-virtual-modules: 0.4.6
      whatwg-fetch: 3.6.20
    transitivePeerDependencies:
      - '@parcel/css'
      - '@rspack/core'
      - '@swc/core'
      - '@vue/compiler-sfc'
      - bufferutil
      - clean-css
      - csso
      - encoding
      - esbuild
      - prettier
      - supports-color
      - uglify-js
      - utf-8-validate
      - vue
      - webpack-cli
    dev: true

  /@vue/cli-shared-utils/5.0.8:
    resolution: {integrity: sha512-uK2YB7bBVuQhjOJF+O52P9yFMXeJVj7ozqJkwYE9PlMHL1LMHjtCYm4cSdOebuPzyP+/9p0BimM/OqxsevIopQ==}
    dependencies:
      '@achrinza/node-ipc': 9.2.9
      chalk: 4.1.2
      execa: 1.0.0
      joi: 17.13.3
      launch-editor: 2.10.0
      lru-cache: 6.0.0
      node-fetch: 2.7.0
      open: 8.4.2
      ora: 5.4.1
      read-pkg: 5.2.0
      semver: 7.7.2
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - encoding
    dev: true

  /@vue/compiler-core/3.5.13:
    resolution: {integrity: sha512-oOdAkwqUfW1WqpwSYJce06wvt6HljgY3fGeM9NcVA1HaYOij3mZG9Rkysn0OHuyUAGMbEbARIpsG+LPVlBJ5/Q==}
    dependencies:
      '@babel/parser': 7.27.2
      '@vue/shared': 3.5.13
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.1
    dev: true

  /@vue/compiler-dom/3.5.13:
    resolution: {integrity: sha512-ZOJ46sMOKUjO3e94wPdCzQ6P1Lx/vhp2RSvfaab88Ajexs0AHeV0uasYhi99WPaogmBlRHNRuly8xV75cNTMDA==}
    dependencies:
      '@vue/compiler-core': 3.5.13
      '@vue/shared': 3.5.13
    dev: true

  /@vue/compiler-sfc/3.5.13:
    resolution: {integrity: sha512-6VdaljMpD82w6c2749Zhf5T9u5uLBWKnVue6XWxprDobftnletJ8+oel7sexFfM3qIxNmVE7LSFGTpv6obNyaQ==}
    dependencies:
      '@babel/parser': 7.27.2
      '@vue/compiler-core': 3.5.13
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-ssr': 3.5.13
      '@vue/shared': 3.5.13
      estree-walker: 2.0.2
      magic-string: 0.30.17
      postcss: 8.5.3
      source-map-js: 1.2.1
    dev: true

  /@vue/compiler-ssr/3.5.13:
    resolution: {integrity: sha512-wMH6vrYHxQl/IybKJagqbquvxpWCuVYpoUJfCqFZwa/JY1GdATAQ+TgVtgrwwMZ0D07QhA99rs/EAAWfvG6KpA==}
    dependencies:
      '@vue/compiler-dom': 3.5.13
      '@vue/shared': 3.5.13
    dev: true

  /@vue/component-compiler-utils/3.3.0:
    resolution: {integrity: sha512-97sfH2mYNU+2PzGrmK2haqffDpVASuib9/w2/noxiFi31Z54hW+q3izKQXXQZSNhtiUpAI36uSuYepeBe4wpHQ==}
    dependencies:
      consolidate: 0.15.1
      hash-sum: 1.0.2
      lru-cache: 4.1.5
      merge-source-map: 1.1.0
      postcss: 7.0.39
      postcss-selector-parser: 6.1.2
      source-map: 0.6.1
      vue-template-es2015-compiler: 1.9.1
    optionalDependencies:
      prettier: 2.8.8

  /@vue/shared/3.5.13:
    resolution: {integrity: sha512-/hnE/qP5ZoGpol0a5mDi45bOd7t3tjYJBjsgCsivow7D48cJeV5l05RD82lPqi7gRiphZM37rnhW1l6ZoCNNnQ==}

  /@vue/web-component-wrapper/1.3.0:
    resolution: {integrity: sha512-Iu8Tbg3f+emIIMmI2ycSI8QcEuAUgPTgHwesDU1eKMLE4YC/c/sFbGc70QgMq31ijRftV0R7vCm9co6rldCeOA==}
    dev: true

  /@webassemblyjs/ast/1.14.1:
    resolution: {integrity: sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ==}
    dependencies:
      '@webassemblyjs/helper-numbers': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
    dev: true

  /@webassemblyjs/ast/1.9.0:
    resolution: {integrity: sha512-C6wW5L+b7ogSDVqymbkkvuW9kruN//YisMED04xzeBBqjHa2FYnmvOlS6Xj68xWQRgWvI9cIglsjFowH/RJyEA==}
    dependencies:
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0
    dev: false

  /@webassemblyjs/floating-point-hex-parser/1.13.2:
    resolution: {integrity: sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA==}
    dev: true

  /@webassemblyjs/floating-point-hex-parser/1.9.0:
    resolution: {integrity: sha512-TG5qcFsS8QB4g4MhrxK5TqfdNe7Ey/7YL/xN+36rRjl/BlGE/NcBvJcqsRgCP6Z92mRE+7N50pRIi8SmKUbcQA==}
    dev: false

  /@webassemblyjs/helper-api-error/1.13.2:
    resolution: {integrity: sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ==}
    dev: true

  /@webassemblyjs/helper-api-error/1.9.0:
    resolution: {integrity: sha512-NcMLjoFMXpsASZFxJ5h2HZRcEhDkvnNFOAKneP5RbKRzaWJN36NC4jqQHKwStIhGXu5mUWlUUk7ygdtrO8lbmw==}
    dev: false

  /@webassemblyjs/helper-buffer/1.14.1:
    resolution: {integrity: sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA==}
    dev: true

  /@webassemblyjs/helper-buffer/1.9.0:
    resolution: {integrity: sha512-qZol43oqhq6yBPx7YM3m9Bv7WMV9Eevj6kMi6InKOuZxhw+q9hOkvq5e/PpKSiLfyetpaBnogSbNCfBwyB00CA==}
    dev: false

  /@webassemblyjs/helper-code-frame/1.9.0:
    resolution: {integrity: sha512-ERCYdJBkD9Vu4vtjUYe8LZruWuNIToYq/ME22igL+2vj2dQ2OOujIZr3MEFvfEaqKoVqpsFKAGsRdBSBjrIvZA==}
    dependencies:
      '@webassemblyjs/wast-printer': 1.9.0
    dev: false

  /@webassemblyjs/helper-fsm/1.9.0:
    resolution: {integrity: sha512-OPRowhGbshCb5PxJ8LocpdX9Kl0uB4XsAjl6jH/dWKlk/mzsANvhwbiULsaiqT5GZGT9qinTICdj6PLuM5gslw==}
    dev: false

  /@webassemblyjs/helper-module-context/1.9.0:
    resolution: {integrity: sha512-MJCW8iGC08tMk2enck1aPW+BE5Cw8/7ph/VGZxwyvGbJwjktKkDK7vy7gAmMDx88D7mhDTCNKAW5tED+gZ0W8g==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
    dev: false

  /@webassemblyjs/helper-numbers/1.13.2:
    resolution: {integrity: sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA==}
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.13.2
      '@webassemblyjs/helper-api-error': 1.13.2
      '@xtuc/long': 4.2.2
    dev: true

  /@webassemblyjs/helper-wasm-bytecode/1.13.2:
    resolution: {integrity: sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA==}
    dev: true

  /@webassemblyjs/helper-wasm-bytecode/1.9.0:
    resolution: {integrity: sha512-R7FStIzyNcd7xKxCZH5lE0Bqy+hGTwS3LJjuv1ZVxd9O7eHCedSdrId/hMOd20I+v8wDXEn+bjfKDLzTepoaUw==}
    dev: false

  /@webassemblyjs/helper-wasm-section/1.14.1:
    resolution: {integrity: sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/wasm-gen': 1.14.1
    dev: true

  /@webassemblyjs/helper-wasm-section/1.9.0:
    resolution: {integrity: sha512-XnMB8l3ek4tvrKUUku+IVaXNHz2YsJyOOmz+MMkZvh8h1uSJpSen6vYnw3IoQ7WwEuAhL8Efjms1ZWjqh2agvw==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
    dev: false

  /@webassemblyjs/ieee754/1.13.2:
    resolution: {integrity: sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw==}
    dependencies:
      '@xtuc/ieee754': 1.2.0
    dev: true

  /@webassemblyjs/ieee754/1.9.0:
    resolution: {integrity: sha512-dcX8JuYU/gvymzIHc9DgxTzUUTLexWwt8uCTWP3otys596io0L5aW02Gb1RjYpx2+0Jus1h4ZFqjla7umFniTg==}
    dependencies:
      '@xtuc/ieee754': 1.2.0
    dev: false

  /@webassemblyjs/leb128/1.13.2:
    resolution: {integrity: sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw==}
    dependencies:
      '@xtuc/long': 4.2.2
    dev: true

  /@webassemblyjs/leb128/1.9.0:
    resolution: {integrity: sha512-ENVzM5VwV1ojs9jam6vPys97B/S65YQtv/aanqnU7D8aSoHFX8GyhGg0CMfyKNIHBuAVjy3tlzd5QMMINa7wpw==}
    dependencies:
      '@xtuc/long': 4.2.2
    dev: false

  /@webassemblyjs/utf8/1.13.2:
    resolution: {integrity: sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ==}
    dev: true

  /@webassemblyjs/utf8/1.9.0:
    resolution: {integrity: sha512-GZbQlWtopBTP0u7cHrEx+73yZKrQoBMpwkGEIqlacljhXCkVM1kMQge/Mf+csMJAjEdSwhOyLAS0AoR3AG5P8w==}
    dev: false

  /@webassemblyjs/wasm-edit/1.14.1:
    resolution: {integrity: sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/helper-wasm-section': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-opt': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      '@webassemblyjs/wast-printer': 1.14.1
    dev: true

  /@webassemblyjs/wasm-edit/1.9.0:
    resolution: {integrity: sha512-FgHzBm80uwz5M8WKnMTn6j/sVbqilPdQXTWraSjBwFXSYGirpkSWE2R9Qvz9tNiTKQvoKILpCuTjBKzOIm0nxw==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/helper-wasm-section': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-opt': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      '@webassemblyjs/wast-printer': 1.9.0
    dev: false

  /@webassemblyjs/wasm-gen/1.14.1:
    resolution: {integrity: sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2
    dev: true

  /@webassemblyjs/wasm-gen/1.9.0:
    resolution: {integrity: sha512-cPE3o44YzOOHvlsb4+E9qSqjc9Qf9Na1OO/BHFy4OI91XDE14MjFN4lTMezzaIWdPqHnsTodGGNP+iRSYfGkjA==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0
    dev: false

  /@webassemblyjs/wasm-opt/1.14.1:
    resolution: {integrity: sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
    dev: true

  /@webassemblyjs/wasm-opt/1.9.0:
    resolution: {integrity: sha512-Qkjgm6Anhm+OMbIL0iokO7meajkzQD71ioelnfPEj6r4eOFuqm4YC3VBPqXjFyyNwowzbMD+hizmprP/Fwkl2A==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
    dev: false

  /@webassemblyjs/wasm-parser/1.14.1:
    resolution: {integrity: sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-api-error': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2
    dev: true

  /@webassemblyjs/wasm-parser/1.9.0:
    resolution: {integrity: sha512-9+wkMowR2AmdSWQzsPEjFU7njh8HTO5MqO8vjwEHuM+AMHioNqSBONRdr0NQQ3dVQrzp0s8lTcYqzUdb7YgELA==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0
    dev: false

  /@webassemblyjs/wast-parser/1.9.0:
    resolution: {integrity: sha512-qsqSAP3QQ3LyZjNC/0jBJ/ToSxfYJ8kYyuiGvtn/8MK89VrNEfwj7BPQzJVHi0jGTRK2dGdJ5PRqhtjzoww+bw==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/floating-point-hex-parser': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-code-frame': 1.9.0
      '@webassemblyjs/helper-fsm': 1.9.0
      '@xtuc/long': 4.2.2
    dev: false

  /@webassemblyjs/wast-printer/1.14.1:
    resolution: {integrity: sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@xtuc/long': 4.2.2
    dev: true

  /@webassemblyjs/wast-printer/1.9.0:
    resolution: {integrity: sha512-2J0nE95rHXHyQ24cWjMKJ1tqB/ds8z/cyeOZxJhcb+rW+SQASVjuznUSmdz5GpVJTzU8JkhYut0D3siFDD6wsA==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0
      '@xtuc/long': 4.2.2
    dev: false

  /@xtuc/ieee754/1.2.0:
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}

  /@xtuc/long/4.2.2:
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}

  /aaptjs/1.3.2:
    resolution: {integrity: sha512-zB1Kyxw7uwUhDYaxWfX41pu6eGzyYzPhfC6Zr3wjEUjaWt0rRQdn3KBmifaB6H60IMcvrvweXZdBEQYWLww4XA==}
    dependencies:
      shelljs: 0.8.5
    dev: false

  /abab/2.0.6:
    resolution: {integrity: sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==}
    deprecated: Use your platform's native atob() and btoa() methods instead
    dev: true

  /accepts/1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  /acorn-globals/4.3.4:
    resolution: {integrity: sha512-clfQEh21R+D0leSbUdWf3OcfqyaCSAQ8Ryq00bofSekfr9W8u1jyYZo6ir0xu9Gtcf7BjcHJpnbZH7JOCpP60A==}
    dependencies:
      acorn: 6.4.2
      acorn-walk: 6.2.0
    dev: true

  /acorn-walk/6.2.0:
    resolution: {integrity: sha512-7evsyfH1cLOCdAzZAd43Cic04yKydNx0cF+7tiA19p1XnLLPU4dpCQOqpjqwokFe//vS0QqfqqjCS2JkiIs0cA==}
    engines: {node: '>=0.4.0'}
    dev: true

  /acorn-walk/8.3.4:
    resolution: {integrity: sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==}
    engines: {node: '>=0.4.0'}
    dependencies:
      acorn: 8.14.1
    dev: true

  /acorn/5.7.4:
    resolution: {integrity: sha512-1D++VG7BhrtvQpNbBzovKNc1FLGGEE/oGe7b9xJm/RFHMBeUaUGpluV9RLjZa47YFdPcDAenEYuq9pQPcMdLJg==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /acorn/6.4.2:
    resolution: {integrity: sha512-XtGIhXwF8YM8bJhGxG5kXgjkEuNGLTkoYqVE+KMR+aspr4KGYmKYg7yUe3KghyQ9yheNwLnjmzh/7+gfDBmHCQ==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  /acorn/7.4.1:
    resolution: {integrity: sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /acorn/8.14.1:
    resolution: {integrity: sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /adb-commander/0.1.9:
    resolution: {integrity: sha512-uFZ+gRLQF4wqkuAQ/bBB22oLHQ8W6HQ31BTCssRtoinMm3oBauAH9e1kUm+cqyc3soGqwUGcsKuKuW37IlMIwA==}
    dependencies:
      adb-driver: 0.1.8
    dev: false

  /adb-devices-emitter/0.1.9:
    resolution: {integrity: sha512-vR38T3ZqZmCXPQS6p0dEjbHZYAybNk6MLW2evtbVGCQvvrvoTzXSFjIdfrRbxryn9HJ7WAXfH42GYgvZIZDiTA==}
    dependencies:
      adb-commander: 0.1.9
    dev: false

  /adb-driver/0.1.8:
    resolution: {integrity: sha512-sERbuWCkIBtoHlEkE3tg67AWUaX/yJskYSLI6DKZsvgr5rL7iVNjMSIB2s7d1zt3luP/BMgLfP5z4Jto+CLafA==}
    dependencies:
      which: 1.3.1
    dev: false

  /address/1.2.2:
    resolution: {integrity: sha512-4B/qKCfeE/ODUaAUpSwfzazo5x29WD4r3vXiWsB7I2mSDAihwEqKO+g8GELZUQSSAo5e1XTYh3ZVfLyxBc12nA==}
    engines: {node: '>= 10.0.0'}
    dev: true

  /after/0.8.2:
    resolution: {integrity: sha512-QbJ0NTQ/I9DI3uSJA4cbexiwQeRAfjPScqIbSjUDd9TOrcg6pTkdgziesOqxBMBzit8vFCTwrP27t13vFOORRA==}
    dev: false

  /ajv-errors/1.0.1_ajv@6.12.6:
    resolution: {integrity: sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ==}
    peerDependencies:
      ajv: '>=5.0.0'
    dependencies:
      ajv: 6.12.6
    dev: false

  /ajv-formats/2.1.1:
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: 8.17.1
    dev: true

  /ajv-keywords/3.5.2_ajv@6.12.6:
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==}
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: 6.12.6

  /ajv-keywords/5.1.0_ajv@8.17.1:
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==}
    peerDependencies:
      ajv: ^8.8.2
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3
    dev: true

  /ajv/6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  /ajv/8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
    dev: true

  /ansi-escapes/3.2.0:
    resolution: {integrity: sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==}
    engines: {node: '>=4'}
    dev: true

  /ansi-escapes/4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3
    dev: true

  /ansi-escapes/5.0.0:
    resolution: {integrity: sha512-5GFMVX8HqE/TB+FuBJGuO5XG0WrsA6ptUqoODaT/n9mmUaZFkqnBueB4leqGBCmrUHnCnC4PCZTCd0E7QQ83bA==}
    engines: {node: '>=12'}
    dependencies:
      type-fest: 1.4.0
    dev: true

  /ansi-html-community/0.0.8:
    resolution: {integrity: sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==}
    engines: {'0': node >= 0.8.0}
    hasBin: true
    dev: true

  /ansi-regex/3.0.1:
    resolution: {integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==}
    engines: {node: '>=4'}
    dev: true

  /ansi-regex/4.1.1:
    resolution: {integrity: sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==}
    engines: {node: '>=6'}
    dev: true

  /ansi-regex/5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}
    dev: true

  /ansi-regex/6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}
    dev: true

  /ansi-styles/3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3

  /ansi-styles/4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1

  /ansi-styles/6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}
    dev: true

  /any-base/1.1.0:
    resolution: {integrity: sha512-uMgjozySS8adZZYePpaWs8cxB9/kdzmpX6SgJZ+wbz1K5eYk5QMYDVJaZKhxyIHUdnnJkfR7SVgStgH7LkGUyg==}
    dev: true

  /any-promise/1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  /anymatch/2.0.0:
    resolution: {integrity: sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==}
    dependencies:
      micromatch: 3.1.10
      normalize-path: 2.1.1

  /anymatch/3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  /aproba/1.2.0:
    resolution: {integrity: sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==}
    dev: false

  /arch/2.2.0:
    resolution: {integrity: sha512-Of/R0wqp83cgHozfIYLbBMnej79U/SVGOOyuB3VVFv1NRM/PSFMK12x9KVtiYzJqmnU5WR2qp0Z5rHb7sWGnFQ==}
    dev: true

  /argparse/1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}
    dependencies:
      sprintf-js: 1.0.3
    dev: true

  /argparse/2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}
    dev: false

  /arr-diff/4.0.0:
    resolution: {integrity: sha512-YVIQ82gZPGBebQV/a8dar4AitzCQs0jjXwMPZllpXMaGjXPYVUawSxQrRsjhjupyVxEvbHgUmIhKVlND+j02kA==}
    engines: {node: '>=0.10.0'}

  /arr-flatten/1.1.0:
    resolution: {integrity: sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==}
    engines: {node: '>=0.10.0'}

  /arr-union/3.1.0:
    resolution: {integrity: sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q==}
    engines: {node: '>=0.10.0'}

  /array-equal/1.0.2:
    resolution: {integrity: sha512-gUHx76KtnhEgB3HOuFYiCm3FIdEs6ocM2asHvNTkfu/Y09qQVrrVVaOKENmS2KkSaGoxgXNqC+ZVtR/n0MOkSA==}
    dev: true

  /array-flatten/1.1.1:
    resolution: {integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==}
    dev: true

  /array-union/2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}
    dev: true

  /array-unique/0.3.2:
    resolution: {integrity: sha512-SleRWjh9JUud2wH1hPs9rZBZ33H6T9HOiL0uwGnGx9FpE6wKGyfWugmbkEOIs6qWrZhg0LWeLziLrEwQJhs5mQ==}
    engines: {node: '>=0.10.0'}

  /arraybuffer.slice/0.0.7:
    resolution: {integrity: sha512-wGUIVQXuehL5TCqQun8OW81jGzAWycqzFF8lFp+GOM5BXLYj3bKNsYC4daB7n6XjCqxQA/qgTJ+8ANR3acjrog==}
    dev: false

  /asn1.js/4.10.1:
    resolution: {integrity: sha512-p32cOF5q0Zqs9uBiONKYLm6BClCoBCM5O9JfeUSlnQLBTxYdTK+pW+nXflm8UkKd2UYlEbYz5qEi0JuZR9ckSw==}
    dependencies:
      bn.js: 4.12.2
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: false

  /asn1/0.2.6:
    resolution: {integrity: sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==}
    dependencies:
      safer-buffer: 2.1.2

  /assert-plus/1.0.0:
    resolution: {integrity: sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==}
    engines: {node: '>=0.8'}

  /assert/1.5.1:
    resolution: {integrity: sha512-zzw1uCAgLbsKwBfFc8CX78DDg+xZeBksSO3vwVIDDN5i94eOrPsSSyiVhmsSABFDM/OcpE2aagCat9dnWQLG1A==}
    dependencies:
      object.assign: 4.1.7
      util: 0.10.4
    dev: false

  /assign-symbols/1.0.0:
    resolution: {integrity: sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw==}
    engines: {node: '>=0.10.0'}

  /ast-types/0.16.1:
    resolution: {integrity: sha512-6t10qk83GOG8p0vKmaCr8eiilZwO171AvbROMtvvNiwrTly62t+7XkA8RdIIVbpMhCASAsxgAzdRSwh6nw/5Dg==}
    engines: {node: '>=4'}
    dependencies:
      tslib: 2.8.1
    dev: true

  /astral-regex/1.0.0:
    resolution: {integrity: sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg==}
    engines: {node: '>=4'}
    dev: true

  /async-each/1.0.6:
    resolution: {integrity: sha512-c646jH1avxr+aVpndVMeAfYw7wAa6idufrlN3LPA4PmKS0QEGp6PIC9nwz0WQkkvBGAMEki3pFdtxaF39J9vvg==}
    dev: false
    optional: true

  /async/3.2.6:
    resolution: {integrity: sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==}

  /asynckit/0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  /at-least-node/1.0.0:
    resolution: {integrity: sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==}
    engines: {node: '>= 4.0.0'}
    dev: true

  /atob/2.1.2:
    resolution: {integrity: sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==}
    engines: {node: '>= 4.5.0'}
    hasBin: true

  /autoprefixer/10.4.21_postcss@8.5.3:
    resolution: {integrity: sha512-O+A6LWV5LDHSJD3LjHYoNi4VLsj/Whi7k6zG12xTYaU4cQ8oxQGckXNX8cRHK5yOZ/ppVHe0ZBXGzSV9jXdVbQ==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      browserslist: 4.24.5
      caniuse-lite: 1.0.30001718
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /autoprefixer/8.6.5:
    resolution: {integrity: sha512-PLWJN3Xo/rycNkx+mp8iBDMTm3FeWe4VmYaZDSqL5QQB9sLsQkG5k8n+LNDFnhh9kdq2K+egL/icpctOmDHwig==}
    hasBin: true
    dependencies:
      browserslist: 3.2.8
      caniuse-lite: 1.0.30001718
      normalize-range: 0.1.2
      num2fraction: 1.2.2
      postcss: 6.0.23
      postcss-value-parser: 3.3.1
    dev: false

  /aws-sign2/0.7.0:
    resolution: {integrity: sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==}

  /aws4/1.13.2:
    resolution: {integrity: sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw==}

  /babel-jest/25.5.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-9dA9+GmMjIzgPnYtkhBg73gOo/RHqPmLruP3BaGL4KEX3Dwz6pI8auSN8G8+iuEG90+GSswyKvslN+JYSaacaQ==}
    engines: {node: '>= 8.3'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@jest/transform': 25.5.1
      '@jest/types': 25.5.0
      '@types/babel__core': 7.20.5
      babel-plugin-istanbul: 6.1.1
      babel-preset-jest: 25.5.0_@babel+core@7.27.1
      chalk: 3.0.0
      graceful-fs: 4.2.11
      slash: 3.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-loader/8.4.1_3rhk6tbchh5vbic6six75ixo2u:
    resolution: {integrity: sha512-nXzRChX+Z1GoE6yWavBQg6jDslyFF3SDjl2paADuoQtQW10JqShJt62R6eJQ5m/pjJFDT8xgKIWSP85OY8eXeA==}
    engines: {node: '>= 8.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'
    dependencies:
      '@babel/core': 7.27.1
      find-cache-dir: 3.3.2
      loader-utils: 2.0.4
      make-dir: 3.1.0
      schema-utils: 2.7.1
      webpack: 4.47.0
    dev: false

  /babel-loader/8.4.1_bm7m7jjqcfz7ytm4b3ejdei32u:
    resolution: {integrity: sha512-nXzRChX+Z1GoE6yWavBQg6jDslyFF3SDjl2paADuoQtQW10JqShJt62R6eJQ5m/pjJFDT8xgKIWSP85OY8eXeA==}
    engines: {node: '>= 8.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'
    dependencies:
      '@babel/core': 7.27.1
      find-cache-dir: 3.3.2
      loader-utils: 2.0.4
      make-dir: 3.1.0
      schema-utils: 2.7.1
      webpack: 5.99.8
    dev: true

  /babel-plugin-dynamic-import-node/2.3.3:
    resolution: {integrity: sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ==}
    dependencies:
      object.assign: 4.1.7
    dev: true

  /babel-plugin-import/1.13.8:
    resolution: {integrity: sha512-36babpjra5m3gca44V6tSTomeBlPA7cHUynrE2WiQIm3rEGD9xy28MKsx5IdO45EbnpJY7Jrgd00C6Dwt/l/2Q==}
    dependencies:
      '@babel/helper-module-imports': 7.27.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-istanbul/6.1.1:
    resolution: {integrity: sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/helper-plugin-utils': 7.27.1
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-instrument: 5.2.1
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-jest-hoist/25.5.0:
    resolution: {integrity: sha512-u+/W+WAjMlvoocYGTwthAiQSxDcJAyHpQ6oWlHdFZaaN+Rlk8Q7iiwDPg2lN/FyJtAYnKjFxbn7xus4HCFkg5g==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.1
      '@types/babel__traverse': 7.20.7
    dev: true

  /babel-plugin-polyfill-corejs2/0.4.13_@babel+core@7.27.1:
    resolution: {integrity: sha512-3sX/eOms8kd3q2KZ6DAhKPc0dgm525Gqq5NtWKZ7QYYZEv57OQ54KtblzJzH1lQF/eQxO8KjWGIK9IPUJNus5g==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/compat-data': 7.27.2
      '@babel/core': 7.27.1
      '@babel/helper-define-polyfill-provider': 0.6.4_@babel+core@7.27.1
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  /babel-plugin-polyfill-corejs3/0.11.1_@babel+core@7.27.1:
    resolution: {integrity: sha512-yGCqvBT4rwMczo28xkH/noxJ6MZ4nJfkVYdoDaC/utLtWrXxv27HVrzAeSbqR8SxDsp46n0YF47EbHoixy6rXQ==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-define-polyfill-provider': 0.6.4_@babel+core@7.27.1
      core-js-compat: 3.42.0
    transitivePeerDependencies:
      - supports-color

  /babel-plugin-polyfill-regenerator/0.6.4_@babel+core@7.27.1:
    resolution: {integrity: sha512-7gD3pRadPrbjhjLyxebmx/WrFYcuSjZ0XbdUujQMZ/fcE9oeewk2U/7PCvez84UeuK3oSjmPZ0Ch0dlupQvGzw==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-define-polyfill-provider': 0.6.4_@babel+core@7.27.1
    transitivePeerDependencies:
      - supports-color

  /babel-preset-current-node-syntax/0.1.4_@babel+core@7.27.1:
    resolution: {integrity: sha512-5/INNCYhUGqw7VbVjT/hb3ucjgkVHKXY7lX3ZjlN4gm565VyFmJUrJ/h+h16ECVB38R/9SF6aACydpKMLZ/c9w==}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      '@babel/plugin-syntax-async-generators': 7.8.4_@babel+core@7.27.1
      '@babel/plugin-syntax-bigint': 7.8.3_@babel+core@7.27.1
      '@babel/plugin-syntax-class-properties': 7.12.13_@babel+core@7.27.1
      '@babel/plugin-syntax-import-meta': 7.10.4_@babel+core@7.27.1
      '@babel/plugin-syntax-json-strings': 7.8.3_@babel+core@7.27.1
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4_@babel+core@7.27.1
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.27.1
      '@babel/plugin-syntax-numeric-separator': 7.10.4_@babel+core@7.27.1
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.27.1
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3_@babel+core@7.27.1
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.27.1
    dev: true

  /babel-preset-jest/25.5.0_@babel+core@7.27.1:
    resolution: {integrity: sha512-8ZczygctQkBU+63DtSOKGh7tFL0CeCuz+1ieud9lJ1WPQ9O6A1a/r+LGn6Y705PA6whHQ3T1XuB/PmpfNYf8Fw==}
    engines: {node: '>= 8.3'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.1
      babel-plugin-jest-hoist: 25.5.0
      babel-preset-current-node-syntax: 0.1.4_@babel+core@7.27.1
    dev: true

  /backo2/1.0.2:
    resolution: {integrity: sha512-zj6Z6M7Eq+PBZ7PQxl5NT665MvJdAkzp0f60nAJ+sLaSCBPMwVak5ZegFbgVCzFcCJTKFoMizvM5Ld7+JrRJHA==}
    dev: false

  /balanced-match/1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  /base/0.11.2:
    resolution: {integrity: sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      cache-base: 1.0.1
      class-utils: 0.3.6
      component-emitter: 1.3.1
      define-property: 1.0.0
      isobject: 3.0.1
      mixin-deep: 1.3.2
      pascalcase: 0.1.1

  /base64-arraybuffer/0.1.4:
    resolution: {integrity: sha512-a1eIFi4R9ySrbiMuyTGx5e92uRH5tQY6kArNcFaKBUleIoLjdjBg7Zxm3Mqm3Kmkf27HLR/1fnxX9q8GQ7Iavg==}
    engines: {node: '>= 0.6.0'}
    dev: false

  /base64-arraybuffer/0.2.0:
    resolution: {integrity: sha512-7emyCsu1/xiBXgQZrscw/8KPRT44I4Yq9Pe6EGs3aPRTsWuggML1/1DTuZUuIaJPIm1FTDUVXl4x/yW8s0kQDQ==}
    engines: {node: '>= 0.6.0'}
    dev: false

  /base64-js/1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  /base64id/2.0.0:
    resolution: {integrity: sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog==}
    engines: {node: ^4.5.0 || >= 5.9}
    dev: false

  /batch/0.6.1:
    resolution: {integrity: sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw==}
    dev: true

  /bcrypt-pbkdf/1.0.2:
    resolution: {integrity: sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==}
    dependencies:
      tweetnacl: 0.14.5

  /big.js/5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}

  /binary-extensions/1.13.1:
    resolution: {integrity: sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw==}
    engines: {node: '>=0.10.0'}
    dev: false
    optional: true

  /binary-extensions/2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  /bindings/1.5.0:
    resolution: {integrity: sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==}
    requiresBuild: true
    dependencies:
      file-uri-to-path: 1.0.0
    dev: false
    optional: true

  /bl/4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==}
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2
    dev: true

  /blob/0.0.5:
    resolution: {integrity: sha512-gaqbzQPqOoamawKg0LGVd7SzLgXS+JH61oWprSLH+P+abTczqJbhTR8CmJ2u9/bUYNmHTGJx/UEmn6doAvvuig==}
    dev: false

  /bluebird/3.7.2:
    resolution: {integrity: sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==}

  /bmp-js/0.1.0:
    resolution: {integrity: sha512-vHdS19CnY3hwiNdkaqk93DvjVLfbEcI8mys4UjuWrlX1haDmroo8o4xCzh4wD6DGV6HxRCyauwhHRqMTfERtjw==}
    dev: true

  /bn.js/4.12.2:
    resolution: {integrity: sha512-n4DSx829VRTRByMRGdjQ9iqsN0Bh4OolPsFnaZBLcbi8iXcB+kJ9s7EnRt4wILZNV3kPLHkRVfOc/HvhC3ovDw==}
    dev: false

  /bn.js/5.2.2:
    resolution: {integrity: sha512-v2YAxEmKaBLahNwE1mjp4WON6huMNeuDvagFZW+ASCuA/ku0bXR9hSMw0XpiqMoA3+rmnyck/tPRSFQkoC9Cuw==}
    dev: false

  /body-parser/1.20.3:
    resolution: {integrity: sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.13.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    dev: true

  /bonjour-service/1.3.0:
    resolution: {integrity: sha512-3YuAUiSkWykd+2Azjgyxei8OWf8thdn8AITIog2M4UICzoqfjlqr64WIjEXZllf/W6vK1goqleSR6brGomxQqA==}
    dependencies:
      fast-deep-equal: 3.1.3
      multicast-dns: 7.2.5
    dev: true

  /boolbase/1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}
    dev: true

  /brace-expansion/1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  /braces/2.3.2:
    resolution: {integrity: sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-flatten: 1.1.0
      array-unique: 0.3.2
      extend-shallow: 2.0.1
      fill-range: 4.0.0
      isobject: 3.0.1
      repeat-element: 1.1.4
      snapdragon: 0.8.2
      snapdragon-node: 2.1.1
      split-string: 3.1.0
      to-regex: 3.0.2

  /braces/3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.1.1

  /brorand/1.1.0:
    resolution: {integrity: sha512-cKV8tMCEpQs4hK/ik71d6LrPOnpkpGBR0wzxqr68g2m/LB2GxVYQroAjMJZRVM1Y4BCjCKc3vAamxSzOY2RP+w==}
    dev: false

  /browser-process-hrtime/1.0.0:
    resolution: {integrity: sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow==}
    dev: true

  /browser-resolve/1.11.3:
    resolution: {integrity: sha512-exDi1BYWB/6raKHmDTCicQfTkqwN5fioMFV4j8BsfMU4R2DK/QfZfK7kOVkmWCNANf0snkBzqGqAJBao9gZMdQ==}
    dependencies:
      resolve: 1.1.7
    dev: true

  /browserify-aes/1.2.0:
    resolution: {integrity: sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==}
    dependencies:
      buffer-xor: 1.0.3
      cipher-base: 1.0.6
      create-hash: 1.2.0
      evp_bytestokey: 1.0.3
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /browserify-cipher/1.0.1:
    resolution: {integrity: sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==}
    dependencies:
      browserify-aes: 1.2.0
      browserify-des: 1.0.2
      evp_bytestokey: 1.0.3
    dev: false

  /browserify-des/1.0.2:
    resolution: {integrity: sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A==}
    dependencies:
      cipher-base: 1.0.6
      des.js: 1.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /browserify-rsa/4.1.1:
    resolution: {integrity: sha512-YBjSAiTqM04ZVei6sXighu679a3SqWORA3qZTEqZImnlkDIFtKc6pNutpjyZ8RJTjQtuYfeetkxM11GwoYXMIQ==}
    engines: {node: '>= 0.10'}
    dependencies:
      bn.js: 5.2.2
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: false

  /browserify-sign/4.2.3:
    resolution: {integrity: sha512-JWCZW6SKhfhjJxO8Tyiiy+XYB7cqd2S5/+WeYHsKdNKFlCBhKbblba1A/HN/90YwtxKc8tCErjffZl++UNmGiw==}
    engines: {node: '>= 0.12'}
    dependencies:
      bn.js: 5.2.2
      browserify-rsa: 4.1.1
      create-hash: 1.2.0
      create-hmac: 1.1.7
      elliptic: 6.6.1
      hash-base: 3.0.5
      inherits: 2.0.4
      parse-asn1: 5.1.7
      readable-stream: 2.3.8
      safe-buffer: 5.2.1
    dev: false

  /browserify-zlib/0.2.0:
    resolution: {integrity: sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==}
    dependencies:
      pako: 1.0.11
    dev: false

  /browserslist/3.2.8:
    resolution: {integrity: sha512-WHVocJYavUwVgVViC0ORikPHQquXwVh939TaelZ4WDqpWgTX/FsGhl/+P4qBUAGcRvtOgDgC+xftNWWp2RUTAQ==}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001718
      electron-to-chromium: 1.5.152
    dev: false

  /browserslist/4.24.5:
    resolution: {integrity: sha512-FDToo4Wo82hIdgc1CQ+NQD0hEhmpPjrZ3hiUgwgOG6IuTdlpr8jdjyG24P6cNP1yJpTLzS5OcGgSw0xmDU1/Tw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001718
      electron-to-chromium: 1.5.152
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3_browserslist@4.24.5

  /bser/2.1.1:
    resolution: {integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==}
    dependencies:
      node-int64: 0.4.0
    dev: true

  /buffer-equal/0.0.1:
    resolution: {integrity: sha512-RgSV6InVQ9ODPdLWJ5UAqBqJBOg370Nz6ZQtRzpt6nUjc8v0St97uJ4PYC6NztqIScrAXafKM3mZPMygSe1ggA==}
    engines: {node: '>=0.4.0'}
    dev: true

  /buffer-from/1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  /buffer-json/2.0.0:
    resolution: {integrity: sha512-+jjPFVqyfF1esi9fvfUs3NqM0pH1ziZ36VP4hmA/y/Ssfo/5w5xHKfTw9BwQjoJ1w/oVtpLomqwUHKdefGyuHw==}
    dev: true

  /buffer-xor/1.0.3:
    resolution: {integrity: sha512-571s0T7nZWK6vB67HI5dyUF7wXiNcfaPPPTl6zYCNApANjIvYJTg7hlud/+cJpdAhS7dVzqMLmfhfHR3rAcOjQ==}
    dev: false

  /buffer/4.9.2:
    resolution: {integrity: sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
      isarray: 1.0.0
    dev: false

  /buffer/5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
    dev: true

  /builtin-status-codes/3.0.0:
    resolution: {integrity: sha512-HpGFw18DgFWlncDfjTa2rcQ4W88O1mC8e8yZ2AvQY5KDaktSTwo+KRf6nHK6FRI5FyRyb/5T6+TSxfP7QyGsmQ==}
    dev: false

  /bytes/3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  /cacache/12.0.4:
    resolution: {integrity: sha512-a0tMB40oefvuInr4Cwb3GerbL9xTj1D5yg0T5xrjGCGyfvbxseIXX7BAO/u/hIXdafzOI5JC3wDwHyf24buOAQ==}
    dependencies:
      bluebird: 3.7.2
      chownr: 1.1.4
      figgy-pudding: 3.5.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      infer-owner: 1.0.4
      lru-cache: 5.1.1
      mississippi: 3.0.0
      mkdirp: 0.5.6
      move-concurrently: 1.0.1
      promise-inflight: 1.0.1
      rimraf: 2.7.1
      ssri: 6.0.2
      unique-filename: 1.1.1
      y18n: 4.0.3
    dev: false

  /cache-base/1.0.1:
    resolution: {integrity: sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      collection-visit: 1.0.0
      component-emitter: 1.3.1
      get-value: 2.0.6
      has-value: 1.0.0
      isobject: 3.0.1
      set-value: 2.0.1
      to-object-path: 0.3.0
      union-value: 1.0.1
      unset-value: 1.0.0

  /cache-content-type/1.0.1:
    resolution: {integrity: sha512-IKufZ1o4Ut42YUrZSo8+qnMTrFuKkvyoLXUywKz9GJ5BrhOFGhLdkx9sG4KAnVvbY6kEcSFjLQul+DVmBm2bgA==}
    engines: {node: '>= 6.0.0'}
    dependencies:
      mime-types: 2.1.35
      ylru: 1.4.0
    dev: false

  /call-bind-apply-helpers/1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  /call-bind/1.0.8:
    resolution: {integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  /call-bound/1.0.4:
    resolution: {integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  /callsites/3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  /camel-case/4.1.2:
    resolution: {integrity: sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==}
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.8.1
    dev: true

  /camelcase/5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  /caniuse-api/3.0.0:
    resolution: {integrity: sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==}
    dependencies:
      browserslist: 4.24.5
      caniuse-lite: 1.0.30001718
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0
    dev: true

  /caniuse-lite/1.0.30001718:
    resolution: {integrity: sha512-AflseV1ahcSunK53NfEs9gFWgOEmzr0f+kaMFA4xiLZlr9Hzt7HxcSpIFcnNCUkz6R6dWKa54rUz3HUmI3nVcw==}

  /capture-exit/2.0.0:
    resolution: {integrity: sha512-PiT/hQmTonHhl/HFGN+Lx3JJUznrVYJ3+AQsnthneZbvW7x+f08Tk7yLJTLEOUvBTbduLeeBkxEaYXUOUrRq6g==}
    engines: {node: 6.* || 8.* || >= 10.*}
    dependencies:
      rsvp: 4.8.5
    dev: true

  /case-sensitive-paths-webpack-plugin/2.4.0:
    resolution: {integrity: sha512-roIFONhcxog0JSSWbvVAh3OocukmSgpqOH6YpMkCvav/ySIV3JKg4Dc8vYtQjYi/UxpNE36r/9v+VqTQqgkYmw==}
    engines: {node: '>=4'}
    dev: true

  /caseless/0.12.0:
    resolution: {integrity: sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==}

  /centra/2.7.0_debug@4.4.1:
    resolution: {integrity: sha512-PbFMgMSrmgx6uxCdm57RUos9Tc3fclMvhLSATYN39XsDV29B89zZ3KA89jmY0vwSGazyU+uerqwa6t+KaodPcg==}
    dependencies:
      follow-redirects: 1.15.9_debug@4.4.1
    transitivePeerDependencies:
      - debug
    dev: true

  /chalk/2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  /chalk/3.0.0:
    resolution: {integrity: sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /chalk/4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  /chalk/5.3.0:
    resolution: {integrity: sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}
    dev: true

  /charenc/0.0.2:
    resolution: {integrity: sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==}
    dev: false

  /chokidar/2.1.8:
    resolution: {integrity: sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==}
    dependencies:
      anymatch: 2.0.0
      async-each: 1.0.6
      braces: 2.3.2
      glob-parent: 3.1.0
      inherits: 2.0.4
      is-binary-path: 1.0.1
      is-glob: 4.0.3
      normalize-path: 3.0.0
      path-is-absolute: 1.0.1
      readdirp: 2.2.1
      upath: 1.2.0
    optionalDependencies:
      fsevents: 1.2.13
    dev: false
    optional: true

  /chokidar/3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  /chokidar/4.0.3:
    resolution: {integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==}
    engines: {node: '>= 14.16.0'}
    dependencies:
      readdirp: 4.1.2
    dev: true

  /chownr/1.1.4:
    resolution: {integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==}
    dev: false

  /chrome-simple-launcher/0.1.3:
    resolution: {integrity: sha512-8xUtp6cS4tn1o1Pzev6pxbgF7vCRdfxFLW4xOSUIF/83+ya8GWRqMpkqd8h7VDliHLhyRIi5zKbi85wpZ4m2oA==}
    dev: false

  /chrome-trace-event/1.0.4:
    resolution: {integrity: sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==}
    engines: {node: '>=6.0'}

  /ci-info/2.0.0:
    resolution: {integrity: sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==}
    dev: true

  /cipher-base/1.0.6:
    resolution: {integrity: sha512-3Ek9H3X6pj5TgenXYtNWdaBon1tgYCaebd+XPg0keyjEbEfkD4KkmAxkQ/i1vYvxdcT5nscLBfq9VJRmCBcFSw==}
    engines: {node: '>= 0.10'}
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /class-utils/0.3.6:
    resolution: {integrity: sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      define-property: 0.2.5
      isobject: 3.0.1
      static-extend: 0.1.2

  /clean-css/5.3.3:
    resolution: {integrity: sha512-D5J+kHaVb/wKSFcyyV75uCn8fiY4sV38XJoe4CUyGQ+mOU/fMVYUdH1hJC+CJQ5uY3EnW27SbJYS4X8BiLrAFg==}
    engines: {node: '>= 10.0'}
    dependencies:
      source-map: 0.6.1
    dev: true

  /cli-cursor/2.1.0:
    resolution: {integrity: sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw==}
    engines: {node: '>=4'}
    dependencies:
      restore-cursor: 2.0.0
    dev: true

  /cli-cursor/3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: 3.1.0
    dev: true

  /cli-cursor/4.0.0:
    resolution: {integrity: sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      restore-cursor: 4.0.0
    dev: true

  /cli-highlight/2.1.11:
    resolution: {integrity: sha512-9KDcoEVwyUXrjcJNvHD0NFc/hiwe/WPVYIleQh2O1N2Zro5gWJZ/K+3DGn8w8P/F6FxOgzyC5bxDyHIgCSPhGg==}
    engines: {node: '>=8.0.0', npm: '>=5.0.0'}
    hasBin: true
    dependencies:
      chalk: 4.1.2
      highlight.js: 10.7.3
      mz: 2.7.0
      parse5: 5.1.1
      parse5-htmlparser2-tree-adapter: 6.0.1
      yargs: 16.2.0
    dev: true

  /cli-spinners/2.9.2:
    resolution: {integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==}
    engines: {node: '>=6'}
    dev: true

  /cli-truncate/3.1.0:
    resolution: {integrity: sha512-wfOBkjXteqSnI59oPcJkcPl/ZmwvMMOj340qUIY1SKZCv0B9Cf4D4fAucRkIKQmsIuYK3x1rrgU7MeGRruiuiA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      slice-ansi: 5.0.0
      string-width: 5.1.2
    dev: true

  /clipboardy/2.3.0:
    resolution: {integrity: sha512-mKhiIL2DrQIsuXMgBgnfEHOZOryC7kY7YO//TN6c63wlEm3NG5tz+YgY5rVi29KCmq/QQjKYvM7a19+MDOTHOQ==}
    engines: {node: '>=8'}
    dependencies:
      arch: 2.2.0
      execa: 1.0.0
      is-wsl: 2.2.0
    dev: true

  /cliui/6.0.0:
    resolution: {integrity: sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0
    dev: true

  /cliui/7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: true

  /clone-deep/4.0.1:
    resolution: {integrity: sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==}
    engines: {node: '>=6'}
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1

  /clone/1.0.4:
    resolution: {integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==}
    engines: {node: '>=0.8'}
    dev: true

  /co-body/5.2.0:
    resolution: {integrity: sha512-sX/LQ7LqUhgyaxzbe7IqwPeTr2yfpfUIQ/dgpKo6ZI4y4lpQA0YxAomWIY+7I7rHWcG02PG+OuPREzMW/5tszQ==}
    dependencies:
      inflation: 2.1.0
      qs: 6.14.0
      raw-body: 2.5.2
      type-is: 1.6.18
    dev: false

  /co-body/6.2.0:
    resolution: {integrity: sha512-Kbpv2Yd1NdL1V/V4cwLVxraHDV6K8ayohr2rmH0J87Er8+zJjcTa6dAn9QMPC9CRgU8+aNajKbSf1TzDB1yKPA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      '@hapi/bourne': 3.0.0
      inflation: 2.1.0
      qs: 6.14.0
      raw-body: 2.5.2
      type-is: 1.6.18
    dev: false

  /co/4.6.0:
    resolution: {integrity: sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}

  /collect-v8-coverage/1.0.2:
    resolution: {integrity: sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==}
    dev: true

  /collection-visit/1.0.0:
    resolution: {integrity: sha512-lNkKvzEeMBBjUGHZ+q6z9pSJla0KWAQPvtzhEV9+iGyQYG+pBpl7xKDhxoNSOZH2hhv0v5k0y2yAM4o4SjoSkw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-visit: 1.0.0
      object-visit: 1.0.1

  /color-convert/1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3

  /color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4

  /color-name/1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  /color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  /colord/2.9.3:
    resolution: {integrity: sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==}
    dev: true

  /colorette/2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}
    dev: true

  /combined-stream/1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0

  /commander/11.0.0:
    resolution: {integrity: sha512-9HMlXtt/BNoYr8ooyjjNRdIilOTkVJXB+GhxMTtOKwk0R4j4lS4NpjuqmRxroBfnfTSHQIHQB7wryHhXarNjmQ==}
    engines: {node: '>=16'}
    dev: true

  /commander/2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  /commander/4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}
    dev: true

  /commander/7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==}
    engines: {node: '>= 10'}
    dev: true

  /commander/8.3.0:
    resolution: {integrity: sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==}
    engines: {node: '>= 12'}
    dev: true

  /commondir/1.0.1:
    resolution: {integrity: sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==}

  /component-bind/1.0.0:
    resolution: {integrity: sha512-WZveuKPeKAG9qY+FkYDeADzdHyTYdIboXS59ixDeRJL5ZhxpqUnxSOwop4FQjMsiYm3/Or8cegVbpAHNA7pHxw==}
    dev: false

  /component-emitter/1.2.1:
    resolution: {integrity: sha512-jPatnhd33viNplKjqXKRkGU345p263OIWzDL2wH3LGIGp5Kojo+uXizHmOADRvhGFFTnJqX3jBAKP6vvmSDKcA==}
    dev: false

  /component-emitter/1.3.1:
    resolution: {integrity: sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ==}

  /component-inherit/0.0.3:
    resolution: {integrity: sha512-w+LhYREhatpVqTESyGFg3NlP6Iu0kEKUHETY9GoZP/pQyW4mHFZuFWRUCIqVPZ36ueVLtoOEZaAqbCF2RDndaA==}
    dev: false

  /compressible/2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.54.0
    dev: true

  /compression/1.8.0:
    resolution: {integrity: sha512-k6WLKfunuqCYD3t6AsuPGvQWaKwuLLh2/xHNcX4qE+vIfDNXpSqnrhwA7O53R7WVQUnt8dVAIW+YHr7xTgOgGA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      bytes: 3.1.2
      compressible: 2.0.18
      debug: 2.6.9
      negotiator: 0.6.4
      on-headers: 1.0.2
      safe-buffer: 5.2.1
      vary: 1.1.2
    dev: true

  /concat-map/0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  /concat-stream/1.6.2:
    resolution: {integrity: sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==}
    engines: {'0': node >= 0.8}
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 2.3.8
      typedarray: 0.0.6
    dev: false

  /connect-history-api-fallback/2.0.0:
    resolution: {integrity: sha512-U73+6lQFmfiNPrYbXqr6kZ1i1wiRqXnp2nhMsINseWXO8lDau0LGEffJ8kQi4EjLZympVgRdvqjAgiZ1tgzDDA==}
    engines: {node: '>=0.8'}
    dev: true

  /console-browserify/1.2.0:
    resolution: {integrity: sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA==}
    dev: false

  /consolidate/0.15.1:
    resolution: {integrity: sha512-DW46nrsMJgy9kqAbPt5rKaCr7uFtpo4mSUvLHIUbJEjm0vo+aY5QLwBUq3FK4tRnJr/X0Psc0C4jf/h+HtXSMw==}
    engines: {node: '>= 0.10.0'}
    deprecated: Please upgrade to consolidate v1.0.0+ as it has been modernized with several long-awaited fixes implemented. Maintenance is supported by Forward Email at https://forwardemail.net ; follow/watch https://github.com/ladjs/consolidate for updates and release changelog
    dependencies:
      bluebird: 3.7.2

  /constants-browserify/1.0.0:
    resolution: {integrity: sha512-xFxOwqIzR/e1k1gLiWEophSCMqXcwVHIH7akf7b/vxcUeGunlj3hvZaaqxwHsTgn+IndtkQJgSztIDWeumWJDQ==}
    dev: false

  /content-disposition/0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}
    dependencies:
      safe-buffer: 5.2.1

  /content-type/1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}

  /convert-source-map/1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}
    dev: true

  /convert-source-map/2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  /cookie-signature/1.0.6:
    resolution: {integrity: sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==}
    dev: true

  /cookie/0.4.2:
    resolution: {integrity: sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==}
    engines: {node: '>= 0.6'}
    dev: false

  /cookie/0.7.1:
    resolution: {integrity: sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==}
    engines: {node: '>= 0.6'}
    dev: true

  /cookies/0.9.1:
    resolution: {integrity: sha512-TG2hpqe4ELx54QER/S3HQ9SRVnQnGBtKUz5bLQWtYAQ+o6GpgMs6sYUvaiJjVxb+UXwhRhAEP3m7LbsIZ77Hmw==}
    engines: {node: '>= 0.8'}
    dependencies:
      depd: 2.0.0
      keygrip: 1.1.0
    dev: false

  /copy-concurrently/1.0.5:
    resolution: {integrity: sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A==}
    deprecated: This package is no longer supported.
    dependencies:
      aproba: 1.2.0
      fs-write-stream-atomic: 1.0.10
      iferr: 0.1.5
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: false

  /copy-descriptor/0.1.1:
    resolution: {integrity: sha512-XgZ0pFcakEUlbwQEVNg3+QAis1FyTL3Qel9FYy8pSkQqoG3PNoT0bOCQtOXcOkur21r2Eq2kI+IE+gsmAEVlYw==}
    engines: {node: '>=0.10.0'}

  /copy-to/2.0.1:
    resolution: {integrity: sha512-3DdaFaU/Zf1AnpLiFDeNCD4TOWe3Zl2RZaTzUvWiIk5ERzcCodOE20Vqq4fzCbNoHURFHT4/us/Lfq+S2zyY4w==}
    dev: false

  /copy-webpack-plugin/9.1.0_webpack@5.99.8:
    resolution: {integrity: sha512-rxnR7PaGigJzhqETHGmAcxKnLZSR5u1Y3/bcIv/1FnqXedcL/E2ewK7ZCNrArJKCiSv8yVXhTqetJh8inDvfsA==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.1.0
    dependencies:
      fast-glob: 3.3.3
      glob-parent: 6.0.2
      globby: 11.1.0
      normalize-path: 3.0.0
      schema-utils: 3.3.0
      serialize-javascript: 6.0.2
      webpack: 5.99.8
    dev: true

  /core-js-compat/3.42.0:
    resolution: {integrity: sha512-bQasjMfyDGyaeWKBIu33lHh9qlSR0MFE/Nmc6nMjf/iU9b3rSMdAYz1Baxrv4lPdGUsTqZudHA4jIGSJy0SWZQ==}
    dependencies:
      browserslist: 4.24.5

  /core-js/3.42.0:
    resolution: {integrity: sha512-Sz4PP4ZA+Rq4II21qkNqOEDTDrCvcANId3xpIgB34NDkWc3UduWj2dqEtN9yZIq8Dk3HyPI33x9sqqU5C8sr0g==}
    requiresBuild: true

  /core-util-is/1.0.2:
    resolution: {integrity: sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==}

  /core-util-is/1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  /cosmiconfig/7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==}
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.1
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    dev: true

  /cosmiconfig/9.0.0:
    resolution: {integrity: sha512-itvL5h8RETACmOTFc4UfIyB2RfEHi71Ax6E/PivVxq9NseKbOWpeyHEOIbmAw1rs8Ak0VursQNww7lf7YtUwzg==}
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      env-paths: 2.2.1
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      parse-json: 5.2.0
    dev: false

  /create-ecdh/4.0.4:
    resolution: {integrity: sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A==}
    dependencies:
      bn.js: 4.12.2
      elliptic: 6.6.1
    dev: false

  /create-hash/1.2.0:
    resolution: {integrity: sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==}
    dependencies:
      cipher-base: 1.0.6
      inherits: 2.0.4
      md5.js: 1.3.5
      ripemd160: 2.0.2
      sha.js: 2.4.11
    dev: false

  /create-hmac/1.1.7:
    resolution: {integrity: sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==}
    dependencies:
      cipher-base: 1.0.6
      create-hash: 1.2.0
      inherits: 2.0.4
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: false

  /cross-env/5.2.1:
    resolution: {integrity: sha512-1yHhtcfAd1r4nwQgknowuUNfIT9E8dOMMspC36g45dN+iD1blloi7xp8X/xAIDnjHWyt1uQ8PHk2fkNaym7soQ==}
    engines: {node: '>=4.0'}
    hasBin: true
    dependencies:
      cross-spawn: 6.0.6
    dev: true

  /cross-env/7.0.3:
    resolution: {integrity: sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==}
    engines: {node: '>=10.14', npm: '>=6', yarn: '>=1'}
    hasBin: true
    dependencies:
      cross-spawn: 7.0.6
    dev: true

  /cross-spawn/6.0.6:
    resolution: {integrity: sha512-VqCUuhcd1iB+dsv8gxPttb5iZh/D0iubSP21g36KXdEuf6I5JiioesUVjpCdHV9MZRUfVFlvwtIUyPfxo5trtw==}
    engines: {node: '>=4.8'}
    dependencies:
      nice-try: 1.0.5
      path-key: 2.0.1
      semver: 5.7.2
      shebang-command: 1.2.0
      which: 1.3.1
    dev: true

  /cross-spawn/7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /crypt/0.0.2:
    resolution: {integrity: sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==}
    dev: false

  /crypto-browserify/3.12.1:
    resolution: {integrity: sha512-r4ESw/IlusD17lgQi1O20Fa3qNnsckR126TdUuBgAu7GBYSIPvdNyONd3Zrxh0xCwA4+6w/TDArBPsMvhur+KQ==}
    engines: {node: '>= 0.10'}
    dependencies:
      browserify-cipher: 1.0.1
      browserify-sign: 4.2.3
      create-ecdh: 4.0.4
      create-hash: 1.2.0
      create-hmac: 1.1.7
      diffie-hellman: 5.0.3
      hash-base: 3.0.5
      inherits: 2.0.4
      pbkdf2: 3.1.2
      public-encrypt: 4.0.3
      randombytes: 2.1.0
      randomfill: 1.0.4
    dev: false

  /css-declaration-sorter/6.4.1_postcss@8.5.3:
    resolution: {integrity: sha512-rtdthzxKuyq6IzqX6jEcIzQF/YqccluefyCYheovBOLhFT/drQA9zj/UbRAa9J7C0o6EG6u3E6g+vKkay7/k3g==}
    engines: {node: ^10 || ^12 || >=14}
    peerDependencies:
      postcss: ^8.0.9
    dependencies:
      postcss: 8.5.3
    dev: true

  /css-loader/2.1.1:
    resolution: {integrity: sha512-OcKJU/lt232vl1P9EEDamhoO9iKY3tIjY5GU+XDLblAykTdgs6Ux9P1hTHve8nFKy5KPpOXOsVI/hIwi3841+w==}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      camelcase: 5.3.1
      icss-utils: 4.1.1
      loader-utils: 1.4.2
      normalize-path: 3.0.0
      postcss: 7.0.39
      postcss-modules-extract-imports: 2.0.0
      postcss-modules-local-by-default: 2.0.6
      postcss-modules-scope: 2.2.0
      postcss-modules-values: 2.0.0
      postcss-value-parser: 3.3.1
      schema-utils: 1.0.0
    dev: false

  /css-loader/6.11.0_webpack@5.99.8:
    resolution: {integrity: sha512-CTJ+AEQJjq5NzLga5pE39qdiSV56F8ywCIsqNIRF0r7BDgWsN25aazToqAFg7ZrtA/U016xudB3ffgweORxX7g==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true
    dependencies:
      icss-utils: 5.1.0_postcss@8.5.3
      postcss: 8.5.3
      postcss-modules-extract-imports: 3.1.0_postcss@8.5.3
      postcss-modules-local-by-default: 4.2.0_postcss@8.5.3
      postcss-modules-scope: 3.2.1_postcss@8.5.3
      postcss-modules-values: 4.0.0_postcss@8.5.3
      postcss-value-parser: 4.2.0
      semver: 7.7.2
      webpack: 5.99.8
    dev: true

  /css-minimizer-webpack-plugin/3.4.1_webpack@5.99.8:
    resolution: {integrity: sha512-1u6D71zeIfgngN2XNRJefc/hY7Ybsxd74Jm4qngIXyUEk7fss3VUzuHxLAq/R8NAba4QU9OUSaMZlbpRc7bM4Q==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      '@parcel/css': '*'
      clean-css: '*'
      csso: '*'
      esbuild: '*'
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@parcel/css':
        optional: true
      clean-css:
        optional: true
      csso:
        optional: true
      esbuild:
        optional: true
    dependencies:
      cssnano: 5.1.15_postcss@8.5.3
      jest-worker: 27.5.1
      postcss: 8.5.3
      schema-utils: 4.3.2
      serialize-javascript: 6.0.2
      source-map: 0.6.1
      webpack: 5.99.8
    dev: true

  /css-select/4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1
    dev: true

  /css-tree/1.1.3:
    resolution: {integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1
    dev: true

  /css-what/2.1.3:
    resolution: {integrity: sha512-a+EPoD+uZiNfh+5fxw2nO9QwFa6nJe2Or35fGY6Ipw1R3R4AGz1d1TEZrCegvw2YTmZ0jXirGYlzxxpYSHwpEg==}
    dev: false

  /css-what/6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}
    dev: true

  /css/2.2.4:
    resolution: {integrity: sha512-oUnjmWpy0niI3x/mPL8dVEI1l7MnG3+HHyRPHf+YFSbK+svOhXpmSOcDURUh2aOCgl2grzrOPt1nHLuCVFULLw==}
    dependencies:
      inherits: 2.0.4
      source-map: 0.6.1
      source-map-resolve: 0.5.3
      urix: 0.1.0

  /cssesc/2.0.0:
    resolution: {integrity: sha512-MsCAG1z9lPdoO/IUMLSBWBSVxVtJ1395VGIQ+Fc2gNdkQ1hNDnQdw3YhA71WJCBW1vdwA0cAnk/DnW6bqoEUYg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /cssesc/3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  /cssnano-preset-default/5.2.14_postcss@8.5.3:
    resolution: {integrity: sha512-t0SFesj/ZV2OTylqQVOrFgEh5uanxbO6ZAdeCrNsUQ6fVuXwYTxJPNAGvGTxHbD68ldIJNec7PyYZDBrfDQ+6A==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      css-declaration-sorter: 6.4.1_postcss@8.5.3
      cssnano-utils: 3.1.0_postcss@8.5.3
      postcss: 8.5.3
      postcss-calc: 8.2.4_postcss@8.5.3
      postcss-colormin: 5.3.1_postcss@8.5.3
      postcss-convert-values: 5.1.3_postcss@8.5.3
      postcss-discard-comments: 5.1.2_postcss@8.5.3
      postcss-discard-duplicates: 5.1.0_postcss@8.5.3
      postcss-discard-empty: 5.1.1_postcss@8.5.3
      postcss-discard-overridden: 5.1.0_postcss@8.5.3
      postcss-merge-longhand: 5.1.7_postcss@8.5.3
      postcss-merge-rules: 5.1.4_postcss@8.5.3
      postcss-minify-font-values: 5.1.0_postcss@8.5.3
      postcss-minify-gradients: 5.1.1_postcss@8.5.3
      postcss-minify-params: 5.1.4_postcss@8.5.3
      postcss-minify-selectors: 5.2.1_postcss@8.5.3
      postcss-normalize-charset: 5.1.0_postcss@8.5.3
      postcss-normalize-display-values: 5.1.0_postcss@8.5.3
      postcss-normalize-positions: 5.1.1_postcss@8.5.3
      postcss-normalize-repeat-style: 5.1.1_postcss@8.5.3
      postcss-normalize-string: 5.1.0_postcss@8.5.3
      postcss-normalize-timing-functions: 5.1.0_postcss@8.5.3
      postcss-normalize-unicode: 5.1.1_postcss@8.5.3
      postcss-normalize-url: 5.1.0_postcss@8.5.3
      postcss-normalize-whitespace: 5.1.1_postcss@8.5.3
      postcss-ordered-values: 5.1.3_postcss@8.5.3
      postcss-reduce-initial: 5.1.2_postcss@8.5.3
      postcss-reduce-transforms: 5.1.0_postcss@8.5.3
      postcss-svgo: 5.1.0_postcss@8.5.3
      postcss-unique-selectors: 5.1.1_postcss@8.5.3
    dev: true

  /cssnano-utils/3.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-JQNR19/YZhz4psLX/rQ9M83e3z2Wf/HdJbryzte4a3NSuafyp9w/I4U+hx5C2S9g41qlstH7DEWnZaaj83OuEA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
    dev: true

  /cssnano/5.1.15_postcss@8.5.3:
    resolution: {integrity: sha512-j+BKgDcLDQA+eDifLx0EO4XSA56b7uut3BQFH+wbSaSTuGLuiyTa/wbRYthUXX8LC9mLg+WWKe8h+qJuwTAbHw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      cssnano-preset-default: 5.2.14_postcss@8.5.3
      lilconfig: 2.1.0
      postcss: 8.5.3
      yaml: 1.10.2
    dev: true

  /csso/4.2.0:
    resolution: {integrity: sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      css-tree: 1.1.3
    dev: true

  /cssom/0.3.8:
    resolution: {integrity: sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==}
    dev: true

  /cssom/0.4.4:
    resolution: {integrity: sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw==}
    dev: true

  /cssstyle/2.3.0:
    resolution: {integrity: sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A==}
    engines: {node: '>=8'}
    dependencies:
      cssom: 0.3.8
    dev: true

  /cyclist/1.0.2:
    resolution: {integrity: sha512-0sVXIohTfLqVIW3kb/0n6IiWF3Ifj5nm2XaSrLq2DI6fKIGa2fYAZdk917rUneaeLVpYfFcyXE2ft0fe3remsA==}
    dev: false

  /dashdash/1.14.1:
    resolution: {integrity: sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==}
    engines: {node: '>=0.10'}
    dependencies:
      assert-plus: 1.0.0

  /data-urls/1.1.0:
    resolution: {integrity: sha512-YTWYI9se1P55u58gL5GkQHW4P6VJBJ5iBT+B5a7i2Tjadhv52paJG0qHX4A0OR6/t52odI64KP2YvFpkDOi3eQ==}
    dependencies:
      abab: 2.0.6
      whatwg-mimetype: 2.3.0
      whatwg-url: 7.1.0
    dev: true

  /dayjs/1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==}
    dev: false

  /de-indent/1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==}

  /debounce/1.2.1:
    resolution: {integrity: sha512-XRRe6Glud4rd/ZGQfiV1ruXSfbvfJedlV9Y6zOlP+2K04vBYiJEte6stfFkCP03aMnY5tsipamumUjL14fofug==}
    dev: true

  /debug/2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    dependencies:
      ms: 2.0.0

  /debug/3.1.0:
    resolution: {integrity: sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==}
    dependencies:
      ms: 2.0.0
    dev: false

  /debug/3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    dependencies:
      ms: 2.1.3
    dev: false

  /debug/4.1.1:
    resolution: {integrity: sha512-pYAIzeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==}
    deprecated: Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)
    dependencies:
      ms: 2.1.3
    dev: false

  /debug/4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: true

  /debug/4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3

  /decamelize/1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /decode-uri-component/0.2.2:
    resolution: {integrity: sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==}
    engines: {node: '>=0.10'}

  /deep-equal/1.0.1:
    resolution: {integrity: sha512-bHtC0iYvWhyaTzvV3CZgPeZQqCOBGyGsVV7v4eevpdkLHfiSrXUdBG+qAuSz4RI70sszvjQ1QSZ98An1yNwpSw==}
    dev: false

  /deep-extend/0.6.0:
    resolution: {integrity: sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==}
    engines: {node: '>=4.0.0'}
    dev: true

  /deep-is/0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  /deepmerge/1.5.2:
    resolution: {integrity: sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /deepmerge/4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}
    dev: true

  /default-gateway/6.0.3:
    resolution: {integrity: sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==}
    engines: {node: '>= 10'}
    dependencies:
      execa: 5.1.1
    dev: true

  /defaults/1.0.4:
    resolution: {integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==}
    dependencies:
      clone: 1.0.4
    dev: true

  /define-data-property/1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  /define-lazy-prop/2.0.0:
    resolution: {integrity: sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==}
    engines: {node: '>=8'}
    dev: true

  /define-properties/1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  /define-property/0.2.5:
    resolution: {integrity: sha512-Rr7ADjQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 0.1.7

  /define-property/1.0.0:
    resolution: {integrity: sha512-cZTYKFWspt9jZsMscWo8sc/5lbPC9Q0N5nBLgb+Yd915iL3udB1uFgS3B8YCx66UVHq018DAVFoee7x+gxggeA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.3

  /define-property/2.0.2:
    resolution: {integrity: sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.3
      isobject: 3.0.1

  /delayed-stream/1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  /delegates/1.0.0:
    resolution: {integrity: sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==}
    dev: false

  /depd/1.1.2:
    resolution: {integrity: sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==}
    engines: {node: '>= 0.6'}

  /depd/2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  /des.js/1.1.0:
    resolution: {integrity: sha512-r17GxjhUCjSRy8aiJpr8/UadFIzMzJGexI3Nmz4ADi9LYSFx4gTBp80+NaX/YsXWWLhpZ7v/v/ubEc/bCNfKwg==}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: false

  /destroy/1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  /detect-libc/1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==}
    engines: {node: '>=0.10'}
    hasBin: true
    dev: true
    optional: true

  /detect-newline/3.1.0:
    resolution: {integrity: sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==}
    engines: {node: '>=8'}
    dev: true

  /detect-node/2.1.0:
    resolution: {integrity: sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==}
    dev: true

  /diff-sequences/25.2.6:
    resolution: {integrity: sha512-Hq8o7+6GaZeoFjtpgvRBUknSXNeJiCx7V9Fr94ZMljNiCr9n9L8H8aJqgWOQiDDGdyn29fRNcDdRVJ5fdyihfg==}
    engines: {node: '>= 8.3'}
    dev: true

  /diffie-hellman/5.0.3:
    resolution: {integrity: sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==}
    dependencies:
      bn.js: 4.12.2
      miller-rabin: 4.0.1
      randombytes: 2.1.0
    dev: false

  /dir-glob/3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0
    dev: true

  /dns-packet/5.6.1:
    resolution: {integrity: sha512-l4gcSouhcgIKRvyy99RNVOgxXiicE+2jZoNmaNmZ6JXiGajBOJAesk1OBlJuM5k2c+eudGdLxDqXuPCKIj6kpw==}
    engines: {node: '>=6'}
    dependencies:
      '@leichtgewicht/ip-codec': 2.0.5
    dev: true

  /dom-converter/0.2.0:
    resolution: {integrity: sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==}
    dependencies:
      utila: 0.4.0
    dev: true

  /dom-serializer/0.2.2:
    resolution: {integrity: sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==}
    dependencies:
      domelementtype: 2.3.0
      entities: 2.2.0
    dev: true

  /dom-serializer/1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0
    dev: true

  /dom-walk/0.1.2:
    resolution: {integrity: sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w==}
    dev: true

  /domain-browser/1.2.0:
    resolution: {integrity: sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA==}
    engines: {node: '>=0.4', npm: '>=1.2'}
    dev: false

  /domelementtype/1.3.1:
    resolution: {integrity: sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w==}
    dev: true

  /domelementtype/2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}
    dev: true

  /domexception/1.0.1:
    resolution: {integrity: sha512-raigMkn7CJNNo6Ihro1fzG7wr3fHuYVytzquZKX5n0yizGsTcYgzdIUwj1X9pK0VvjeihV+XiclP+DjwbsSKug==}
    deprecated: Use your platform's native DOMException instead
    dependencies:
      webidl-conversions: 4.0.2
    dev: true

  /domhandler/2.4.2:
    resolution: {integrity: sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA==}
    dependencies:
      domelementtype: 1.3.1
    dev: true

  /domhandler/4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0
    dev: true

  /domutils/1.7.0:
    resolution: {integrity: sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg==}
    dependencies:
      dom-serializer: 0.2.2
      domelementtype: 1.3.1
    dev: true

  /domutils/2.8.0:
    resolution: {integrity: sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==}
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1
    dev: true

  /dot-case/3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1
    dev: true

  /dotenv-expand/5.1.0:
    resolution: {integrity: sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==}
    dev: true

  /dotenv/10.0.0:
    resolution: {integrity: sha512-rlBi9d8jpv9Sf1klPjNfFAuWDjKLwTIJJ/VxtoTwIR6hnZxcEOQCZg2oIL3MWBYw5GpUDKOEnND7LXTbIpQ03Q==}
    engines: {node: '>=10'}
    dev: true

  /dunder-proto/1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  /duplexer/0.1.2:
    resolution: {integrity: sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==}
    dev: true

  /duplexify/3.7.1:
    resolution: {integrity: sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==}
    dependencies:
      end-of-stream: 1.4.4
      inherits: 2.0.4
      readable-stream: 2.3.8
      stream-shift: 1.0.3
    dev: false

  /eastasianwidth/0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}
    dev: true

  /easy-stack/1.0.1:
    resolution: {integrity: sha512-wK2sCs4feiiJeFXn3zvY0p41mdU5VUgbgs1rNsc/y5ngFUijdWd+iIN8eoyuZHKB8xN6BL4PdWmzqFmxNg6V2w==}
    engines: {node: '>=6.0.0'}
    dev: true

  /ecc-jsbn/0.1.2:
    resolution: {integrity: sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==}
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2

  /ee-first/1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}

  /electron-to-chromium/1.5.152:
    resolution: {integrity: sha512-xBOfg/EBaIlVsHipHl2VdTPJRSvErNUaqW8ejTq5OlOlIYx1wOllCHsAvAIrr55jD1IYEfdR86miUEt8H5IeJg==}

  /elliptic/6.6.1:
    resolution: {integrity: sha512-RaddvvMatK2LJHqFJ+YA4WysVN5Ita9E35botqIYspQ4TkRAlCicdzKOjlyv/1Za5RyTNn7di//eEV0uTAfe3g==}
    dependencies:
      bn.js: 4.12.2
      brorand: 1.1.0
      hash.js: 1.1.7
      hmac-drbg: 1.0.1
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: false

  /emoji-regex/8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}
    dev: true

  /emoji-regex/9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}
    dev: true

  /emojis-list/3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}

  /encodeurl/1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}

  /encodeurl/2.0.0:
    resolution: {integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==}
    engines: {node: '>= 0.8'}
    dev: true

  /end-of-stream/1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==}
    dependencies:
      once: 1.4.0

  /engine.io-client/3.5.4:
    resolution: {integrity: sha512-ydc8uuMMDxC5KCKNJN3zZKYJk2sgyTuTZQ7Aj1DJSsLKAcizA/PzWivw8fZMIjJVBo2CJOYzntv4FSjY/Lr//g==}
    dependencies:
      component-emitter: 1.3.1
      component-inherit: 0.0.3
      debug: 3.1.0
      engine.io-parser: 2.2.1
      has-cors: 1.1.0
      indexof: 0.0.1
      parseqs: 0.0.6
      parseuri: 0.0.6
      ws: 7.5.10
      xmlhttprequest-ssl: 1.6.3
      yeast: 0.1.2
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: false

  /engine.io-parser/2.2.1:
    resolution: {integrity: sha512-x+dN/fBH8Ro8TFwJ+rkB2AmuVw9Yu2mockR/p3W8f8YtExwFgDvBDi0GWyb4ZLkpahtDGZgtr3zLovanJghPqg==}
    dependencies:
      after: 0.8.2
      arraybuffer.slice: 0.0.7
      base64-arraybuffer: 0.1.4
      blob: 0.0.5
      has-binary2: 1.0.3
    dev: false

  /engine.io/3.6.2:
    resolution: {integrity: sha512-C4JjGQZLY3kWlIDx0BQNKizbrfpb7NahxDztGdN5jrPK2ghmXiNDN+E/t0JzDeNRZxPVaszxEng42Pmj27X/0w==}
    engines: {node: '>=8.0.0'}
    dependencies:
      accepts: 1.3.8
      base64id: 2.0.0
      cookie: 0.4.2
      debug: 4.1.1
      engine.io-parser: 2.2.1
      ws: 7.5.10
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: false

  /enhanced-resolve/4.5.0:
    resolution: {integrity: sha512-Nv9m36S/vxpsI+Hc4/ZGRs0n9mXqSWGGq49zxb/cJfPAQMbUtttJAlNPS4AQzaBdw/pKskw5bMbekT/Y7W/Wlg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      graceful-fs: 4.2.11
      memory-fs: 0.5.0
      tapable: 1.1.3
    dev: false

  /enhanced-resolve/5.18.1:
    resolution: {integrity: sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==}
    engines: {node: '>=10.13.0'}
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1
    dev: true

  /entities/1.1.2:
    resolution: {integrity: sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w==}
    dev: true

  /entities/2.2.0:
    resolution: {integrity: sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==}
    dev: true

  /entities/4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}
    dev: true

  /env-paths/2.2.1:
    resolution: {integrity: sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==}
    engines: {node: '>=6'}
    dev: false

  /envinfo/6.0.1:
    resolution: {integrity: sha512-IbMWvMQulMm1hiky1Zt5YTcSDEdZs0r9bt77mcLa4RUAKRYTGZvrb3MtAt47FuldPxwL+u2LtQex1FajIW1/Cw==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /errno/0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true
    dependencies:
      prr: 1.0.1
    dev: false

  /error-ex/1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}
    dependencies:
      is-arrayish: 0.2.1

  /error-stack-parser/2.1.4:
    resolution: {integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==}
    dependencies:
      stackframe: 1.3.4
    dev: true

  /es-define-property/1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  /es-errors/1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  /es-module-lexer/1.7.0:
    resolution: {integrity: sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==}
    dev: true

  /es-object-atoms/1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0

  /escalade/3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  /escape-html/1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  /escape-string-regexp/1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  /escape-string-regexp/2.0.0:
    resolution: {integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==}
    engines: {node: '>=8'}
    dev: true

  /escape-string-regexp/4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}
    dev: true

  /escodegen/1.14.3:
    resolution: {integrity: sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==}
    engines: {node: '>=4.0'}
    hasBin: true
    dependencies:
      esprima: 4.0.1
      estraverse: 4.3.0
      esutils: 2.0.3
      optionator: 0.8.3
    optionalDependencies:
      source-map: 0.6.1

  /eslint-scope/4.0.3:
    resolution: {integrity: sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg==}
    engines: {node: '>=4.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: false

  /eslint-scope/5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: true

  /esprima/4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  /esrecurse/4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0

  /estraverse/4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  /estraverse/5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  /estree-walker/2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}
    dev: true

  /esutils/2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  /etag/1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}
    dev: true

  /event-pubsub/4.3.0:
    resolution: {integrity: sha512-z7IyloorXvKbFx9Bpie2+vMJKKx1fH1EN5yiTfp8CiLOTptSYy1g8H4yDpGlEdshL1PBiFtBHepF2cNsqeEeFQ==}
    engines: {node: '>=4.0.0'}
    dev: true

  /eventemitter3/4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}
    dev: true

  /eventemitter3/5.0.1:
    resolution: {integrity: sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==}
    dev: true

  /events/3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  /evp_bytestokey/1.0.3:
    resolution: {integrity: sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==}
    dependencies:
      md5.js: 1.3.5
      safe-buffer: 5.2.1
    dev: false

  /exec-sh/0.3.6:
    resolution: {integrity: sha512-nQn+hI3yp+oD0huYhKwvYI32+JFeq+XkNcD1GAo3Y/MjxsfVGmrrzrnzjWiNY6f+pUCP440fThsFh5gZrRAU/w==}
    dev: true

  /execa/1.0.0:
    resolution: {integrity: sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==}
    engines: {node: '>=6'}
    dependencies:
      cross-spawn: 6.0.6
      get-stream: 4.1.0
      is-stream: 1.1.0
      npm-run-path: 2.0.2
      p-finally: 1.0.0
      signal-exit: 3.0.7
      strip-eof: 1.0.0
    dev: true

  /execa/3.4.0:
    resolution: {integrity: sha512-r9vdGQk4bmCuK1yKQu1KTwcT2zwfWdbdaXfCtAh+5nU/4fSX+JAb7vZGvI5naJrQlvONrEB20jeruESI69530g==}
    engines: {node: ^8.12.0 || >=9.7.0}
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 5.2.0
      human-signals: 1.1.1
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      p-finally: 2.0.1
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: true

  /execa/5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: true

  /execa/7.2.0:
    resolution: {integrity: sha512-UduyVP7TLB5IcAQl+OzLyLcS/l32W/GLg+AhHJ+ow40FOk2U3SAllPwR44v4vmdFwIWqpdwxxpQbF1n5ta9seA==}
    engines: {node: ^14.18.0 || ^16.14.0 || >=18.0.0}
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 6.0.1
      human-signals: 4.3.1
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 3.0.7
      strip-final-newline: 3.0.0
    dev: true

  /exif-parser/0.1.12:
    resolution: {integrity: sha512-c2bQfLNbMzLPmzQuOr8fy0csy84WmwnER81W88DzTp9CYNPJ6yzOj2EZAh9pywYpqHnshVLHQJ8WzldAyfY+Iw==}
    dev: true

  /exit/0.1.2:
    resolution: {integrity: sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==}
    engines: {node: '>= 0.8.0'}
    dev: true

  /expand-brackets/2.1.4:
    resolution: {integrity: sha512-w/ozOKR9Obk3qoWeY/WDi6MFta9AoMR+zud60mdnbniMcBxRuFJyDt2LdX/14A1UABeqk+Uk+LDfUpvoGKppZA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      posix-character-classes: 0.1.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2

  /expect/25.5.0:
    resolution: {integrity: sha512-w7KAXo0+6qqZZhovCaBVPSIqQp7/UTcx4M9uKt2m6pd2VB1voyC8JizLRqeEqud3AAVP02g+hbErDu5gu64tlA==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      ansi-styles: 4.3.0
      jest-get-type: 25.2.6
      jest-matcher-utils: 25.5.0
      jest-message-util: 25.5.0
      jest-regex-util: 25.2.6
    dev: true

  /express/4.21.2:
    resolution: {integrity: sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA==}
    engines: {node: '>= 0.10.0'}
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.3
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.7.1
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.3.1
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.3
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.12
      proxy-addr: 2.0.7
      qs: 6.13.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.19.0
      serve-static: 1.16.2
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    dev: true

  /extend-shallow/2.0.1:
    resolution: {integrity: sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 0.1.1

  /extend-shallow/3.0.2:
    resolution: {integrity: sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q==}
    engines: {node: '>=0.10.0'}
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1

  /extend/3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  /extglob/2.0.4:
    resolution: {integrity: sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-unique: 0.3.2
      define-property: 1.0.0
      expand-brackets: 2.1.4
      extend-shallow: 2.0.1
      fragment-cache: 0.2.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2

  /extsprintf/1.3.0:
    resolution: {integrity: sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==}
    engines: {'0': node >=0.6.0}

  /fast-deep-equal/3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  /fast-glob/3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8
    dev: true

  /fast-json-stable-stringify/2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  /fast-levenshtein/2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  /fast-uri/3.0.6:
    resolution: {integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==}
    dev: true

  /fastq/1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}
    dependencies:
      reusify: 1.1.0
    dev: true

  /faye-websocket/0.11.4:
    resolution: {integrity: sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==}
    engines: {node: '>=0.8.0'}
    dependencies:
      websocket-driver: 0.7.4
    dev: true

  /fb-watchman/2.0.2:
    resolution: {integrity: sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==}
    dependencies:
      bser: 2.1.1
    dev: true

  /figgy-pudding/3.5.2:
    resolution: {integrity: sha512-0btnI/H8f2pavGMN8w40mlSKOfTK2SVJmBfBeVIj3kNw0swwgzyRq0d5TJVOwodFmtvpPeWPN/MCcfuWF0Ezbw==}
    deprecated: This module is no longer supported.
    dev: false

  /figures/2.0.0:
    resolution: {integrity: sha512-Oa2M9atig69ZkfwiApY8F2Yy+tzMbazyvqv21R0NsSC8floSOC09BbT1ITWAdoMGQvJ/aZnR1KMwdx9tvHnTNA==}
    engines: {node: '>=4'}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: true

  /file-type/9.0.0:
    resolution: {integrity: sha512-Qe/5NJrgIOlwijpq3B7BEpzPFcgzggOTagZmkXQY4LA6bsXKTUstK7Wp12lEJ/mLKTpvIZxmIuRcLYWT6ov9lw==}
    engines: {node: '>=6'}
    dev: true

  /file-uri-to-path/1.0.0:
    resolution: {integrity: sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==}
    requiresBuild: true
    dev: false
    optional: true

  /fill-range/4.0.0:
    resolution: {integrity: sha512-VcpLTWqWDiTerugjj8e3+esbg+skS3M9e54UuR3iCeIDMXCLTsAH8hTSzDQU/X6/6t3eYkOKoZSef2PlU6U1XQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-number: 3.0.0
      repeat-string: 1.6.1
      to-regex-range: 2.1.1

  /fill-range/7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1

  /filter-obj/1.1.0:
    resolution: {integrity: sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /finalhandler/1.3.1:
    resolution: {integrity: sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      debug: 2.6.9
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    dev: true

  /find-cache-dir/2.1.0:
    resolution: {integrity: sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ==}
    engines: {node: '>=6'}
    dependencies:
      commondir: 1.0.1
      make-dir: 2.1.0
      pkg-dir: 3.0.0
    dev: false

  /find-cache-dir/3.3.2:
    resolution: {integrity: sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==}
    engines: {node: '>=8'}
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0

  /find-up/3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==}
    engines: {node: '>=6'}
    dependencies:
      locate-path: 3.0.0
    dev: false

  /find-up/4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  /flat/5.0.2:
    resolution: {integrity: sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==}
    hasBin: true
    dev: true

  /flush-write-stream/1.1.1:
    resolution: {integrity: sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /flyio/0.6.14:
    resolution: {integrity: sha512-RE2OXE1ZZmcXOKb0jCtGyquHDxpAqHg17CZ8lmQKRfl3x1kP+NBpaQDx4WgN7DNpLJjFnspTzTEQpwRGg6/xaA==}
    dependencies:
      request: 2.88.2
    dev: false

  /follow-redirects/1.15.9_debug@4.4.1:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dependencies:
      debug: 4.4.1
    dev: true

  /for-in/1.0.2:
    resolution: {integrity: sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ==}
    engines: {node: '>=0.10.0'}

  /forever-agent/0.6.1:
    resolution: {integrity: sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==}

  /form-data/2.3.3:
    resolution: {integrity: sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==}
    engines: {node: '>= 0.12'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  /formidable/1.2.6:
    resolution: {integrity: sha512-KcpbcpuLNOwrEjnbpMC0gS+X8ciDoZE1kkqzat4a8vrprf+s9pKNQ/QIwWfbfs4ltgmFl3MD177SNTkve3BwGQ==}
    deprecated: 'Please upgrade to latest, formidable@v2 or formidable@v3! Check these notes: https://bit.ly/2ZEqIau'
    dev: false

  /forwarded/0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}
    dev: true

  /fraction.js/4.3.7:
    resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==}
    dev: true

  /fragment-cache/0.2.1:
    resolution: {integrity: sha512-GMBAbW9antB8iZRHLoGw0b3HANt57diZYFO/HL1JGIC1MjKrdmhxvrJbupnVvpys0zsz7yBApXdQyfepKly2kA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-cache: 0.2.2

  /fresh/0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}

  /from2/2.3.0:
    resolution: {integrity: sha512-OMcX/4IC/uqEPVgGeyfN22LJk6AZrMkRZHxcHBMBvHScDGgwTm2GT2Wkgtocyd3JfZffjj2kYUDXXII0Fk9W0g==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /fs-extra/10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==}
    engines: {node: '>=12'}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1
    dev: true

  /fs-extra/7.0.1:
    resolution: {integrity: sha512-YJDaCJZEnBmcbw13fvdAM9AwNOJwOzrE4pqMqBq5nFiEqXUqHwlK4B+3pUw6JNvfSPtX05xFHtYy/1ni01eGCw==}
    engines: {node: '>=6 <7 || >=8'}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2
    dev: false

  /fs-extra/8.1.0:
    resolution: {integrity: sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==}
    engines: {node: '>=6 <7 || >=8'}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2
    dev: true

  /fs-extra/9.1.0:
    resolution: {integrity: sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==}
    engines: {node: '>=10'}
    dependencies:
      at-least-node: 1.0.0
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1
    dev: true

  /fs-monkey/1.0.6:
    resolution: {integrity: sha512-b1FMfwetIKymC0eioW7mTywihSQE4oLzQn1dB6rZB5fx/3NpNEdAWeCSMB+60/AeT0TCXsxzAlcYVEFCTAksWg==}
    dev: true

  /fs-write-stream-atomic/1.0.10:
    resolution: {integrity: sha512-gehEzmPn2nAwr39eay+x3X34Ra+M2QlVUTLhkXPjWdeO8RF9kszk116avgBJM3ZyNHgHXBNx+VmPaFC36k0PzA==}
    deprecated: This package is no longer supported.
    dependencies:
      graceful-fs: 4.2.11
      iferr: 0.1.5
      imurmurhash: 0.1.4
      readable-stream: 2.3.8
    dev: false

  /fs.realpath/1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  /fsevents/1.2.13:
    resolution: {integrity: sha512-oWb1Z6mkHIskLzEJ/XWX0srkpkTQ7vaopMQkyaEIoq0fmtFVxOthb8cCxeT+p3ynTdkk/RZwbgG4brR5BeWECw==}
    engines: {node: '>= 4.0'}
    os: [darwin]
    deprecated: Upgrade to fsevents v2 to mitigate potential security issues
    requiresBuild: true
    dependencies:
      bindings: 1.5.0
      nan: 2.22.2
    dev: false
    optional: true

  /fsevents/2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    optional: true

  /function-bind/1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  /gensync/1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  /get-caller-file/2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}
    dev: true

  /get-intrinsic/1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  /get-package-type/0.1.0:
    resolution: {integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==}
    engines: {node: '>=8.0.0'}
    dev: true

  /get-proto/1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  /get-stream/4.1.0:
    resolution: {integrity: sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==}
    engines: {node: '>=6'}
    dependencies:
      pump: 3.0.2
    dev: true

  /get-stream/5.2.0:
    resolution: {integrity: sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==}
    engines: {node: '>=8'}
    dependencies:
      pump: 3.0.2
    dev: true

  /get-stream/6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}
    dev: true

  /get-them-args/1.3.2:
    resolution: {integrity: sha512-LRn8Jlk+DwZE4GTlDbT3Hikd1wSHgLMme/+7ddlqKd7ldwR6LjJgTVWzBnR01wnYGe4KgrXjg287RaI22UHmAw==}
    dev: true

  /get-value/2.0.6:
    resolution: {integrity: sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA==}
    engines: {node: '>=0.10.0'}

  /getpass/0.1.7:
    resolution: {integrity: sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==}
    dependencies:
      assert-plus: 1.0.0

  /glob-escape/0.0.2:
    resolution: {integrity: sha512-L/cXYz8x7qer1HAyUQ+mbjcUsJVdpRxpAf7CwqHoNBs9vTpABlGfNN4tzkDxt+u3Z7ZncVyKlCNPtzb0R/7WbA==}
    engines: {node: '>= 0.10'}
    dev: true

  /glob-parent/3.1.0:
    resolution: {integrity: sha512-E8Ak/2+dZY6fnzlR7+ueWvhsH1SjHr4jjss4YS/h4py44jY9MhK/VFdaZJAWDz6BbL21KeteKxFSFpq8OS5gVA==}
    dependencies:
      is-glob: 3.1.0
      path-dirname: 1.0.2
    dev: false
    optional: true

  /glob-parent/5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3

  /glob-parent/6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob-to-regexp/0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}
    dev: true

  /glob/7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  /global/4.4.0:
    resolution: {integrity: sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==}
    dependencies:
      min-document: 2.19.0
      process: 0.11.10
    dev: true

  /globals/11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  /globby/11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0
    dev: true

  /gopd/1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  /graceful-fs/4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  /growly/1.3.0:
    resolution: {integrity: sha512-+xGQY0YyAWCnqy7Cd++hc2JqMYzlm0dG30Jd0beaA64sROr8C4nt8Yc9V5Ro3avlSUDTN0ulqP/VBKi1/lLygw==}
    dev: true
    optional: true

  /gzip-size/6.0.0:
    resolution: {integrity: sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==}
    engines: {node: '>=10'}
    dependencies:
      duplexer: 0.1.2
    dev: true

  /handle-thing/2.0.1:
    resolution: {integrity: sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==}
    dev: true

  /har-schema/2.0.0:
    resolution: {integrity: sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==}
    engines: {node: '>=4'}

  /har-validator/5.1.5:
    resolution: {integrity: sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0

  /has-binary2/1.0.3:
    resolution: {integrity: sha512-G1LWKhDSvhGeAQ8mPVQlqNcOB2sJdwATtZKl2pDKKHfpf/rYj24lkinxf69blJbnsvtqqNU+L3SL50vzZhXOnw==}
    dependencies:
      isarray: 2.0.1
    dev: false

  /has-cors/1.1.0:
    resolution: {integrity: sha512-g5VNKdkFuUuVCP9gYfDJHjK2nqdQJ7aDLTnycnc2+RvsOQbuLdF5pm7vuE5J76SEBIQjs4kQY/BWq74JUmjbXA==}
    dev: false

  /has-flag/3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  /has-flag/4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  /has-property-descriptors/1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}
    dependencies:
      es-define-property: 1.0.1

  /has-symbols/1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  /has-tostringtag/1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.1.0
    dev: false

  /has-value/0.3.1:
    resolution: {integrity: sha512-gpG936j8/MzaeID5Yif+577c17TxaDmhuyVgSwtnL/q8UUTySg8Mecb+8Cf1otgLoD7DDH75axp86ER7LFsf3Q==}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 0.1.4
      isobject: 2.1.0

  /has-value/1.0.0:
    resolution: {integrity: sha512-IBXk4GTsLYdQ7Rvt+GRBrFSVEkmuOUy4re0Xjd9kJSUQpnTrWR4/y9RpfexN9vkAPMFuQoeWKwqzPozRTlasGw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 1.0.0
      isobject: 3.0.1

  /has-values/0.1.4:
    resolution: {integrity: sha512-J8S0cEdWuQbqD9//tlZxiMuMNmxB8PlEwvYwuxsTmR1G5RXUePEX/SJn7aD0GMLieuZYSwNH0cQuJGwnYunXRQ==}
    engines: {node: '>=0.10.0'}

  /has-values/1.0.0:
    resolution: {integrity: sha512-ODYZC64uqzmtfGMEAX/FvZiRyWLpAC3vYnNunURUnkGVTS+mI0smVsWaPydRBsE3g+ok7h960jChO8mFcWlHaQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      kind-of: 4.0.0

  /hash-base/3.0.5:
    resolution: {integrity: sha512-vXm0l45VbcHEVlTCzs8M+s0VeYsB2lnlAaThoLKGXr3bE/VWDOelNUnycUPEhKEaXARL2TEFjBOyUiM6+55KBg==}
    engines: {node: '>= 0.10'}
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /hash-sum/1.0.2:
    resolution: {integrity: sha512-fUs4B4L+mlt8/XAtSOGMUO1TXmAelItBPtJG7CyHJfYTdDjwisntGO2JQz7oUsatOY9o68+57eziUVNw/mRHmA==}

  /hash-sum/2.0.0:
    resolution: {integrity: sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg==}
    dev: true

  /hash.js/1.1.7:
    resolution: {integrity: sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: false

  /hasown/2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: 1.1.2

  /he/1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  /highlight.js/10.7.3:
    resolution: {integrity: sha512-tzcUFauisWKNHaRkN4Wjl/ZA07gENAjFl3J/c480dprkGTg5EQstgaNFqBfUqCq54kZRIEcreTsAgF/m2quD7A==}
    dev: true

  /hmac-drbg/1.0.1:
    resolution: {integrity: sha512-Tti3gMqLdZfhOQY1Mzf/AanLiqh1WTiJgEj26ZuYQ9fbkLomzGchCws4FyrSd4VkpBfiNhaE1On+lOz894jvXg==}
    dependencies:
      hash.js: 1.1.7
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: false

  /hosted-git-info/2.8.9:
    resolution: {integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==}
    dev: true

  /hpack.js/2.1.6:
    resolution: {integrity: sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ==}
    dependencies:
      inherits: 2.0.4
      obuf: 1.1.2
      readable-stream: 2.3.8
      wbuf: 1.7.3
    dev: true

  /html-encoding-sniffer/1.0.2:
    resolution: {integrity: sha512-71lZziiDnsuabfdYiUeWdCVyKuqwWi23L8YeIgV9jSSZHCtb6wB1BKWooH7L3tn4/FuZJMVWyNaIDr4RGmaSYw==}
    dependencies:
      whatwg-encoding: 1.0.5
    dev: true

  /html-entities/2.6.0:
    resolution: {integrity: sha512-kig+rMn/QOVRvr7c86gQ8lWXq+Hkv6CbAH1hLu+RG338StTpE8Z0b44SDVaqVu7HGKf27frdmUYEs9hTUX/cLQ==}
    dev: true

  /html-escaper/2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}
    dev: true

  /html-minifier-terser/6.1.0:
    resolution: {integrity: sha512-YXxSlJBZTP7RS3tWnQw74ooKa6L9b9i9QYXY21eUEvhZ3u9XLfv6OnFsQq6RxkhHygsaUMvYsZRV5rU/OVNZxw==}
    engines: {node: '>=12'}
    hasBin: true
    dependencies:
      camel-case: 4.1.2
      clean-css: 5.3.3
      commander: 8.3.0
      he: 1.2.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 5.39.1
    dev: true

  /html-tags/2.0.0:
    resolution: {integrity: sha512-+Il6N8cCo2wB/Vd3gqy/8TZhTD3QvcVeQLCnZiGkGCH3JP28IgGAY41giccp2W4R3jfyJPAP318FQTa1yU7K7g==}
    engines: {node: '>=4'}
    dev: true

  /html-webpack-plugin/5.6.3_webpack@5.99.8:
    resolution: {integrity: sha512-QSf1yjtSAsmf7rYBV7XX86uua4W/vkhIt0xNXKbsi2foEeW7vjJQz4bhnpL3xH+l1ryl1680uNv968Z+X6jSYg==}
    engines: {node: '>=10.13.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      webpack: ^5.20.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true
    dependencies:
      '@types/html-minifier-terser': 6.1.0
      html-minifier-terser: 6.1.0
      lodash: 4.17.21
      pretty-error: 4.0.0
      tapable: 2.2.1
      webpack: 5.99.8
    dev: true

  /htmlparser2/6.1.0:
    resolution: {integrity: sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0
    dev: true

  /http-assert/1.5.0:
    resolution: {integrity: sha512-uPpH7OKX4H25hBmU6G1jWNaqJGpTXxey+YOUizJUAgu0AjLUeC8D73hTrhvDS5D+GJN1DN1+hhc/eF/wpxtp0w==}
    engines: {node: '>= 0.8'}
    dependencies:
      deep-equal: 1.0.1
      http-errors: 1.8.1
    dev: false

  /http-deceiver/1.2.7:
    resolution: {integrity: sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw==}
    dev: true

  /http-errors/1.6.3:
    resolution: {integrity: sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==}
    engines: {node: '>= 0.6'}
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0

  /http-errors/1.8.1:
    resolution: {integrity: sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==}
    engines: {node: '>= 0.6'}
    dependencies:
      depd: 1.1.2
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 1.5.0
      toidentifier: 1.0.1
    dev: false

  /http-errors/2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  /http-parser-js/0.5.10:
    resolution: {integrity: sha512-Pysuw9XpUq5dVc/2SMHpuTY01RFl8fttgcyunjL7eEMhGM3cI4eOmiCycJDVCo/7O7ClfQD3SaI6ftDzqOXYMA==}
    dev: true

  /http-proxy-middleware/2.0.9_g3bw2jtlgbqqf2rcku7vbigucu:
    resolution: {integrity: sha512-c1IyJYLYppU574+YI7R4QyX2ystMtVXZwIdzazUIPIJsHuWNd+mho2j+bKoHftndicGj9yh+xjd+l0yj7VeT1Q==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/express': ^4.17.13
    peerDependenciesMeta:
      '@types/express':
        optional: true
    dependencies:
      '@types/express': 4.17.21
      '@types/http-proxy': 1.17.16
      http-proxy: 1.18.1_debug@4.4.1
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.8
    transitivePeerDependencies:
      - debug
    dev: true

  /http-proxy/1.18.1_debug@4.4.1:
    resolution: {integrity: sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==}
    engines: {node: '>=8.0.0'}
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.9_debug@4.4.1
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug
    dev: true

  /http-signature/1.2.0:
    resolution: {integrity: sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==}
    engines: {node: '>=0.8', npm: '>=1.3.7'}
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.18.0

  /https-browserify/1.0.0:
    resolution: {integrity: sha512-J+FkSdyD+0mA0N+81tMotaRMfSL9SGi+xpD3T6YApKsc3bGSXJlfXri3VyFOeYkfLRQisDk1W+jIFFKBeUBbBg==}
    dev: false

  /human-signals/1.1.1:
    resolution: {integrity: sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw==}
    engines: {node: '>=8.12.0'}
    dev: true

  /human-signals/2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}
    dev: true

  /human-signals/4.3.1:
    resolution: {integrity: sha512-nZXjEF2nbo7lIw3mgYjItAfgQXog3OjJogSbKa2CQIIvSGWcKgeJnQlNXip6NglNzYH45nSRiEVimMvYL8DDqQ==}
    engines: {node: '>=14.18.0'}
    dev: true

  /husky/8.0.3:
    resolution: {integrity: sha512-+dQSyqPh4x1hlO1swXBiNb2HzTDN1I2IGLQx1GrBuiqFJfoMrnZWwVmatvSiO+Iz8fBUnf+lekwNo4c2LlXItg==}
    engines: {node: '>=14'}
    hasBin: true
    dev: true

  /i18n/0.13.4:
    resolution: {integrity: sha512-GZnXWeA15jTi9gc1jfgrJcSrNYDg7qbJXSYMuibqPYb1ThORmGCeM+gL6LrDagYRHh87/q/D0jRSOhAfv6wAow==}
    engines: {node: '>=0.10.0'}
    dependencies:
      debug: 4.4.1
      make-plural: 7.4.0
      math-interval-parser: 2.0.1
      messageformat: 2.3.0
      mustache: 4.2.0
      sprintf-js: 1.1.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /iconv-lite/0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2

  /icss-replace-symbols/1.1.0:
    resolution: {integrity: sha512-chIaY3Vh2mh2Q3RGXttaDIzeiPvaVXJ+C4DAh/w3c37SKZ/U6PGMmuicR2EQQp9bKG8zLMCl7I+PtIoOOPp8Gg==}
    dev: false

  /icss-utils/4.1.1:
    resolution: {integrity: sha512-4aFq7wvWyMHKgxsH8QQtGpvbASCf+eM3wPRLI6R+MgAnTCZ6STYsRvttLvRWK0Nfif5piF394St3HeJDaljGPA==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /icss-utils/5.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.5.3
    dev: true

  /ieee754/1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  /iferr/0.1.5:
    resolution: {integrity: sha512-DUNFN5j7Tln0D+TxzloUjKB+CtVu6myn0JEFak6dG18mNt9YkQ6lzGCdafwofISZ1lLF3xRHJ98VKy9ynkcFaA==}
    dev: false

  /ignore/5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}
    dev: true

  /immediate/3.0.6:
    resolution: {integrity: sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==}
    dev: false

  /immutable/5.1.2:
    resolution: {integrity: sha512-qHKXW1q6liAk1Oys6umoaZbDRqjcjgSrbnrifHsfsttza7zcvRAsL7mMV6xWcyhwQy7Xj5v4hhbr6b+iDYwlmQ==}
    dev: true

  /import-fresh/3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  /import-local/3.2.0:
    resolution: {integrity: sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==}
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0
    dev: true

  /imurmurhash/0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  /indexes-of/1.0.1:
    resolution: {integrity: sha512-bup+4tap3Hympa+JBJUG7XuOsdNQ6fxt0MHyXMKuLBKn0OqsTfvUxkUrroEX1+B2VsSHvCjiIcZVxRtYa4nllA==}
    dev: true

  /indexof/0.0.1:
    resolution: {integrity: sha512-i0G7hLJ1z0DE8dsqJa2rycj9dBmNKgXBvotXtZYXakU9oivfB9Uj2ZBC27qqef2U58/ZLwalxa1X/RDCdkHtVg==}
    dev: false

  /infer-owner/1.0.4:
    resolution: {integrity: sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==}
    dev: false

  /inflation/2.1.0:
    resolution: {integrity: sha512-t54PPJHG1Pp7VQvxyVCJ9mBbjG3Hqryges9bXoOO6GExCPa+//i/d5GSuFtpx3ALLd7lgIAur6zrIlBQyJuMlQ==}
    engines: {node: '>= 0.8.0'}
    dev: false

  /inflight/1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  /inherits/2.0.3:
    resolution: {integrity: sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==}

  /inherits/2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  /ini/1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}
    dev: true

  /interpret/1.4.0:
    resolution: {integrity: sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==}
    engines: {node: '>= 0.10'}
    dev: false

  /intersection-observer/0.7.0:
    resolution: {integrity: sha512-Id0Fij0HsB/vKWGeBe9PxeY45ttRiBmhFyyt/geBdDHBYNctMRTE3dC1U3ujzz3lap+hVXlEcVaB56kZP/eEUg==}
    dev: false

  /invert-kv/3.0.1:
    resolution: {integrity: sha512-CYdFeFexxhv/Bcny+Q0BfOV+ltRlJcd4BBZBYFX/O0u4npJrgZtIcjokegtiSMAvlMTJ+Koq0GBCc//3bueQxw==}
    engines: {node: '>=8'}
    dev: true

  /ip-regex/2.1.0:
    resolution: {integrity: sha512-58yWmlHpp7VYfcdTwMTvwMmqx/Elfxjd9RXTDyMsbL7lLWmhMylLEqiYVLKuLzOZqVgiWXD9MfR62Vv89VRxkw==}
    engines: {node: '>=4'}
    dev: true

  /ipaddr.js/1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}
    dev: true

  /ipaddr.js/2.2.0:
    resolution: {integrity: sha512-Ag3wB2o37wslZS19hZqorUnrnzSkpOVy+IiiDEiTqNubEYpYuHWIf6K4psgN2ZWKExS4xhVCrRVfb/wfW8fWJA==}
    engines: {node: '>= 10'}
    dev: true

  /is-accessor-descriptor/1.0.1:
    resolution: {integrity: sha512-YBUanLI8Yoihw923YeFUS5fs0fF2f5TSFTNiYAAzhhDscDa3lEqYuz1pDOEP5KvX94I9ey3vsqjJcLVFVU+3QA==}
    engines: {node: '>= 0.10'}
    dependencies:
      hasown: 2.0.2

  /is-arrayish/0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  /is-binary-path/1.0.1:
    resolution: {integrity: sha512-9fRVlXc0uCxEDj1nQzaWONSpbTfx0FmJfzHF7pwlI8DkWGoHBBea4Pg5Ky0ojwwxQmnSifgbKkI06Qv0Ljgj+Q==}
    engines: {node: '>=0.10.0'}
    dependencies:
      binary-extensions: 1.13.1
    dev: false
    optional: true

  /is-binary-path/2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.3.0

  /is-buffer/1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}

  /is-ci/2.0.0:
    resolution: {integrity: sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w==}
    hasBin: true
    dependencies:
      ci-info: 2.0.0
    dev: true

  /is-core-module/2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2

  /is-data-descriptor/1.0.1:
    resolution: {integrity: sha512-bc4NlCDiCr28U4aEsQ3Qs2491gVq4V8G7MQyws968ImqjKuYtTJXrl7Vq7jsN7Ly/C3xj5KWFrY7sHNeDkAzXw==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2

  /is-descriptor/0.1.7:
    resolution: {integrity: sha512-C3grZTvObeN1xud4cRWl366OMXZTj0+HGyk4hvfpx4ZHt1Pb60ANSXqCK7pdOTeUQpRzECBSTphqvD7U+l22Eg==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-accessor-descriptor: 1.0.1
      is-data-descriptor: 1.0.1

  /is-descriptor/1.0.3:
    resolution: {integrity: sha512-JCNNGbwWZEVaSPtS45mdtrneRWJFp07LLmykxeFV5F6oBvNF8vHSfJuJgoT472pSfk+Mf8VnlrspaFBHWM8JAw==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-accessor-descriptor: 1.0.1
      is-data-descriptor: 1.0.1

  /is-docker/2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true
    dev: true

  /is-extendable/0.1.1:
    resolution: {integrity: sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==}
    engines: {node: '>=0.10.0'}

  /is-extendable/1.0.1:
    resolution: {integrity: sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-object: 2.0.4

  /is-extglob/2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  /is-file-esm/1.0.0:
    resolution: {integrity: sha512-rZlaNKb4Mr8WlRu2A9XdeoKgnO5aA53XdPHgCKVyCrQ/rWi89RET1+bq37Ru46obaQXeiX4vmFIm1vks41hoSA==}
    dependencies:
      read-pkg-up: 7.0.1
    dev: true

  /is-fullwidth-code-point/2.0.0:
    resolution: {integrity: sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==}
    engines: {node: '>=4'}
    dev: true

  /is-fullwidth-code-point/3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}
    dev: true

  /is-fullwidth-code-point/4.0.0:
    resolution: {integrity: sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==}
    engines: {node: '>=12'}
    dev: true

  /is-function/1.0.2:
    resolution: {integrity: sha512-lw7DUp0aWXYg+CBCN+JKkcE0Q2RayZnSvnZBlwgxHBQhqt5pZNVy4Ri7H9GmmXkdu7LUthszM+Tor1u/2iBcpQ==}
    dev: true

  /is-generator-fn/2.1.0:
    resolution: {integrity: sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==}
    engines: {node: '>=6'}
    dev: true

  /is-generator-function/1.1.0:
    resolution: {integrity: sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0
    dev: false

  /is-glob/3.1.0:
    resolution: {integrity: sha512-UFpDDrPgM6qpnFNI+rh/p3bUaq9hKLZN8bMUWzxmcnZVS3omf4IPK+BrewlnWjO1WmUsMYuSjKh4UJuV4+Lqmw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: false
    optional: true

  /is-glob/4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1

  /is-interactive/1.0.0:
    resolution: {integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==}
    engines: {node: '>=8'}
    dev: true

  /is-number/3.0.0:
    resolution: {integrity: sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  /is-plain-obj/3.0.0:
    resolution: {integrity: sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==}
    engines: {node: '>=10'}
    dev: true

  /is-plain-object/2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1

  /is-regex/1.2.1:
    resolution: {integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2
    dev: false

  /is-stream/1.1.0:
    resolution: {integrity: sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-stream/2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}
    dev: true

  /is-stream/3.0.0:
    resolution: {integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dev: true

  /is-typedarray/1.0.0:
    resolution: {integrity: sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==}

  /is-unicode-supported/0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==}
    engines: {node: '>=10'}
    dev: true

  /is-windows/1.0.2:
    resolution: {integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==}
    engines: {node: '>=0.10.0'}

  /is-wsl/1.1.0:
    resolution: {integrity: sha512-gfygJYZ2gLTDlmbWMI0CE2MwnFzSN/2SZfkMlItC4K/JBlsWVDB0bO6XhqcY13YXE7iMcAJnzTCJjPiTeJJ0Mw==}
    engines: {node: '>=4'}
    dev: false

  /is-wsl/2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==}
    engines: {node: '>=8'}
    dependencies:
      is-docker: 2.2.1
    dev: true

  /isarray/0.0.1:
    resolution: {integrity: sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==}
    dev: false

  /isarray/1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  /isarray/2.0.1:
    resolution: {integrity: sha512-c2cu3UxbI+b6kR3fy0nRnAhodsvR9dx7U5+znCOzdj6IfP3upFURTr0Xl5BlQZNKZjEtxrmVyfSdeE3O57smoQ==}
    dev: false

  /isexe/2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  /isobject/2.1.0:
    resolution: {integrity: sha512-+OUdGJlgjOBZDfxnDjYYG6zp487z0JGNQq3cYQYg5f5hKR+syHMsaztzGeml/4kGG55CSpKSpWTY+jYGgsHLgA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isarray: 1.0.0

  /isobject/3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}

  /isstream/0.1.2:
    resolution: {integrity: sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==}

  /istanbul-lib-coverage/3.2.2:
    resolution: {integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==}
    engines: {node: '>=8'}
    dev: true

  /istanbul-lib-instrument/4.0.3:
    resolution: {integrity: sha512-BXgQl9kf4WTCPCCpmFGoJkz/+uhvm7h7PFKUYxh7qarQd3ER33vHG//qaE8eN25l07YqZPpHXU9I09l/RD5aGQ==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/core': 7.27.1
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /istanbul-lib-instrument/5.2.1:
    resolution: {integrity: sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/core': 7.27.1
      '@babel/parser': 7.27.2
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /istanbul-lib-report/3.0.1:
    resolution: {integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==}
    engines: {node: '>=10'}
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0
    dev: true

  /istanbul-lib-source-maps/4.0.1:
    resolution: {integrity: sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==}
    engines: {node: '>=10'}
    dependencies:
      debug: 4.4.1
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /istanbul-reports/3.1.7:
    resolution: {integrity: sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==}
    engines: {node: '>=8'}
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1
    dev: true

  /javascript-stringify/2.1.0:
    resolution: {integrity: sha512-JVAfqNPTvNq3sB/VHQJAFxN/sPgKnsKrCwyRt15zwNCdrMMJDdcEOdubuy+DuJYYdm0ox1J4uzEuYKkN+9yhVg==}
    dev: true

  /jest-changed-files/25.5.0:
    resolution: {integrity: sha512-EOw9QEqapsDT7mKF162m8HFzRPbmP8qJQny6ldVOdOVBz3ACgPm/1nAn5fPQ/NDaYhX/AHkrGwwkCncpAVSXcw==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      execa: 3.4.0
      throat: 5.0.0
    dev: true

  /jest-cli/25.5.4:
    resolution: {integrity: sha512-rG8uJkIiOUpnREh1768/N3n27Cm+xPFkSNFO91tgg+8o2rXeVLStz+vkXkGr4UtzH6t1SNbjwoiswd7p4AhHTw==}
    engines: {node: '>= 8.3'}
    hasBin: true
    dependencies:
      '@jest/core': 25.5.4
      '@jest/test-result': 25.5.0
      '@jest/types': 25.5.0
      chalk: 3.0.0
      exit: 0.1.2
      graceful-fs: 4.2.11
      import-local: 3.2.0
      is-ci: 2.0.0
      jest-config: 25.5.4
      jest-util: 25.5.0
      jest-validate: 25.5.0
      prompts: 2.4.2
      realpath-native: 2.0.0
      yargs: 15.4.1
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /jest-config/25.5.4:
    resolution: {integrity: sha512-SZwR91SwcdK6bz7Gco8qL7YY2sx8tFJYzvg216DLihTWf+LKY/DoJXpM9nTzYakSyfblbqeU48p/p7Jzy05Atg==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@babel/core': 7.27.1
      '@jest/test-sequencer': 25.5.4
      '@jest/types': 25.5.0
      babel-jest: 25.5.1_@babel+core@7.27.1
      chalk: 3.0.0
      deepmerge: 4.3.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-environment-jsdom: 25.5.0
      jest-environment-node: 25.5.0
      jest-get-type: 25.2.6
      jest-jasmine2: 25.5.4
      jest-regex-util: 25.2.6
      jest-resolve: 25.5.1
      jest-util: 25.5.0
      jest-validate: 25.5.0
      micromatch: 4.0.8
      pretty-format: 25.5.0
      realpath-native: 2.0.0
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /jest-diff/25.5.0:
    resolution: {integrity: sha512-z1kygetuPiREYdNIumRpAHY6RXiGmp70YHptjdaxTWGmA085W3iCnXNx0DhflK3vwrKmrRWyY1wUpkPMVxMK7A==}
    engines: {node: '>= 8.3'}
    dependencies:
      chalk: 3.0.0
      diff-sequences: 25.2.6
      jest-get-type: 25.2.6
      pretty-format: 25.5.0
    dev: true

  /jest-docblock/25.3.0:
    resolution: {integrity: sha512-aktF0kCar8+zxRHxQZwxMy70stc9R1mOmrLsT5VO3pIT0uzGRSDAXxSlz4NqQWpuLjPpuMhPRl7H+5FRsvIQAg==}
    engines: {node: '>= 8.3'}
    dependencies:
      detect-newline: 3.1.0
    dev: true

  /jest-each/25.5.0:
    resolution: {integrity: sha512-QBogUxna3D8vtiItvn54xXde7+vuzqRrEeaw8r1s+1TG9eZLVJE5ZkKoSUlqFwRjnlaA4hyKGiu9OlkFIuKnjA==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      chalk: 3.0.0
      jest-get-type: 25.2.6
      jest-util: 25.5.0
      pretty-format: 25.5.0
    dev: true

  /jest-environment-jsdom/25.5.0:
    resolution: {integrity: sha512-7Jr02ydaq4jaWMZLY+Skn8wL5nVIYpWvmeatOHL3tOcV3Zw8sjnPpx+ZdeBfc457p8jCR9J6YCc+Lga0oIy62A==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/environment': 25.5.0
      '@jest/fake-timers': 25.5.0
      '@jest/types': 25.5.0
      jest-mock: 25.5.0
      jest-util: 25.5.0
      jsdom: 15.2.1
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - utf-8-validate
    dev: true

  /jest-environment-node/25.5.0:
    resolution: {integrity: sha512-iuxK6rQR2En9EID+2k+IBs5fCFd919gVVK5BeND82fYeLWPqvRcFNPKu9+gxTwfB5XwBGBvZ0HFQa+cHtIoslA==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/environment': 25.5.0
      '@jest/fake-timers': 25.5.0
      '@jest/types': 25.5.0
      jest-mock: 25.5.0
      jest-util: 25.5.0
      semver: 6.3.1
    dev: true

  /jest-get-type/25.2.6:
    resolution: {integrity: sha512-DxjtyzOHjObRM+sM1knti6or+eOgcGU4xVSb2HNP1TqO4ahsT+rqZg+nyqHWJSvWgKC5cG3QjGFBqxLghiF/Ig==}
    engines: {node: '>= 8.3'}
    dev: true

  /jest-haste-map/25.5.1:
    resolution: {integrity: sha512-dddgh9UZjV7SCDQUrQ+5t9yy8iEgKc1AKqZR9YDww8xsVOtzPQSMVLDChc21+g29oTRexb9/B0bIlZL+sWmvAQ==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      '@types/graceful-fs': 4.1.9
      anymatch: 3.1.3
      fb-watchman: 2.0.2
      graceful-fs: 4.2.11
      jest-serializer: 25.5.0
      jest-util: 25.5.0
      jest-worker: 25.5.0
      micromatch: 4.0.8
      sane: 4.1.0
      walker: 1.0.8
      which: 2.0.2
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /jest-jasmine2/25.5.4:
    resolution: {integrity: sha512-9acbWEfbmS8UpdcfqnDO+uBUgKa/9hcRh983IHdM+pKmJPL77G0sWAAK0V0kr5LK3a8cSBfkFSoncXwQlRZfkQ==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@babel/traverse': 7.27.1
      '@jest/environment': 25.5.0
      '@jest/source-map': 25.5.0
      '@jest/test-result': 25.5.0
      '@jest/types': 25.5.0
      chalk: 3.0.0
      co: 4.6.0
      expect: 25.5.0
      is-generator-fn: 2.1.0
      jest-each: 25.5.0
      jest-matcher-utils: 25.5.0
      jest-message-util: 25.5.0
      jest-runtime: 25.5.4
      jest-snapshot: 25.5.1
      jest-util: 25.5.0
      pretty-format: 25.5.0
      throat: 5.0.0
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /jest-leak-detector/25.5.0:
    resolution: {integrity: sha512-rV7JdLsanS8OkdDpZtgBf61L5xZ4NnYLBq72r6ldxahJWWczZjXawRsoHyXzibM5ed7C2QRjpp6ypgwGdKyoVA==}
    engines: {node: '>= 8.3'}
    dependencies:
      jest-get-type: 25.2.6
      pretty-format: 25.5.0
    dev: true

  /jest-matcher-utils/25.5.0:
    resolution: {integrity: sha512-VWI269+9JS5cpndnpCwm7dy7JtGQT30UHfrnM3mXl22gHGt/b7NkjBqXfbhZ8V4B7ANUsjK18PlSBmG0YH7gjw==}
    engines: {node: '>= 8.3'}
    dependencies:
      chalk: 3.0.0
      jest-diff: 25.5.0
      jest-get-type: 25.2.6
      pretty-format: 25.5.0
    dev: true

  /jest-message-util/25.5.0:
    resolution: {integrity: sha512-ezddz3YCT/LT0SKAmylVyWWIGYoKHOFOFXx3/nA4m794lfVUskMcwhip6vTgdVrOtYdjeQeis2ypzes9mZb4EA==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@babel/code-frame': 7.27.1
      '@jest/types': 25.5.0
      '@types/stack-utils': 1.0.1
      chalk: 3.0.0
      graceful-fs: 4.2.11
      micromatch: 4.0.8
      slash: 3.0.0
      stack-utils: 1.0.5
    dev: true

  /jest-mock/25.5.0:
    resolution: {integrity: sha512-eXWuTV8mKzp/ovHc5+3USJMYsTBhyQ+5A1Mak35dey/RG8GlM4YWVylZuGgVXinaW6tpvk/RSecmF37FKUlpXA==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
    dev: true

  /jest-pnp-resolver/1.2.3_jest-resolve@25.5.1:
    resolution: {integrity: sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==}
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true
    dependencies:
      jest-resolve: 25.5.1
    dev: true

  /jest-regex-util/25.2.6:
    resolution: {integrity: sha512-KQqf7a0NrtCkYmZZzodPftn7fL1cq3GQAFVMn5Hg8uKx/fIenLEobNanUxb7abQ1sjADHBseG/2FGpsv/wr+Qw==}
    engines: {node: '>= 8.3'}
    dev: true

  /jest-resolve-dependencies/25.5.4:
    resolution: {integrity: sha512-yFmbPd+DAQjJQg88HveObcGBA32nqNZ02fjYmtL16t1xw9bAttSn5UGRRhzMHIQbsep7znWvAvnD4kDqOFM0Uw==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      jest-regex-util: 25.2.6
      jest-snapshot: 25.5.1
    dev: true

  /jest-resolve/25.5.1:
    resolution: {integrity: sha512-Hc09hYch5aWdtejsUZhA+vSzcotf7fajSlPA6EZPE1RmPBAD39XtJhvHWFStid58iit4IPDLI/Da4cwdDmAHiQ==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      browser-resolve: 1.11.3
      chalk: 3.0.0
      graceful-fs: 4.2.11
      jest-pnp-resolver: 1.2.3_jest-resolve@25.5.1
      read-pkg-up: 7.0.1
      realpath-native: 2.0.0
      resolve: 1.22.10
      slash: 3.0.0
    dev: true

  /jest-runner/25.5.4:
    resolution: {integrity: sha512-V/2R7fKZo6blP8E9BL9vJ8aTU4TH2beuqGNxHbxi6t14XzTb+x90B3FRgdvuHm41GY8ch4xxvf0ATH4hdpjTqg==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/console': 25.5.0
      '@jest/environment': 25.5.0
      '@jest/test-result': 25.5.0
      '@jest/types': 25.5.0
      chalk: 3.0.0
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-config: 25.5.4
      jest-docblock: 25.3.0
      jest-haste-map: 25.5.1
      jest-jasmine2: 25.5.4
      jest-leak-detector: 25.5.0
      jest-message-util: 25.5.0
      jest-resolve: 25.5.1
      jest-runtime: 25.5.4
      jest-util: 25.5.0
      jest-worker: 25.5.0
      source-map-support: 0.5.21
      throat: 5.0.0
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /jest-runtime/25.5.4:
    resolution: {integrity: sha512-RWTt8LeWh3GvjYtASH2eezkc8AehVoWKK20udV6n3/gC87wlTbE1kIA+opCvNWyyPeBs6ptYsc6nyHUb1GlUVQ==}
    engines: {node: '>= 8.3'}
    hasBin: true
    dependencies:
      '@jest/console': 25.5.0
      '@jest/environment': 25.5.0
      '@jest/globals': 25.5.2
      '@jest/source-map': 25.5.0
      '@jest/test-result': 25.5.0
      '@jest/transform': 25.5.1
      '@jest/types': 25.5.0
      '@types/yargs': 15.0.19
      chalk: 3.0.0
      collect-v8-coverage: 1.0.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-config: 25.5.4
      jest-haste-map: 25.5.1
      jest-message-util: 25.5.0
      jest-mock: 25.5.0
      jest-regex-util: 25.2.6
      jest-resolve: 25.5.1
      jest-snapshot: 25.5.1
      jest-util: 25.5.0
      jest-validate: 25.5.0
      realpath-native: 2.0.0
      slash: 3.0.0
      strip-bom: 4.0.0
      yargs: 15.4.1
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /jest-serializer/25.5.0:
    resolution: {integrity: sha512-LxD8fY1lByomEPflwur9o4e2a5twSQ7TaVNLlFUuToIdoJuBt8tzHfCsZ42Ok6LkKXWzFWf3AGmheuLAA7LcCA==}
    engines: {node: '>= 8.3'}
    dependencies:
      graceful-fs: 4.2.11
    dev: true

  /jest-snapshot/25.5.1:
    resolution: {integrity: sha512-C02JE1TUe64p2v1auUJ2ze5vcuv32tkv9PyhEb318e8XOKF7MOyXdJ7kdjbvrp3ChPLU2usI7Rjxs97Dj5P0uQ==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@babel/types': 7.27.1
      '@jest/types': 25.5.0
      '@types/prettier': 1.19.1
      chalk: 3.0.0
      expect: 25.5.0
      graceful-fs: 4.2.11
      jest-diff: 25.5.0
      jest-get-type: 25.2.6
      jest-matcher-utils: 25.5.0
      jest-message-util: 25.5.0
      jest-resolve: 25.5.1
      make-dir: 3.1.0
      natural-compare: 1.4.0
      pretty-format: 25.5.0
      semver: 6.3.1
    dev: true

  /jest-util/25.5.0:
    resolution: {integrity: sha512-KVlX+WWg1zUTB9ktvhsg2PXZVdkI1NBevOJSkTKYAyXyH4QSvh+Lay/e/v+bmaFfrkfx43xD8QTfgobzlEXdIA==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      chalk: 3.0.0
      graceful-fs: 4.2.11
      is-ci: 2.0.0
      make-dir: 3.1.0
    dev: true

  /jest-validate/25.5.0:
    resolution: {integrity: sha512-okUFKqhZIpo3jDdtUXUZ2LxGUZJIlfdYBvZb1aczzxrlyMlqdnnws9MOxezoLGhSaFc2XYaHNReNQfj5zPIWyQ==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      camelcase: 5.3.1
      chalk: 3.0.0
      jest-get-type: 25.2.6
      leven: 3.1.0
      pretty-format: 25.5.0
    dev: true

  /jest-watcher/25.5.0:
    resolution: {integrity: sha512-XrSfJnVASEl+5+bb51V0Q7WQx65dTSk7NL4yDdVjPnRNpM0hG+ncFmDYJo9O8jaSRcAitVbuVawyXCRoxGrT5Q==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/test-result': 25.5.0
      '@jest/types': 25.5.0
      ansi-escapes: 4.3.2
      chalk: 3.0.0
      jest-util: 25.5.0
      string-length: 3.1.0
    dev: true

  /jest-worker/25.5.0:
    resolution: {integrity: sha512-/dsSmUkIy5EBGfv/IjjqmFxrNAUpBERfGs1oHROyD7yxjG/w+t0GOJDX8O1k32ySmd7+a5IhnJU2qQFcJ4n1vw==}
    engines: {node: '>= 8.3'}
    dependencies:
      merge-stream: 2.0.0
      supports-color: 7.2.0
    dev: true

  /jest-worker/27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': 22.15.17
      merge-stream: 2.0.0
      supports-color: 8.1.1
    dev: true

  /jest/25.5.4:
    resolution: {integrity: sha512-hHFJROBTqZahnO+X+PMtT6G2/ztqAZJveGqz//FnWWHurizkD05PQGzRZOhF3XP6z7SJmL+5tCfW8qV06JypwQ==}
    engines: {node: '>= 8.3'}
    hasBin: true
    dependencies:
      '@jest/core': 25.5.4
      import-local: 3.2.0
      jest-cli: 25.5.4
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /jimp/0.10.3_debug@4.4.1:
    resolution: {integrity: sha512-meVWmDMtyUG5uYjFkmzu0zBgnCvvxwWNi27c4cg55vWNVC9ES4Lcwb+ogx+uBBQE3Q+dLKjXaLl0JVW+nUNwbQ==}
    dependencies:
      '@babel/runtime': 7.17.9
      '@jimp/custom': 0.10.3_debug@4.4.1
      '@jimp/plugins': 0.10.3_6b7n5g776ny5s7kpuvjlc4hz4a
      '@jimp/types': 0.10.3_@jimp+custom@0.10.3
      core-js: 3.42.0
      regenerator-runtime: 0.13.11
    transitivePeerDependencies:
      - debug
    dev: true

  /jiti/1.21.7:
    resolution: {integrity: sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==}
    hasBin: true
    dev: false

  /joi/17.13.3:
    resolution: {integrity: sha512-otDA4ldcIx+ZXsKHWmp0YizCweVRZG96J10b0FevjfuncLO1oX59THoAmHkNubYJ+9gWsYsp5k8v4ib6oDv1fA==}
    dependencies:
      '@hapi/hoek': 9.3.0
      '@hapi/topo': 5.1.0
      '@sideway/address': 4.1.5
      '@sideway/formula': 3.0.1
      '@sideway/pinpoint': 2.0.0
    dev: true

  /jpeg-js/0.3.7:
    resolution: {integrity: sha512-9IXdWudL61npZjvLuVe/ktHiA41iE8qFyLB+4VDTblEsWBzeg8WQTlktdUK4CdncUqtUgUg0bbOmTE2bKBKaBQ==}
    dev: true

  /js-message/1.0.7:
    resolution: {integrity: sha512-efJLHhLjIyKRewNS9EGZ4UpI8NguuL6fKkhRxVuMmrGV2xN/0APGdQYwLFky5w9naebSZ0OwAGp0G6/2Cg90rA==}
    engines: {node: '>=0.6.0'}
    dev: true

  /js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  /js-yaml/3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1
    dev: true

  /js-yaml/4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
    dev: false

  /jsbn/0.1.1:
    resolution: {integrity: sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==}

  /jsdom/15.2.1:
    resolution: {integrity: sha512-fAl1W0/7T2G5vURSyxBzrJ1LSdQn6Tr5UX/xD4PXDx/PDgwygedfW6El/KIj3xJ7FU61TTYnc/l/B7P49Eqt6g==}
    engines: {node: '>=8'}
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true
    dependencies:
      abab: 2.0.6
      acorn: 7.4.1
      acorn-globals: 4.3.4
      array-equal: 1.0.2
      cssom: 0.4.4
      cssstyle: 2.3.0
      data-urls: 1.1.0
      domexception: 1.0.1
      escodegen: 1.14.3
      html-encoding-sniffer: 1.0.2
      nwsapi: 2.2.20
      parse5: 5.1.0
      pn: 1.1.0
      request: 2.88.2
      request-promise-native: 1.0.9_request@2.88.2
      saxes: 3.1.11
      symbol-tree: 3.2.4
      tough-cookie: 3.0.1
      w3c-hr-time: 1.0.2
      w3c-xmlserializer: 1.1.2
      webidl-conversions: 4.0.2
      whatwg-encoding: 1.0.5
      whatwg-mimetype: 2.3.0
      whatwg-url: 7.1.0
      ws: 7.5.10
      xml-name-validator: 3.0.0
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: true

  /jsesc/3.0.2:
    resolution: {integrity: sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==}
    engines: {node: '>=6'}
    hasBin: true

  /jsesc/3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true

  /json-parse-better-errors/1.0.2:
    resolution: {integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==}

  /json-parse-even-better-errors/2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  /json-schema-traverse/0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  /json-schema-traverse/1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}
    dev: true

  /json-schema/0.4.0:
    resolution: {integrity: sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==}

  /json-stringify-safe/5.0.1:
    resolution: {integrity: sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==}

  /json5/1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true
    dependencies:
      minimist: 1.2.8

  /json5/2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  /jsonc-parser/3.3.1:
    resolution: {integrity: sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ==}
    dev: true

  /jsonfile/4.0.0:
    resolution: {integrity: sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==}
    optionalDependencies:
      graceful-fs: 4.2.11

  /jsonfile/6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: true

  /jsprim/1.4.2:
    resolution: {integrity: sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  /jsrsasign/7.2.2:
    resolution: {integrity: sha512-hNN6476wY3ZP9X5HXdCmf/ovcbK+K56Fhg3cAIhgxy1WYXtGSNYDmlplecMqz1RbmqdBolkr4iyl5FAl6s4Xtg==}
    dev: false

  /jszip/3.10.1:
    resolution: {integrity: sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==}
    dependencies:
      lie: 3.3.0
      pako: 1.0.11
      readable-stream: 2.3.8
      setimmediate: 1.0.5
    dev: false

  /keygrip/1.1.0:
    resolution: {integrity: sha512-iYSchDJ+liQ8iwbSI2QqsQOvqv58eJCEanyJPJi+Khyu8smkcKSFUCbPwzFcL7YVtZ6eONjqRX/38caJ7QjRAQ==}
    engines: {node: '>= 0.6'}
    dependencies:
      tsscmp: 1.0.6
    dev: false

  /kill-port/1.6.1:
    resolution: {integrity: sha512-un0Y55cOM7JKGaLnGja28T38tDDop0AQ8N0KlAdyh+B1nmMoX8AnNmqPNZbS3mUMgiST51DCVqmbFT1gNJpVNw==}
    hasBin: true
    dependencies:
      get-them-args: 1.3.2
      shell-exec: 1.0.2
    dev: true

  /kind-of/3.2.2:
    resolution: {integrity: sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6

  /kind-of/4.0.0:
    resolution: {integrity: sha512-24XsCxmEbRwEDbz/qz3stgin8TTzZ1ESR56OMCN0ujYg+vRutNSiOj9bHH9u85DKgXguraugV5sFuvbD4FW/hw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6

  /kind-of/6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  /kleur/3.0.3:
    resolution: {integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==}
    engines: {node: '>=6'}
    dev: true

  /klona/2.0.6:
    resolution: {integrity: sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA==}
    engines: {node: '>= 8'}
    dev: true

  /koa-body/4.2.0:
    resolution: {integrity: sha512-wdGu7b9amk4Fnk/ytH8GuWwfs4fsB5iNkY8kZPpgQVb04QZSv85T0M8reb+cJmvLE8cjPYvBzRikD3s6qz8OoA==}
    dependencies:
      '@types/formidable': 1.2.8
      co-body: 5.2.0
      formidable: 1.2.6
    dev: false

  /koa-bodyparser/4.4.1:
    resolution: {integrity: sha512-kBH3IYPMb+iAXnrxIhXnW+gXV8OTzCu8VPDqvcDHW9SQrbkHmqPQtiZwrltNmSq6/lpipHnT7k7PsjlVD7kK0w==}
    engines: {node: '>=8.0.0'}
    dependencies:
      co-body: 6.2.0
      copy-to: 2.0.1
      type-is: 1.6.18
    dev: false

  /koa-compose/3.2.1:
    resolution: {integrity: sha512-8gen2cvKHIZ35eDEik5WOo8zbVp9t4cP8p4hW4uE55waxolLRexKKrqfCpwhGVppnB40jWeF8bZeTVg99eZgPw==}
    dependencies:
      any-promise: 1.3.0
    dev: false

  /koa-compose/4.1.0:
    resolution: {integrity: sha512-8ODW8TrDuMYvXRwra/Kh7/rJo9BtOfPc6qO8eAfC80CnCvSjSl0bkRM24X6/XBBEyj0v1nRUQ1LyOy3dbqOWXw==}
    dev: false

  /koa-convert/2.0.0:
    resolution: {integrity: sha512-asOvN6bFlSnxewce2e/DK3p4tltyfC4VM7ZwuTuepI7dEQVcvpyFuBcEARu1+Hxg8DIwytce2n7jrZtRlPrARA==}
    engines: {node: '>= 10'}
    dependencies:
      co: 4.6.0
      koa-compose: 4.1.0
    dev: false

  /koa-mount/4.2.0:
    resolution: {integrity: sha512-2iHQc7vbA9qLeVq5gKAYh3m5DOMMlMfIKjW/REPAS18Mf63daCJHHVXY9nbu7ivrnYn5PiPC4CE523Tf5qvjeQ==}
    engines: {node: '>= 7.6.0'}
    dependencies:
      debug: 4.4.1
      koa-compose: 4.1.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /koa-router/7.4.0:
    resolution: {integrity: sha512-IWhaDXeAnfDBEpWS6hkGdZ1ablgr6Q6pGdXCyK38RbzuH4LkUOpPqPw+3f8l8aTDrQmBQ7xJc0bs2yV4dzcO+g==}
    engines: {node: '>= 4'}
    deprecated: '**IMPORTANT 10x+ PERFORMANCE UPGRADE**: Please upgrade to v12.0.1+ as we have fixed an issue with debuglog causing 10x slower router benchmark performance, see https://github.com/koajs/router/pull/173'
    dependencies:
      debug: 3.2.7
      http-errors: 1.8.1
      koa-compose: 3.2.1
      methods: 1.1.2
      path-to-regexp: 1.9.0
      urijs: 1.19.11
    dev: false

  /koa-send/5.0.1:
    resolution: {integrity: sha512-tmcyQ/wXXuxpDxyNXv5yNNkdAMdFRqwtegBXUaowiQzUKqJehttS0x2j0eOZDQAyloAth5w6wwBImnFzkUz3pQ==}
    engines: {node: '>= 8'}
    dependencies:
      debug: 4.4.1
      http-errors: 1.8.1
      resolve-path: 1.4.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /koa-static/5.0.0:
    resolution: {integrity: sha512-UqyYyH5YEXaJrf9S8E23GoJFQZXkBVJ9zYYMPGz919MSX1KuvAcycIuS0ci150HCoPf4XQVhQ84Qf8xRPWxFaQ==}
    engines: {node: '>= 7.6.0'}
    dependencies:
      debug: 3.2.7
      koa-send: 5.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /koa/2.16.1:
    resolution: {integrity: sha512-umfX9d3iuSxTQP4pnzLOz0HKnPg0FaUUIKcye2lOiz3KPu1Y3M3xlz76dISdFPQs37P9eJz1wUpcTS6KDPn9fA==}
    engines: {node: ^4.8.4 || ^6.10.1 || ^7.10.1 || >= 8.1.4}
    dependencies:
      accepts: 1.3.8
      cache-content-type: 1.0.1
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookies: 0.9.1
      debug: 4.4.1
      delegates: 1.0.0
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      fresh: 0.5.2
      http-assert: 1.5.0
      http-errors: 1.8.1
      is-generator-function: 1.1.0
      koa-compose: 4.1.0
      koa-convert: 2.0.0
      on-finished: 2.4.1
      only: 0.0.2
      parseurl: 1.3.3
      statuses: 1.5.0
      type-is: 1.6.18
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /launch-editor-middleware/2.10.0:
    resolution: {integrity: sha512-RzZu7MeVlE3p1H6Sadc2BhuDGAj7bkeDCBpNq/zSENP4ohJGhso00k5+iYaRwKshIpiOAhMmimce+5D389xmSg==}
    dependencies:
      launch-editor: 2.10.0
    dev: true

  /launch-editor/2.10.0:
    resolution: {integrity: sha512-D7dBRJo/qcGX9xlvt/6wUYzQxjh5G1RvZPgPv8vi4KRU99DVQL/oW7tnVOCCTm2HGeo3C5HvGE5Yrh6UBoZ0vA==}
    dependencies:
      picocolors: 1.1.1
      shell-quote: 1.8.2
    dev: true

  /lcid/3.1.1:
    resolution: {integrity: sha512-M6T051+5QCGLBQb8id3hdvIW8+zeFV2FyBGFS9IEK5H9Wt4MueD4bW1eWikpHgZp+5xR3l5c8pZUkQsIA0BFZg==}
    engines: {node: '>=8'}
    dependencies:
      invert-kv: 3.0.1
    dev: true

  /leven/3.1.0:
    resolution: {integrity: sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==}
    engines: {node: '>=6'}
    dev: true

  /levn/0.3.0:
    resolution: {integrity: sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2

  /licia/1.48.0:
    resolution: {integrity: sha512-bBWiT5CSdEtwuAHiYTJ74yItCjIFdHi4xiFk6BRDfKa+sdCpkUHp69YKb5udNOJlHDzFjNjcMgNZ/+wQIHrB8A==}
    dev: true

  /lie/3.3.0:
    resolution: {integrity: sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==}
    dependencies:
      immediate: 3.0.6
    dev: false

  /lilconfig/2.1.0:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==}
    engines: {node: '>=10'}
    dev: true

  /lines-and-columns/1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  /lint-staged/13.3.0:
    resolution: {integrity: sha512-mPRtrYnipYYv1FEE134ufbWpeggNTo+O/UPzngoaKzbzHAthvR55am+8GfHTnqNRQVRRrYQLGW9ZyUoD7DsBHQ==}
    engines: {node: ^16.14.0 || >=18.0.0}
    hasBin: true
    dependencies:
      chalk: 5.3.0
      commander: 11.0.0
      debug: 4.3.4
      execa: 7.2.0
      lilconfig: 2.1.0
      listr2: 6.6.1
      micromatch: 4.0.5
      pidtree: 0.6.0
      string-argv: 0.3.2
      yaml: 2.3.1
    transitivePeerDependencies:
      - enquirer
      - supports-color
    dev: true

  /listr2/6.6.1:
    resolution: {integrity: sha512-+rAXGHh0fkEWdXBmX+L6mmfmXmXvDGEKzkjxO+8mP3+nI/r/CWznVBvsibXdxda9Zz0OW2e2ikphN3OwCT/jSg==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      enquirer: '>= 2.3.0 < 3'
    peerDependenciesMeta:
      enquirer:
        optional: true
    dependencies:
      cli-truncate: 3.1.0
      colorette: 2.0.20
      eventemitter3: 5.0.1
      log-update: 5.0.1
      rfdc: 1.4.1
      wrap-ansi: 8.1.0
    dev: true

  /load-bmfont/1.4.2_debug@4.4.1:
    resolution: {integrity: sha512-qElWkmjW9Oq1F9EI5Gt7aD9zcdHb9spJCW1L/dmPf7KzCCEJxq8nhHz5eCgI9aMf7vrG/wyaCqdsI+Iy9ZTlog==}
    dependencies:
      buffer-equal: 0.0.1
      mime: 1.6.0
      parse-bmfont-ascii: 1.0.6
      parse-bmfont-binary: 1.0.6
      parse-bmfont-xml: 1.1.6
      phin: 3.7.1_debug@4.4.1
      xhr: 2.6.0
      xtend: 4.0.2
    transitivePeerDependencies:
      - debug
    dev: true

  /loader-runner/2.4.0:
    resolution: {integrity: sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw==}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dev: false

  /loader-runner/4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}
    dev: true

  /loader-utils/1.4.2:
    resolution: {integrity: sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==}
    engines: {node: '>=4.0.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 1.0.2

  /loader-utils/2.0.4:
    resolution: {integrity: sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==}
    engines: {node: '>=8.9.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3

  /locate-path/3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==}
    engines: {node: '>=6'}
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0
    dev: false

  /locate-path/5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}
    dependencies:
      p-locate: 4.1.0

  /lodash.debounce/4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}

  /lodash.defaultsdeep/4.6.1:
    resolution: {integrity: sha512-3j8wdDzYuWO3lM3Reg03MuQR957t287Rpcxp1njpEa8oDrikb+FwGdW3n+FELh/A6qib6yPit0j/pv9G/yeAqA==}
    dev: true

  /lodash.kebabcase/4.1.1:
    resolution: {integrity: sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==}
    dev: true

  /lodash.mapvalues/4.6.0:
    resolution: {integrity: sha512-JPFqXFeZQ7BfS00H58kClY7SPVeHertPE0lNuCyZ26/XlN8TvakYD7b9bGyNmXbT/D3BbtPAAmq90gPWqLkxlQ==}
    dev: true

  /lodash.memoize/4.1.2:
    resolution: {integrity: sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==}
    dev: true

  /lodash.sortby/4.7.0:
    resolution: {integrity: sha512-HDWXG8isMntAyRF5vZ7xKuEvOhT4AhlRt/3czTSjvGUxjYCBVRQY48ViDHyfYz9VIoBkW4TMGQNapx+l3RUwdA==}
    dev: true

  /lodash.uniq/4.5.0:
    resolution: {integrity: sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==}
    dev: true

  /lodash/4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    dev: true

  /log-symbols/4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==}
    engines: {node: '>=10'}
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0
    dev: true

  /log-update/2.3.0:
    resolution: {integrity: sha512-vlP11XfFGyeNQlmEn9tJ66rEW1coA/79m5z6BCkudjbAGE83uhAcGYrBFwfs3AdLiLzGRusRPAbSPK9xZteCmg==}
    engines: {node: '>=4'}
    dependencies:
      ansi-escapes: 3.2.0
      cli-cursor: 2.1.0
      wrap-ansi: 3.0.1
    dev: true

  /log-update/5.0.1:
    resolution: {integrity: sha512-5UtUDQ/6edw4ofyljDNcOVJQ4c7OjDro4h3y8e1GQL5iYElYclVHJ3zeWchylvMaKnDbDilC8irOVyexnA/Slw==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      ansi-escapes: 5.0.0
      cli-cursor: 4.0.0
      slice-ansi: 5.0.0
      strip-ansi: 7.1.0
      wrap-ansi: 8.1.0
    dev: true

  /lolex/5.1.2:
    resolution: {integrity: sha512-h4hmjAvHTmd+25JSwrtTIuwbKdwg5NzZVRMLn9saij4SZaepCrTCxPr35H/3bjwfMJtN+t3CX8672UIkglz28A==}
    dependencies:
      '@sinonjs/commons': 1.8.6
    dev: true

  /lower-case/2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==}
    dependencies:
      tslib: 2.8.1
    dev: true

  /lru-cache/4.1.5:
    resolution: {integrity: sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==}
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2

  /lru-cache/5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}
    dependencies:
      yallist: 3.1.1

  /lru-cache/6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /magic-string/0.30.17:
    resolution: {integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==}
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0
    dev: true

  /make-dir/2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    dev: false

  /make-dir/3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}
    dependencies:
      semver: 6.3.1

  /make-dir/4.0.0:
    resolution: {integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==}
    engines: {node: '>=10'}
    dependencies:
      semver: 7.7.2
    dev: true

  /make-plural/4.3.0:
    resolution: {integrity: sha512-xTYd4JVHpSCW+aqDof6w/MebaMVNTVYBZhbB/vi513xXdiPT92JMVCo0Jq8W2UZnzYRFeVbQiQ+I25l13JuKvA==}
    hasBin: true
    optionalDependencies:
      minimist: 1.2.8
    dev: true

  /make-plural/7.4.0:
    resolution: {integrity: sha512-4/gC9KVNTV6pvYg2gFeQYTW3mWaoJt7WZE5vrp1KnQDgW92JtYZnzmZT81oj/dUTqAIu0ufI2x3dkgu3bB1tYg==}
    dev: true

  /makeerror/1.0.12:
    resolution: {integrity: sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==}
    dependencies:
      tmpl: 1.0.5
    dev: true

  /map-cache/0.2.2:
    resolution: {integrity: sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg==}
    engines: {node: '>=0.10.0'}

  /map-visit/1.0.0:
    resolution: {integrity: sha512-4y7uGv8bd2WdM9vpQsiQNo41Ln1NvhvDRuVt0k2JZQ+ezN2uaQes7lZeZ+QQUHOLQAtDaBJ+7wCbi+ab/KFs+w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-visit: 1.0.1

  /math-interval-parser/2.0.1:
    resolution: {integrity: sha512-VmlAmb0UJwlvMyx8iPhXUDnVW1F9IrGEd9CIOmv+XL8AErCUUuozoDMrgImvnYt2A+53qVX/tPW6YJurMKYsvA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /math-intrinsics/1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  /md5.js/1.3.5:
    resolution: {integrity: sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg==}
    dependencies:
      hash-base: 3.0.5
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /md5/2.3.0:
    resolution: {integrity: sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==}
    dependencies:
      charenc: 0.0.2
      crypt: 0.0.2
      is-buffer: 1.1.6
    dev: false

  /mdn-data/2.0.14:
    resolution: {integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==}
    dev: true

  /media-typer/0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==}
    engines: {node: '>= 0.6'}

  /memfs/3.5.3:
    resolution: {integrity: sha512-UERzLsxzllchadvbPs5aolHh65ISpKpM+ccLbOJ8/vvpBKmAWf+la7dXFy7Mr0ySHbdHrFv5kGFCUHHe6GFEmw==}
    engines: {node: '>= 4.0.0'}
    dependencies:
      fs-monkey: 1.0.6
    dev: true

  /memory-fs/0.4.1:
    resolution: {integrity: sha512-cda4JKCxReDXFXRqOHPQscuIYg1PvxbE2S2GP45rnwfEK+vZaXC8C1OFvdHIbgw0DLzowXGVoxLaAmlgRy14GQ==}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.8
    dev: false

  /memory-fs/0.5.0:
    resolution: {integrity: sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA==}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.8
    dev: false

  /merge-descriptors/1.0.3:
    resolution: {integrity: sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==}
    dev: true

  /merge-source-map/1.1.0:
    resolution: {integrity: sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==}
    dependencies:
      source-map: 0.6.1

  /merge-stream/2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    dev: true

  /merge/2.1.1:
    resolution: {integrity: sha512-jz+Cfrg9GWOZbQAnDQ4hlVnQky+341Yk5ru8bZSe6sIDTCIg8n9i/u7hSQGSVOF3C7lH6mGtqjkiT9G4wFLL0w==}
    dev: true

  /merge2/1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}
    dev: true

  /messageformat-formatters/2.0.1:
    resolution: {integrity: sha512-E/lQRXhtHwGuiQjI7qxkLp8AHbMD5r2217XNe/SREbBlSawe0lOqsFb7rflZJmlQFSULNLIqlcjjsCPlB3m3Mg==}
    dev: true

  /messageformat-parser/4.1.3:
    resolution: {integrity: sha512-2fU3XDCanRqeOCkn7R5zW5VQHWf+T3hH65SzuqRvjatBK7r4uyFa5mEX+k6F9Bd04LVM5G4/BHBTUJsOdW7uyg==}
    dev: true

  /messageformat/2.3.0:
    resolution: {integrity: sha512-uTzvsv0lTeQxYI2y1NPa1lItL5VRI8Gb93Y2K2ue5gBPyrbJxfDi/EYWxh2PKv5yO42AJeeqblS9MJSh/IEk4w==}
    deprecated: Package renamed as '@messageformat/core', see messageformat.github.io for more details. 'messageformat@4' will eventually provide a polyfill for Intl.MessageFormat, once it's been defined by Unicode & ECMA.
    dependencies:
      make-plural: 4.3.0
      messageformat-formatters: 2.0.1
      messageformat-parser: 4.1.3
    dev: true

  /methods/1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==}
    engines: {node: '>= 0.6'}

  /micromatch/3.1.10:
    resolution: {integrity: sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 6.0.3
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2

  /micromatch/4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1
    dev: true

  /micromatch/4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1
    dev: true

  /miller-rabin/4.0.1:
    resolution: {integrity: sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==}
    hasBin: true
    dependencies:
      bn.js: 4.12.2
      brorand: 1.1.0
    dev: false

  /mime-db/1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  /mime-db/1.54.0:
    resolution: {integrity: sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==}
    engines: {node: '>= 0.6'}
    dev: true

  /mime-types/2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0

  /mime/1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /mime/2.6.0:
    resolution: {integrity: sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==}
    engines: {node: '>=4.0.0'}
    hasBin: true
    dev: false

  /mimic-fn/1.2.0:
    resolution: {integrity: sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==}
    engines: {node: '>=4'}
    dev: true

  /mimic-fn/2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}
    dev: true

  /mimic-fn/4.0.0:
    resolution: {integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==}
    engines: {node: '>=12'}
    dev: true

  /min-document/2.19.0:
    resolution: {integrity: sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ==}
    dependencies:
      dom-walk: 0.1.2
    dev: true

  /mini-css-extract-plugin/0.5.0:
    resolution: {integrity: sha512-IuaLjruM0vMKhUUT51fQdQzBYTX49dLj8w68ALEAe2A4iYNpIC4eMac67mt3NzycvjOlf07/kYxJDc0RTl1Wqw==}
    engines: {node: '>= 6.9.0 <7.0.0 || >= 8.9.0'}
    peerDependencies:
      webpack: ^4.4.0
    dependencies:
      loader-utils: 1.4.2
      schema-utils: 1.0.0
      webpack-sources: 1.4.3
    dev: false

  /mini-css-extract-plugin/2.9.2_webpack@5.99.8:
    resolution: {integrity: sha512-GJuACcS//jtq4kCtd5ii/M0SZf7OZRH+BxdqXZHaJfb8TJiVl+NgQRPwiYt2EuqeSkNydn/7vP+bcE27C5mb9w==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0
    dependencies:
      schema-utils: 4.3.2
      tapable: 2.2.1
      webpack: 5.99.8
    dev: true

  /mini-html-parser2/0.3.0:
    resolution: {integrity: sha512-W4x1MCmtlnAH5M9qQ1WbRn+hTvv7bdrJx4VI+6SD0MUZatW/6K7v213Aidx7VDQmSKoRv+iAn5TswJnesOs71Q==}
    dependencies:
      domhandler: 2.4.2
      entities: 1.1.2
      events: 3.3.0
    dev: true

  /minimalistic-assert/1.0.1:
    resolution: {integrity: sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==}

  /minimalistic-crypto-utils/1.0.1:
    resolution: {integrity: sha512-JIYlbt6g8i5jKfJ3xz7rF0LXmv2TkDxBLUkiBeZ7bAx4GnnNMr8xFpGnOxn6GhTEHx3SjRrZEoU+j04prX1ktg==}
    dev: false

  /minimatch/3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.11

  /minimist/1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  /minipass/3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==}
    engines: {node: '>=8'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /mississippi/3.0.0:
    resolution: {integrity: sha512-x471SsVjUtBRtcvd4BzKE9kFC+/2TeWgKCgw0bZcw1b9l2X3QX5vCWgF+KaZaYm87Ss//rHnWryupDrgLvmSkA==}
    engines: {node: '>=4.0.0'}
    dependencies:
      concat-stream: 1.6.2
      duplexify: 3.7.1
      end-of-stream: 1.4.4
      flush-write-stream: 1.1.1
      from2: 2.3.0
      parallel-transform: 1.2.0
      pump: 3.0.2
      pumpify: 1.5.1
      stream-each: 1.2.3
      through2: 2.0.5
    dev: false

  /mixin-deep/1.3.2:
    resolution: {integrity: sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: 1.0.2
      is-extendable: 1.0.1

  /mkdirp/0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true
    dependencies:
      minimist: 1.2.8

  /module-alias/2.2.3:
    resolution: {integrity: sha512-23g5BFj4zdQL/b6tor7Ji+QY4pEfNH784BMslY9Qb0UnJWRAt+lQGLYmRaM0KDBwIG23ffEBELhZDP2rhi9f/Q==}

  /moment/2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==}
    dev: false

  /move-concurrently/1.0.1:
    resolution: {integrity: sha512-hdrFxZOycD/g6A6SoI2bB5NA/5NEqD0569+S47WZhPvm46sD50ZHdYaFmnua5lndde9rCHGjmfK7Z8BuCt/PcQ==}
    deprecated: This package is no longer supported.
    dependencies:
      aproba: 1.2.0
      copy-concurrently: 1.0.5
      fs-write-stream-atomic: 1.0.10
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: false

  /mrmime/2.0.1:
    resolution: {integrity: sha512-Y3wQdFg2Va6etvQ5I82yUhGdsKrcYox6p7FfL1LbK2J4V01F9TGlepTIhnK24t7koZibmg82KGglhA1XK5IsLQ==}
    engines: {node: '>=10'}
    dev: true

  /ms/2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  /ms/2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}
    dev: true

  /ms/2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  /multicast-dns/7.2.5:
    resolution: {integrity: sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg==}
    hasBin: true
    dependencies:
      dns-packet: 5.6.1
      thunky: 1.1.0
    dev: true

  /mustache/3.2.1:
    resolution: {integrity: sha512-RERvMFdLpaFfSRIEe632yDm5nsd0SDKn8hGmcUwswnyiE5mtdZLDybtHAz6hjJhawokF0hXvGLtx9mrQfm6FkA==}
    engines: {npm: '>=1.4.0'}
    hasBin: true
    dev: true

  /mustache/4.2.0:
    resolution: {integrity: sha512-71ippSywq5Yb7/tVYyGbkBggbU8H3u5Rz56fH60jGFgr8uHwxs+aSKeqmluIVzM0m0kB7xQjKS6qPfd0b2ZoqQ==}
    hasBin: true
    dev: true

  /mz/2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0
    dev: true

  /nan/2.22.2:
    resolution: {integrity: sha512-DANghxFkS1plDdRsX0X9pm0Z6SJNN6gBdtXfanwoZ8hooC5gosGFSBGRYHUVPz1asKA/kMRqDRdHrluZ61SpBQ==}
    requiresBuild: true
    dev: false
    optional: true

  /nanoid/3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: true

  /nanomatch/1.2.13:
    resolution: {integrity: sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      fragment-cache: 0.2.1
      is-windows: 1.0.2
      kind-of: 6.0.3
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2

  /natural-compare/1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}
    dev: true

  /negotiator/0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  /negotiator/0.6.4:
    resolution: {integrity: sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w==}
    engines: {node: '>= 0.6'}
    dev: true

  /neo-async/2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}

  /nice-try/1.0.5:
    resolution: {integrity: sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==}
    dev: true

  /no-case/3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==}
    dependencies:
      lower-case: 2.0.2
      tslib: 2.8.1
    dev: true

  /node-addon-api/7.1.1:
    resolution: {integrity: sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==}
    dev: true
    optional: true

  /node-fetch/2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true
    dependencies:
      whatwg-url: 5.0.0
    dev: true

  /node-forge/1.3.1:
    resolution: {integrity: sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==}
    engines: {node: '>= 6.13.0'}
    dev: true

  /node-int64/0.4.0:
    resolution: {integrity: sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==}
    dev: true

  /node-libs-browser/2.2.1:
    resolution: {integrity: sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q==}
    dependencies:
      assert: 1.5.1
      browserify-zlib: 0.2.0
      buffer: 4.9.2
      console-browserify: 1.2.0
      constants-browserify: 1.0.0
      crypto-browserify: 3.12.1
      domain-browser: 1.2.0
      events: 3.3.0
      https-browserify: 1.0.0
      os-browserify: 0.3.0
      path-browserify: 0.0.1
      process: 0.11.10
      punycode: 1.4.1
      querystring-es3: 0.2.1
      readable-stream: 2.3.8
      stream-browserify: 2.0.2
      stream-http: 2.8.3
      string_decoder: 1.3.0
      timers-browserify: 2.0.12
      tty-browserify: 0.0.0
      url: 0.11.4
      util: 0.11.1
      vm-browserify: 1.1.2
    dev: false

  /node-notifier/6.0.0:
    resolution: {integrity: sha512-SVfQ/wMw+DesunOm5cKqr6yDcvUTDl/yc97ybGHMrteNEY6oekXpNpS3lZwgLlwz0FLgHoiW28ZpmBHUDg37cw==}
    requiresBuild: true
    dependencies:
      growly: 1.3.0
      is-wsl: 2.2.0
      semver: 6.3.1
      shellwords: 0.1.1
      which: 1.3.1
    dev: true
    optional: true

  /node-releases/2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  /normalize-package-data/2.5.0:
    resolution: {integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==}
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.10
      semver: 5.7.2
      validate-npm-package-license: 3.0.4
    dev: true

  /normalize-path/2.1.1:
    resolution: {integrity: sha512-3pKJwH184Xo/lnH6oyP1q2pMd7HcypqqmRs91/6/i2CGtWwIKGCkOOMTm/zXbgTEWHw1uNpNi/igc3ePOYHb6w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      remove-trailing-separator: 1.1.0

  /normalize-path/3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  /normalize-range/0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}

  /normalize-url/6.1.0:
    resolution: {integrity: sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==}
    engines: {node: '>=10'}
    dev: true

  /npm-run-path/2.0.2:
    resolution: {integrity: sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw==}
    engines: {node: '>=4'}
    dependencies:
      path-key: 2.0.1
    dev: true

  /npm-run-path/4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1
    dev: true

  /npm-run-path/5.3.0:
    resolution: {integrity: sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      path-key: 4.0.0
    dev: true

  /nth-check/2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}
    dependencies:
      boolbase: 1.0.0
    dev: true

  /num2fraction/1.2.2:
    resolution: {integrity: sha512-Y1wZESM7VUThYY+4W+X4ySH2maqcA+p7UR+w8VWNWVAd6lwuXXWz/w/Cz43J/dI2I+PS6wD5N+bJUF+gjWvIqg==}
    dev: false

  /nwsapi/2.2.20:
    resolution: {integrity: sha512-/ieB+mDe4MrrKMT8z+mQL8klXydZWGR5Dowt4RAGKbJ3kIGEx3X4ljUo+6V73IXtUPWgfOlU5B9MlGxFO5T+cA==}
    dev: true

  /oauth-sign/0.9.0:
    resolution: {integrity: sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==}

  /object-assign/4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /object-copy/0.1.0:
    resolution: {integrity: sha512-79LYn6VAb63zgtmAteVOWo9Vdj71ZVBy3Pbse+VqxDpEP83XuujMrGqHIwAXJ5I/aM0zU7dIyIAhifVTPrNItQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      copy-descriptor: 0.1.1
      define-property: 0.2.5
      kind-of: 3.2.2

  /object-inspect/1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==}
    engines: {node: '>= 0.4'}

  /object-keys/1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  /object-visit/1.0.1:
    resolution: {integrity: sha512-GBaMwwAVK9qbQN3Scdo0OyvgPW7l3lnaVMj84uTOZlswkX0KpF6fyDBJhtTthf7pymztoN36/KEr1DyhF96zEA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1

  /object.assign/4.1.7:
    resolution: {integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  /object.pick/1.3.0:
    resolution: {integrity: sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1

  /obuf/1.1.2:
    resolution: {integrity: sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==}
    dev: true

  /omggif/1.0.10:
    resolution: {integrity: sha512-LMJTtvgc/nugXj0Vcrrs68Mn2D1r0zf630VNtqtpI1FEO7e+O9FP4gqs9AcnBaSEeoHIPm28u6qgPR0oyEpGSw==}
    dev: true

  /on-finished/2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1

  /on-headers/1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}
    dev: true

  /once/1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}
    dependencies:
      wrappy: 1.0.2

  /onetime/2.0.1:
    resolution: {integrity: sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ==}
    engines: {node: '>=4'}
    dependencies:
      mimic-fn: 1.2.0
    dev: true

  /onetime/5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: true

  /onetime/6.0.0:
    resolution: {integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==}
    engines: {node: '>=12'}
    dependencies:
      mimic-fn: 4.0.0
    dev: true

  /only/0.0.2:
    resolution: {integrity: sha512-Fvw+Jemq5fjjyWz6CpKx6w9s7xxqo3+JCyM0WXWeCSOboZ8ABkyvP8ID4CZuChA/wxSx+XSJmdOm8rGVyJ1hdQ==}
    dev: false

  /open/8.4.2:
    resolution: {integrity: sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==}
    engines: {node: '>=12'}
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0
    dev: true

  /opener/1.5.2:
    resolution: {integrity: sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==}
    hasBin: true
    dev: true

  /opn/5.5.0:
    resolution: {integrity: sha512-PqHpggC9bLV0VeWcdKhkpxY+3JTzetLSqTCWL/z/tFIbI6G8JCjondXklT1JinczLz2Xib62sSp0T/gKT4KksA==}
    engines: {node: '>=4'}
    dependencies:
      is-wsl: 1.1.0
    dev: false

  /optionator/0.8.3:
    resolution: {integrity: sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.5

  /ora/5.4.1:
    resolution: {integrity: sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==}
    engines: {node: '>=10'}
    dependencies:
      bl: 4.1.0
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-spinners: 2.9.2
      is-interactive: 1.0.0
      is-unicode-supported: 0.1.0
      log-symbols: 4.1.0
      strip-ansi: 6.0.1
      wcwidth: 1.0.1
    dev: true

  /os-browserify/0.3.0:
    resolution: {integrity: sha512-gjcpUc3clBf9+210TRaDWbf+rZZZEshZ+DlXMRCeAjp0xhTrnQsKHypIy1J3d5hKdUzj69t708EHtU8P6bUn0A==}
    dev: false

  /os-locale-s-fix/1.0.8-fix-1:
    resolution: {integrity: sha512-Sv0OvhPiMutICiwORAUefv02DCPb62IelBmo8ZsSrRHyI3FStqIWZvjqDkvtjU+lcujo7UNir+dCwKSqlEQ/5w==}
    engines: {node: '>=10', yarn: ^1.22.4}
    dependencies:
      lcid: 3.1.1
    dev: true

  /p-each-series/2.2.0:
    resolution: {integrity: sha512-ycIL2+1V32th+8scbpTvyHNaHe02z0sjgh91XXjAk+ZeXoPN4Z46DVUnzdso0aX4KckKw0FNNFHdjZ2UsZvxiA==}
    engines: {node: '>=8'}
    dev: true

  /p-finally/1.0.0:
    resolution: {integrity: sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==}
    engines: {node: '>=4'}
    dev: true

  /p-finally/2.0.1:
    resolution: {integrity: sha512-vpm09aKwq6H9phqRQzecoDpD8TmVyGw70qmWlyq5onxY7tqyTTFVvxMykxQSQKILBSFlbXpypIw2T1Ml7+DDtw==}
    engines: {node: '>=8'}
    dev: true

  /p-limit/2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0

  /p-locate/3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==}
    engines: {node: '>=6'}
    dependencies:
      p-limit: 2.3.0
    dev: false

  /p-locate/4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}
    dependencies:
      p-limit: 2.3.0

  /p-retry/4.6.2:
    resolution: {integrity: sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==}
    engines: {node: '>=8'}
    dependencies:
      '@types/retry': 0.12.0
      retry: 0.13.1
    dev: true

  /p-try/2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  /pako/1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}

  /parallel-transform/1.2.0:
    resolution: {integrity: sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg==}
    dependencies:
      cyclist: 1.0.2
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /param-case/3.0.4:
    resolution: {integrity: sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==}
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1
    dev: true

  /parent-module/1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0

  /parse-asn1/5.1.7:
    resolution: {integrity: sha512-CTM5kuWR3sx9IFamcl5ErfPl6ea/N8IYwiJ+vpeB2g+1iknv7zBl5uPwbMbRVznRVbrNY6lGuDoE5b30grmbqg==}
    engines: {node: '>= 0.10'}
    dependencies:
      asn1.js: 4.10.1
      browserify-aes: 1.2.0
      evp_bytestokey: 1.0.3
      hash-base: 3.0.5
      pbkdf2: 3.1.2
      safe-buffer: 5.2.1
    dev: false

  /parse-bmfont-ascii/1.0.6:
    resolution: {integrity: sha512-U4RrVsUFCleIOBsIGYOMKjn9PavsGOXxbvYGtMOEfnId0SVNsgehXh1DxUdVPLoxd5mvcEtvmKs2Mmf0Mpa1ZA==}
    dev: true

  /parse-bmfont-binary/1.0.6:
    resolution: {integrity: sha512-GxmsRea0wdGdYthjuUeWTMWPqm2+FAd4GI8vCvhgJsFnoGhTrLhXDDupwTo7rXVAgaLIGoVHDZS9p/5XbSqeWA==}
    dev: true

  /parse-bmfont-xml/1.1.6:
    resolution: {integrity: sha512-0cEliVMZEhrFDwMh4SxIyVJpqYoOWDJ9P895tFuS+XuNzI5UBmBk5U5O4KuJdTnZpSBI4LFA2+ZiJaiwfSwlMA==}
    dependencies:
      xml-parse-from-string: 1.0.1
      xml2js: 0.5.0
    dev: true

  /parse-headers/2.0.6:
    resolution: {integrity: sha512-Tz11t3uKztEW5FEVZnj1ox8GKblWn+PvHY9TmJV5Mll2uHEwRdR/5Li1OlXoECjLYkApdhWy44ocONwXLiKO5A==}
    dev: true

  /parse-json/5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  /parse5-htmlparser2-tree-adapter/6.0.1:
    resolution: {integrity: sha512-qPuWvbLgvDGilKc5BoicRovlT4MtYT6JfJyBOMDsKoiT+GiuP5qyrPCnR9HcPECIJJmZh5jRndyNThnhhb/vlA==}
    dependencies:
      parse5: 6.0.1
    dev: true

  /parse5/3.0.3:
    resolution: {integrity: sha512-rgO9Zg5LLLkfJF9E6CCmXlSE4UVceloys8JrFqCcHloC3usd/kJCyPDwH2SOlzix2j3xaP9sUX3e8+kvkuleAA==}
    dependencies:
      '@types/node': 22.15.17
    dev: false

  /parse5/5.1.0:
    resolution: {integrity: sha512-fxNG2sQjHvlVAYmzBZS9YlDp6PTSSDwa98vkD4QgVDDCAo84z5X1t5XyJQ62ImdLXx5NdIIfihey6xpum9/gRQ==}
    dev: true

  /parse5/5.1.1:
    resolution: {integrity: sha512-ugq4DFI0Ptb+WWjAdOK16+u/nHfiIrcE+sh8kZMaM0WllQKLI9rOUq6c2b7cwPkXdzfQESqvoqK6ug7U/Yyzug==}
    dev: true

  /parse5/6.0.1:
    resolution: {integrity: sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==}
    dev: true

  /parseqs/0.0.6:
    resolution: {integrity: sha512-jeAGzMDbfSHHA091hr0r31eYfTig+29g3GKKE/PPbEQ65X0lmMwlEoqmhzu0iztID5uJpZsFlUPDP8ThPL7M8w==}
    dev: false

  /parseuri/0.0.6:
    resolution: {integrity: sha512-AUjen8sAkGgao7UyCX6Ahv0gIK2fABKmYjvP4xmy5JaKvcbTRueIqIPHLAfq30xJddqSE033IOMUSOMCcK3Sow==}
    dev: false

  /parseurl/1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  /pascal-case/3.1.2:
    resolution: {integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1
    dev: true

  /pascalcase/0.1.1:
    resolution: {integrity: sha512-XHXfu/yOQRy9vYOtUDVMN60OEJjW013GoObG1o+xwQTpB9eYJX/BjXMsdW13ZDPruFhYYn0AG22w0xgQMwl3Nw==}
    engines: {node: '>=0.10.0'}

  /path-browserify/0.0.1:
    resolution: {integrity: sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ==}
    dev: false

  /path-dirname/1.0.2:
    resolution: {integrity: sha512-ALzNPpyNq9AqXMBjeymIjFDAkAFH06mHJH/cSBHAgU0s4vfpBn6b2nf8tiRLvagKD8RbTpq2FKTBg7cl9l3c7Q==}
    dev: false
    optional: true

  /path-exists/3.0.0:
    resolution: {integrity: sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==}
    engines: {node: '>=4'}
    dev: false

  /path-exists/4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  /path-is-absolute/1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  /path-key/2.0.1:
    resolution: {integrity: sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==}
    engines: {node: '>=4'}
    dev: true

  /path-key/3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}
    dev: true

  /path-key/4.0.0:
    resolution: {integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==}
    engines: {node: '>=12'}
    dev: true

  /path-parse/1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  /path-to-regexp/0.1.12:
    resolution: {integrity: sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ==}
    dev: true

  /path-to-regexp/1.9.0:
    resolution: {integrity: sha512-xIp7/apCFJuUHdDLWe8O1HIkb0kQrOMb/0u6FXQjemHn/ii5LrIzU6bdECnsiTF/GjZkMEKg1xdiZwNqDYlZ6g==}
    dependencies:
      isarray: 0.0.1
    dev: false

  /path-type/4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}
    dev: true

  /pbkdf2/3.1.2:
    resolution: {integrity: sha512-iuh7L6jA7JEGu2WxDwtQP1ddOpaJNC4KlDEFfdQajSGgGPNi4OyDc2R7QnbY2bR9QjBVGwgvTdNJZoE7RaxUMA==}
    engines: {node: '>=0.12'}
    dependencies:
      create-hash: 1.2.0
      create-hmac: 1.1.7
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: false

  /performance-now/2.1.0:
    resolution: {integrity: sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==}

  /phin/2.9.3:
    resolution: {integrity: sha512-CzFr90qM24ju5f88quFC/6qohjC144rehe5n6DH900lgXmUe86+xCKc10ev56gRKC4/BkHUoG4uSiQgBiIXwDA==}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.
    dev: true

  /phin/3.7.1_debug@4.4.1:
    resolution: {integrity: sha512-GEazpTWwTZaEQ9RhL7Nyz0WwqilbqgLahDM3D0hxWwmVDI52nXEybHqiN6/elwpkJBhcuj+WbBu+QfT0uhPGfQ==}
    engines: {node: '>= 8'}
    dependencies:
      centra: 2.7.0_debug@4.4.1
    transitivePeerDependencies:
      - debug
    dev: true

  /picocolors/0.2.1:
    resolution: {integrity: sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==}

  /picocolors/1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  /picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  /pidtree/0.6.0:
    resolution: {integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==}
    engines: {node: '>=0.10'}
    hasBin: true
    dev: true

  /pify/2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}
    dev: true

  /pify/4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}
    dev: false

  /pirates/4.0.7:
    resolution: {integrity: sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==}
    engines: {node: '>= 6'}

  /pixelmatch/4.0.2:
    resolution: {integrity: sha512-J8B6xqiO37sU/gkcMglv6h5Jbd9xNER7aHzpfRdNmV4IbQBzBpe4l9XmbG+xPF/znacgu2jfEw+wHffaq/YkXA==}
    hasBin: true
    dependencies:
      pngjs: 3.4.0
    dev: true

  /pkg-dir/3.0.0:
    resolution: {integrity: sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==}
    engines: {node: '>=6'}
    dependencies:
      find-up: 3.0.0
    dev: false

  /pkg-dir/4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0

  /pn/1.1.0:
    resolution: {integrity: sha512-2qHaIQr2VLRFoxe2nASzsV6ef4yOOH+Fi9FBOVH6cqeSgUnoyySPZkxzLuzd+RYOQTRpROA0ztTMqxROKSb/nA==}
    dev: true

  /pngjs/3.4.0:
    resolution: {integrity: sha512-NCrCHhWmnQklfH4MtJMRjZ2a8c80qXeMlQMv2uVp9ISJMTt562SbGd6n2oq0PaPgKm7Z6pL9E2UlLIhC+SHL3w==}
    engines: {node: '>=4.0.0'}
    dev: true

  /portfinder/1.0.37:
    resolution: {integrity: sha512-yuGIEjDAYnnOex9ddMnKZEMFE0CcGo6zbfzDklkmT1m5z734ss6JMzN9rNB3+RR7iS+F10D4/BVIaXOyh8PQKw==}
    engines: {node: '>= 10.12'}
    dependencies:
      async: 3.2.6
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  /posix-character-classes/0.1.1:
    resolution: {integrity: sha512-xTgYBc3fuo7Yt7JbiuFxSYGToMoz8fLoE6TC9Wx1P/u+LfeThMOAqmuyECnlBaaJb+u1m9hHiXUEtwW4OzfUJg==}
    engines: {node: '>=0.10.0'}

  /postcss-calc/8.2.4_postcss@8.5.3:
    resolution: {integrity: sha512-SmWMSJmB8MRnnULldx0lQIyhSNvuDl9HfrZkaqqE/WHAhToYsAvDq+yAsA/kIyINDszOp3Rh0GFoNuH5Ypsm3Q==}
    peerDependencies:
      postcss: ^8.2.2
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-colormin/5.3.1_postcss@8.5.3:
    resolution: {integrity: sha512-UsWQG0AqTFQmpBegeLLc1+c3jIqBNB0zlDGRWR+dQ3pRKJL1oeMzyqmH3o2PIfn9MBdNrVPWhDbT769LxCTLJQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.24.5
      caniuse-api: 3.0.0
      colord: 2.9.3
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-comment/2.0.0:
    resolution: {integrity: sha512-5zT5iKU7c0tK9KJFNrVf+g1MGTkzf/4V3e0Zzm2g1uoFQC5jeTHmB9O1iAqh97+jnKpc6al204e0pwFUiCwseg==}
    dependencies:
      postcss: 6.0.23
    dev: true

  /postcss-convert-values/5.1.3_postcss@8.5.3:
    resolution: {integrity: sha512-82pC1xkJZtcJEfiLw6UXnXVXScgtBrjlO5CBmuDQc+dlb88ZYheFsjTn40+zBVi3DkfF7iezO0nJUPLcJK3pvA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.24.5
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-discard-comments/5.1.2_postcss@8.5.3:
    resolution: {integrity: sha512-+L8208OVbHVF2UQf1iDmRcbdjJkuBF6IS29yBDSiWUIzpYaAhtNl6JYnYm12FnkeCwQqF5LeklOu6rAqgfBZqQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
    dev: true

  /postcss-discard-duplicates/5.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-zmX3IoSI2aoenxHV6C7plngHWWhUOV3sP1T8y2ifzxzbtnuhk1EdPwm0S1bIUNaJ2eNbWeGLEwzw8huPD67aQw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
    dev: true

  /postcss-discard-empty/5.1.1_postcss@8.5.3:
    resolution: {integrity: sha512-zPz4WljiSuLWsI0ir4Mcnr4qQQ5e1Ukc3i7UfE2XcrwKK2LIPIqE5jxMRxO6GbI3cv//ztXDsXwEWT3BHOGh3A==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
    dev: true

  /postcss-discard-overridden/5.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-21nOL7RqWR1kasIVdKs8HNqQJhFxLsyRfAnUDm4Fe4t4mCWL9OJiHvlHPjcd8zc5Myu89b/7wZDnOSjFgeWRtw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
    dev: true

  /postcss-helpers/0.3.3:
    resolution: {integrity: sha512-VumiUcrpbxGlTBNQj6fUOkb/HNRUk/xYz8bNlhgVOdvk3yWEy4B+0nlDUZZM9mTVZ5bJoxUy7WT6z/4E7oMTgw==}
    engines: {node: '>=0.12.9'}
    dependencies:
      urijs: 1.19.11

  /postcss-import/12.0.1:
    resolution: {integrity: sha512-3Gti33dmCjyKBgimqGxL3vcV8w9+bsHwO5UrBawp796+jdardbcFl4RP5w/76BwNL7aGzpKstIfF9I+kdE8pTw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      read-cache: 1.0.0
      resolve: 1.22.10
    dev: true

  /postcss-loader/6.2.1_qhv7z3lwctzesdbw7aunk53kba:
    resolution: {integrity: sha512-WbbYpmAaKcux/P66bZ40bpWsBucjx/TTgVVzRZ9yUO8yQfVBlameJ0ZGVaPfH64hNSBh63a+ICP5nqOpBA0w+Q==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      postcss: ^7.0.0 || ^8.0.1
      webpack: ^5.0.0
    dependencies:
      cosmiconfig: 7.1.0
      klona: 2.0.6
      postcss: 8.5.3
      semver: 7.7.2
      webpack: 5.99.8
    dev: true

  /postcss-loader/8.1.1:
    resolution: {integrity: sha512-0IeqyAsG6tYiDRCYKQJLAmgQr47DX6N7sFSWvQxt6AcupX8DIdmykuk/o/tx0Lze3ErGHJEp5OSRxrelC6+NdQ==}
    engines: {node: '>= 18.12.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      postcss: ^7.0.0 || ^8.0.1
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true
    dependencies:
      cosmiconfig: 9.0.0
      jiti: 1.21.7
      semver: 7.7.2
    transitivePeerDependencies:
      - typescript
    dev: false

  /postcss-merge-longhand/5.1.7_postcss@8.5.3:
    resolution: {integrity: sha512-YCI9gZB+PLNskrK0BB3/2OzPnGhPkBEwmwhfYk1ilBHYVAZB7/tkTHFBAnCrvBBOmeYyMYw3DMjT55SyxMBzjQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
      stylehacks: 5.1.1_postcss@8.5.3
    dev: true

  /postcss-merge-rules/5.1.4_postcss@8.5.3:
    resolution: {integrity: sha512-0R2IuYpgU93y9lhVbO/OylTtKMVcHb67zjWIfCiKR9rWL3GUk1677LAqD/BcHizukdZEjT8Ru3oHRoAYoJy44g==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.24.5
      caniuse-api: 3.0.0
      cssnano-utils: 3.1.0_postcss@8.5.3
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2
    dev: true

  /postcss-minify-font-values/5.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-el3mYTgx13ZAPPirSVsHqFzl+BBBDrXvbySvPGFnQcTI4iNslrPaFq4muTkLZmKlGk4gyFAYUBMH30+HurREyA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-minify-gradients/5.1.1_postcss@8.5.3:
    resolution: {integrity: sha512-VGvXMTpCEo4qHTNSa9A0a3D+dxGFZCYwR6Jokk+/3oB6flu2/PnPXAh2x7x52EkY5xlIHLm+Le8tJxe/7TNhzw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      colord: 2.9.3
      cssnano-utils: 3.1.0_postcss@8.5.3
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-minify-params/5.1.4_postcss@8.5.3:
    resolution: {integrity: sha512-+mePA3MgdmVmv6g+30rn57USjOGSAyuxUmkfiWpzalZ8aiBkdPYjXWtHuwJGm1v5Ojy0Z0LaSYhHaLJQB0P8Jw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.24.5
      cssnano-utils: 3.1.0_postcss@8.5.3
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-minify-selectors/5.2.1_postcss@8.5.3:
    resolution: {integrity: sha512-nPJu7OjZJTsVUmPdm2TcaiohIwxP+v8ha9NehQ2ye9szv4orirRU3SDdtUmKH+10nzn0bAyOXZ0UEr7OpvLehg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2
    dev: true

  /postcss-modules-extract-imports/2.0.0:
    resolution: {integrity: sha512-LaYLDNS4SG8Q5WAWqIJgdHPJrDDr/Lv775rMBFUbgjTz6j34lUznACHcdRWroPvXANP2Vj7yNK57vp9eFqzLWQ==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
    dev: false

  /postcss-modules-extract-imports/3.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-k3kNe0aNFQDAZGbin48pL2VNidTF0w4/eASDsxlyspobzU3wZQLOGj7L9gfRe0Jo9/4uud09DsjFNH7winGv8Q==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.5.3
    dev: true

  /postcss-modules-local-by-default/2.0.6:
    resolution: {integrity: sha512-oLUV5YNkeIBa0yQl7EYnxMgy4N6noxmiwZStaEJUSe2xPMcdNc8WmBQuQCx18H5psYbVxz8zoHk0RAAYZXP9gA==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 3.3.1
    dev: false

  /postcss-modules-local-by-default/4.2.0_postcss@8.5.3:
    resolution: {integrity: sha512-5kcJm/zk+GJDSfw+V/42fJ5fhjL5YbFDl8nVdXkJPLLW+Vf9mTD5Xe0wqIaDnLuL2U6cDNpTr+UQ+v2HWIBhzw==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      icss-utils: 5.1.0_postcss@8.5.3
      postcss: 8.5.3
      postcss-selector-parser: 7.1.0
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-modules-scope/2.2.0:
    resolution: {integrity: sha512-YyEgsTMRpNd+HmyC7H/mh3y+MeFWevy7V1evVhJWewmMbjDHIbZbOXICC2y+m1xI1UVfIT1HMW/O04Hxyu9oXQ==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-modules-scope/3.2.1_postcss@8.5.3:
    resolution: {integrity: sha512-m9jZstCVaqGjTAuny8MdgE88scJnCiQSlSrOWcTQgM2t32UBe+MUmFSO5t7VMSfAf/FJKImAxBav8ooCHJXCJA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 7.1.0
    dev: true

  /postcss-modules-values/2.0.0:
    resolution: {integrity: sha512-Ki7JZa7ff1N3EIMlPnGTZfUMe69FFwiQPnVSXC9mnn3jozCRBYIxiZd44yJOV2AmabOo4qFf8s0dC/+lweG7+w==}
    dependencies:
      icss-replace-symbols: 1.1.0
      postcss: 7.0.39
    dev: false

  /postcss-modules-values/4.0.0_postcss@8.5.3:
    resolution: {integrity: sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      icss-utils: 5.1.0_postcss@8.5.3
      postcss: 8.5.3
    dev: true

  /postcss-normalize-charset/5.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-mSgUJ+pd/ldRGVx26p2wz9dNZ7ji6Pn8VWBajMXFf8jk7vUoSrZ2lt/wZR7DtlZYKesmZI680qjr2CeFF2fbUg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
    dev: true

  /postcss-normalize-display-values/5.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-WP4KIM4o2dazQXWmFaqMmcvsKmhdINFblgSeRgn8BJ6vxaMyaJkwAzpPpuvSIoG/rmX3M+IrRZEz2H0glrQNEA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-positions/5.1.1_postcss@8.5.3:
    resolution: {integrity: sha512-6UpCb0G4eofTCQLFVuI3EVNZzBNPiIKcA1AKVka+31fTVySphr3VUgAIULBhxZkKgwLImhzMR2Bw1ORK+37INg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-repeat-style/5.1.1_postcss@8.5.3:
    resolution: {integrity: sha512-mFpLspGWkQtBcWIRFLmewo8aC3ImN2i/J3v8YCFUwDnPu3Xz4rLohDO26lGjwNsQxB3YF0KKRwspGzE2JEuS0g==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-string/5.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-oYiIJOf4T9T1N4i+abeIc7Vgm/xPCGih4bZz5Nm0/ARVJ7K6xrDlLwvwqOydvyL3RHNf8qZk6vo3aatiw/go3w==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-timing-functions/5.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-DOEkzJ4SAXv5xkHl0Wa9cZLF3WCBhF3o1SKVxKQAa+0pYKlueTpCgvkFAHfk+Y64ezX9+nITGrDZeVGgITJXjg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-unicode/5.1.1_postcss@8.5.3:
    resolution: {integrity: sha512-qnCL5jzkNUmKVhZoENp1mJiGNPcsJCs1aaRmURmeJGES23Z/ajaln+EPTD+rBeNkSryI+2WTdW+lwcVdOikrpA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.24.5
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-url/5.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-5upGeDO+PVthOxSmds43ZeMeZfKH+/DKgGRD7TElkkyS46JXAUhMzIKiCa7BabPeIy3AQcTkXwVVN7DbqsiCew==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      normalize-url: 6.1.0
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-normalize-whitespace/5.1.1_postcss@8.5.3:
    resolution: {integrity: sha512-83ZJ4t3NUDETIHTa3uEg6asWjSBYL5EdkVB0sDncx9ERzOKBVJIUeDO9RyA9Zwtig8El1d79HBp0JEi8wvGQnA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-ordered-values/5.1.3_postcss@8.5.3:
    resolution: {integrity: sha512-9UO79VUhPwEkzbb3RNpqqghc6lcYej1aveQteWY+4POIwlqkYE21HKWaLDF6lWNuqCobEAyTovVhtI32Rbv2RQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      cssnano-utils: 3.1.0_postcss@8.5.3
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-reduce-initial/5.1.2_postcss@8.5.3:
    resolution: {integrity: sha512-dE/y2XRaqAi6OvjzD22pjTUQ8eOfc6m/natGHgKFBK9DxFmIm69YmaRVQrGgFlEfc1HePIurY0TmDeROK05rIg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.24.5
      caniuse-api: 3.0.0
      postcss: 8.5.3
    dev: true

  /postcss-reduce-transforms/5.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-2fbdbmgir5AvpW9RLtdONx1QoYG2/EtqpNQbFASDlixBbAYuTcJ0dECwlqNqH7VbaUnEnh8SrxOe2sRIn24XyQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-selector-parser/5.0.0:
    resolution: {integrity: sha512-w+zLE5Jhg6Liz8+rQOWEAwtwkyqpfnmsinXjXg6cY7YIONZZtgvE0v2O0uhQBs0peNomOJwWRKt6JBfTdTd3OQ==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 2.0.0
      indexes-of: 1.0.1
      uniq: 1.0.1
    dev: true

  /postcss-selector-parser/6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  /postcss-selector-parser/7.1.0:
    resolution: {integrity: sha512-8sLjZwK0R+JlxlYcTuVnyT2v+htpdrjDOKuMcOVdYjt52Lh8hWRYpxBPoKx/Zg+bcjc3wx6fmQevMmUztS/ccA==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss-svgo/5.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-D75KsH1zm5ZrHyxPakAxJWtkyXew5qwS70v56exwvw542d9CRtTo78K0WeFxZB4G7JXKKMbEZtZayTGdIky/eA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
      svgo: 2.8.0
    dev: true

  /postcss-unique-selectors/5.1.1_postcss@8.5.3:
    resolution: {integrity: sha512-5JiODlELrz8L2HwxfPnhOWZYWDxVHWL83ufOv84NrcgipI7TaeRsatAhK4Tr2/ZiYldpK/wBvw5BD3qfaK96GA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2
    dev: true

  /postcss-urlrewrite/0.2.2:
    resolution: {integrity: sha512-DxPSgykgHjoV4Z+ygvq2C5HkiuiKQQD74xpoNQSQuyi8zab9nODVtNKfnCN6BEv9VZrjpOGLGAf8BDvgG6EtHg==}
    engines: {node: '>=0.12.9'}
    dependencies:
      postcss-helpers: 0.3.3
    dev: true

  /postcss-urlrewrite/0.3.0:
    resolution: {integrity: sha512-504S/dMa7a0n1yghE2I6fxY/DfMUM+w9qsFaoYnXE8KsCofmKLlA7PKbR+wtdEJ0N00Z1lGYxX/oFz13xArcLQ==}
    engines: {node: '>=16'}
    dependencies:
      postcss-helpers: 0.3.3
    dev: false

  /postcss-value-parser/3.3.1:
    resolution: {integrity: sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ==}

  /postcss-value-parser/4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}
    dev: true

  /postcss/6.0.23:
    resolution: {integrity: sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==}
    engines: {node: '>=4.0.0'}
    dependencies:
      chalk: 2.4.2
      source-map: 0.6.1
      supports-color: 5.5.0

  /postcss/7.0.39:
    resolution: {integrity: sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      picocolors: 0.2.1
      source-map: 0.6.1

  /postcss/8.5.3:
    resolution: {integrity: sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1
    dev: true

  /prelude-ls/1.1.2:
    resolution: {integrity: sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w==}
    engines: {node: '>= 0.8.0'}

  /prettier/2.8.8:
    resolution: {integrity: sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==}
    engines: {node: '>=10.13.0'}
    hasBin: true

  /pretty-error/4.0.0:
    resolution: {integrity: sha512-AoJ5YMAcXKYxKhuJGdcvse+Voc6v1RgnsR3nWcYU7q4t6z0Q6T86sv5Zq8VIRbOWWFpvdGE83LtdSMNd+6Y0xw==}
    dependencies:
      lodash: 4.17.21
      renderkid: 3.0.0
    dev: true

  /pretty-format/25.5.0:
    resolution: {integrity: sha512-kbo/kq2LQ/A/is0PQwsEHM7Ca6//bGPPvU6UnsdDRSKTWxT/ru/xb88v4BJf6a69H+uTytOEsTusT9ksd/1iWQ==}
    engines: {node: '>= 8.3'}
    dependencies:
      '@jest/types': 25.5.0
      ansi-regex: 5.0.1
      ansi-styles: 4.3.0
      react-is: 16.13.1
    dev: true

  /process-nextick-args/2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  /process/0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}

  /progress-webpack-plugin/1.0.16_webpack@5.99.8:
    resolution: {integrity: sha512-sdiHuuKOzELcBANHfrupYo+r99iPRyOnw15qX+rNlVUqXGfjXdH4IgxriKwG1kNJwVswKQHMdj1hYZMcb9jFaA==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0
    dependencies:
      chalk: 2.4.2
      figures: 2.0.0
      log-update: 2.3.0
      webpack: 5.99.8
    dev: true

  /promise-inflight/1.0.1:
    resolution: {integrity: sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==}
    dev: false

  /prompts/2.4.2:
    resolution: {integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==}
    engines: {node: '>= 6'}
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5
    dev: true

  /proxy-addr/2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1
    dev: true

  /prr/1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==}
    dev: false

  /pseudomap/1.0.2:
    resolution: {integrity: sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==}

  /psl/1.15.0:
    resolution: {integrity: sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==}
    dependencies:
      punycode: 2.3.1

  /public-encrypt/4.0.3:
    resolution: {integrity: sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q==}
    dependencies:
      bn.js: 4.12.2
      browserify-rsa: 4.1.1
      create-hash: 1.2.0
      parse-asn1: 5.1.7
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: false

  /pump/2.0.1:
    resolution: {integrity: sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0
    dev: false

  /pump/3.0.2:
    resolution: {integrity: sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  /pumpify/1.5.1:
    resolution: {integrity: sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==}
    dependencies:
      duplexify: 3.7.1
      inherits: 2.0.4
      pump: 2.0.1
    dev: false

  /punycode/1.4.1:
    resolution: {integrity: sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==}
    dev: false

  /punycode/2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  /qr-image/3.2.0:
    resolution: {integrity: sha512-rXKDS5Sx3YipVsqmlMJsJsk6jXylEpiHRC2+nJy66fxA5ExYyGa4PqwteW69SaVmAb2OQ18HbYriT7cGQMbduw==}
    dev: false

  /qrcode-reader/1.0.4:
    resolution: {integrity: sha512-rRjALGNh9zVqvweg1j5OKIQKNsw3bLC+7qwlnead5K/9cb1cEIAGkwikt/09U0K+2IDWGD9CC6SP7tHAjUeqvQ==}
    dev: true

  /qrcode-terminal/0.12.0:
    resolution: {integrity: sha512-EXtzRZmC+YGmGlDFbXKxQiMZNwCLEO6BANKXG4iCtSIM0yqc/pappSx3RIKr4r0uh5JsBckOXeKrB3Iz7mdQpQ==}
    hasBin: true

  /qs/6.13.0:
    resolution: {integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.1.0
    dev: true

  /qs/6.14.0:
    resolution: {integrity: sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.1.0
    dev: false

  /qs/6.5.3:
    resolution: {integrity: sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==}
    engines: {node: '>=0.6'}

  /query-string/7.1.3:
    resolution: {integrity: sha512-hh2WYhq4fi8+b+/2Kg9CEge4fDPvHS534aOOvOZeQ3+Vf2mCFsaFBYj0i+iXcAq6I9Vzp5fjMFBlONvayDC1qg==}
    engines: {node: '>=6'}
    dependencies:
      decode-uri-component: 0.2.2
      filter-obj: 1.1.0
      split-on-first: 1.1.0
      strict-uri-encode: 2.0.0
    dev: false

  /querystring-es3/0.2.1:
    resolution: {integrity: sha512-773xhDQnZBMFobEiztv8LIl70ch5MSF/jUQVlhwFyBILqq96anmoctVIYz+ZRp0qbCKATTn6ev02M3r7Ga5vqA==}
    engines: {node: '>=0.4.x'}
    dev: false

  /queue-microtask/1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}
    dev: true

  /randombytes/2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}
    dependencies:
      safe-buffer: 5.2.1

  /randomfill/1.0.4:
    resolution: {integrity: sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw==}
    dependencies:
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: false

  /range-parser/1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}
    dev: true

  /raw-body/2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==}
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  /rc/1.2.8:
    resolution: {integrity: sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==}
    hasBin: true
    dependencies:
      deep-extend: 0.6.0
      ini: 1.3.8
      minimist: 1.2.8
      strip-json-comments: 2.0.1
    dev: true

  /react-is/16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}
    dev: true

  /read-cache/1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}
    dependencies:
      pify: 2.3.0
    dev: true

  /read-pkg-up/7.0.1:
    resolution: {integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1
    dev: true

  /read-pkg/5.2.0:
    resolution: {integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==}
    engines: {node: '>=8'}
    dependencies:
      '@types/normalize-package-data': 2.4.4
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0
    dev: true

  /readable-stream/2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  /readable-stream/3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2
    dev: true

  /readdirp/2.2.1:
    resolution: {integrity: sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==}
    engines: {node: '>=0.10'}
    dependencies:
      graceful-fs: 4.2.11
      micromatch: 3.1.10
      readable-stream: 2.3.8
    dev: false
    optional: true

  /readdirp/3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1

  /readdirp/4.1.2:
    resolution: {integrity: sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==}
    engines: {node: '>= 14.18.0'}
    dev: true

  /realpath-native/2.0.0:
    resolution: {integrity: sha512-v1SEYUOXXdbBZK8ZuNgO4TBjamPsiSgcFr0aP+tEKpQZK8vooEUqV6nm6Cv502mX4NF2EfsnVqtNAHG+/6Ur1Q==}
    engines: {node: '>=8'}
    dev: true

  /recast/0.23.11:
    resolution: {integrity: sha512-YTUo+Flmw4ZXiWfQKGcwwc11KnoRAYgzAE2E7mXKCjSviTKShtxBsN6YUUBB2gtaBzKzeKunxhUwNHQuRryhWA==}
    engines: {node: '>= 4'}
    dependencies:
      ast-types: 0.16.1
      esprima: 4.0.1
      source-map: 0.6.1
      tiny-invariant: 1.3.3
      tslib: 2.8.1
    dev: true

  /rechoir/0.6.2:
    resolution: {integrity: sha512-HFM8rkZ+i3zrV+4LQjwQ0W+ez98pApMGM3HUrN04j3CqzPOzl9nmP15Y8YXNm8QHGv/eacOVEjqhmWpkRV0NAw==}
    engines: {node: '>= 0.10'}
    dependencies:
      resolve: 1.22.10
    dev: false

  /regenerate-unicode-properties/10.2.0:
    resolution: {integrity: sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2

  /regenerate/1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}

  /regenerator-runtime/0.12.1:
    resolution: {integrity: sha512-odxIc1/vDlo4iZcfXqRYFj0vpXFNoGdKMAUieAlFYO6m/nl5e9KR/beGf41z4a1FI+aQgtjhuaSlDxQ0hmkrHg==}
    dev: false

  /regenerator-runtime/0.13.11:
    resolution: {integrity: sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==}

  /regex-not/1.0.2:
    resolution: {integrity: sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
      safe-regex: 1.1.0

  /regexpu-core/6.2.0:
    resolution: {integrity: sha512-H66BPQMrv+V16t8xtmq+UC0CBpiTBA60V8ibS1QVReIp8T1z8hwFxqcGzm9K6lgsN7sB5edVH8a+ze6Fqm4weA==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsgen: 0.8.0
      regjsparser: 0.12.0
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0

  /registry-auth-token/3.3.2:
    resolution: {integrity: sha512-JL39c60XlzCVgNrO+qq68FoNb56w/m7JYvGR2jT5iR1xBrUA3Mfx5Twk5rqTThPmQKMWydGmq8oFtDlxfrmxnQ==}
    dependencies:
      rc: 1.2.8
      safe-buffer: 5.2.1
    dev: true

  /registry-url/3.1.0:
    resolution: {integrity: sha512-ZbgR5aZEdf4UKZVBPYIgaglBmSF2Hi94s2PcIHhRGFjKYu+chjJdYfHn4rt3hB6eCKLJ8giVIIfgMa1ehDfZKA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      rc: 1.2.8
    dev: true

  /regjsgen/0.8.0:
    resolution: {integrity: sha512-RvwtGe3d7LvWiDQXeQw8p5asZUmfU1G/l6WbUXeHta7Y2PEIvBTwH6E2EfmYUK8pxcxEdEmaomqyp0vZZ7C+3Q==}

  /regjsparser/0.12.0:
    resolution: {integrity: sha512-cnE+y8bz4NhMjISKbgeVJtqNbtf5QpjZP+Bslo+UqkIt9QPnX9q095eiRRASJG1/tz6dlNr6Z5NsBiWYokp6EQ==}
    hasBin: true
    dependencies:
      jsesc: 3.0.2

  /relateurl/0.2.7:
    resolution: {integrity: sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog==}
    engines: {node: '>= 0.10'}
    dev: true

  /remove-trailing-separator/1.1.0:
    resolution: {integrity: sha512-/hS+Y0u3aOfIETiaiirUFwDBDzmXPvO+jAfKTitUngIPzdKc6Z0LoFjM/CK5PL4C+eKwHohlHAb6H0VFfmmUsw==}

  /renderkid/3.0.0:
    resolution: {integrity: sha512-q/7VIQA8lmM1hF+jn+sFSPWGlMkSAeNYcPLmDQx2zzuiDfaLrOmumR8iaUKlenFgh0XRPIUeSPlH3A+AW3Z5pg==}
    dependencies:
      css-select: 4.3.0
      dom-converter: 0.2.0
      htmlparser2: 6.1.0
      lodash: 4.17.21
      strip-ansi: 6.0.1
    dev: true

  /repeat-element/1.1.4:
    resolution: {integrity: sha512-LFiNfRcSu7KK3evMyYOuCzv3L10TW7yC1G2/+StMjK8Y6Vqd2MG7r/Qjw4ghtuCOjFvlnms/iMmLqpvW/ES/WQ==}
    engines: {node: '>=0.10.0'}

  /repeat-string/1.6.1:
    resolution: {integrity: sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==}
    engines: {node: '>=0.10'}

  /request-promise-core/1.1.4_request@2.88.2:
    resolution: {integrity: sha512-TTbAfBBRdWD7aNNOoVOBH4pN/KigV6LyapYNNlAPA8JwbovRti1E88m3sYAwsLi5ryhPKsE9APwnjFTgdUjTpw==}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      request: ^2.34
    dependencies:
      lodash: 4.17.21
      request: 2.88.2
    dev: true

  /request-promise-native/1.0.9_request@2.88.2:
    resolution: {integrity: sha512-wcW+sIUiWnKgNY0dqCpOZkUbF/I+YPi+f09JZIDa39Ec+q82CpSYniDp+ISgTTbKmnpJWASeJBPZmoxH84wt3g==}
    engines: {node: '>=0.12.0'}
    deprecated: request-promise-native has been deprecated because it extends the now deprecated request package, see https://github.com/request/request/issues/3142
    peerDependencies:
      request: ^2.34
    dependencies:
      request: 2.88.2
      request-promise-core: 1.1.4_request@2.88.2
      stealthy-require: 1.1.1
      tough-cookie: 2.5.0
    dev: true

  /request/2.88.2:
    resolution: {integrity: sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.13.2
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  /require-directory/2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}
    dev: true

  /require-from-string/2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /require-main-filename/2.0.0:
    resolution: {integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==}
    dev: true

  /requires-port/1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}
    dev: true

  /resolve-cwd/3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==}
    engines: {node: '>=8'}
    dependencies:
      resolve-from: 5.0.0
    dev: true

  /resolve-from/4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  /resolve-from/5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}
    dev: true

  /resolve-path/1.4.0:
    resolution: {integrity: sha512-i1xevIst/Qa+nA9olDxLWnLk8YZbi8R/7JPbCMcgyWaFR6bKWaexgJgEB5oc2PKMjYdrHynyz0NY+if+H98t1w==}
    engines: {node: '>= 0.8'}
    dependencies:
      http-errors: 1.6.3
      path-is-absolute: 1.0.1
    dev: false

  /resolve-url/0.2.1:
    resolution: {integrity: sha512-ZuF55hVUQaaczgOIwqWzkEcEidmlD/xl44x1UZnhOXcYuFN2S6+rcxpG+C1N3So0wvNI3DmJICUFfu2SxhBmvg==}
    deprecated: https://github.com/lydell/resolve-url#deprecated

  /resolve/1.1.7:
    resolution: {integrity: sha512-9znBF0vBcaSN3W2j7wKvdERPwqTxSpCq+if5C0WoTCyV9n24rua28jeuQ2pL/HOf+yUe/Mef+H/5p60K0Id3bg==}
    dev: true

  /resolve/1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  /restore-cursor/2.0.0:
    resolution: {integrity: sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q==}
    engines: {node: '>=4'}
    dependencies:
      onetime: 2.0.1
      signal-exit: 3.0.7
    dev: true

  /restore-cursor/3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7
    dev: true

  /restore-cursor/4.0.0:
    resolution: {integrity: sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7
    dev: true

  /ret/0.1.15:
    resolution: {integrity: sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==}
    engines: {node: '>=0.12'}

  /retry/0.13.1:
    resolution: {integrity: sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==}
    engines: {node: '>= 4'}
    dev: true

  /reusify/1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  /rfdc/1.4.1:
    resolution: {integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==}
    dev: true

  /rimraf/2.7.1:
    resolution: {integrity: sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: false

  /rimraf/3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: true

  /ripemd160/2.0.2:
    resolution: {integrity: sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==}
    dependencies:
      hash-base: 3.0.5
      inherits: 2.0.4
    dev: false

  /rsvp/4.8.5:
    resolution: {integrity: sha512-nfMOlASu9OnRJo1mbEk2cz0D56a1MBNrJ7orjRZQG10XDyuvwksKbuXNp6qa+kbn839HwjwhBzhFmdsaEAfauA==}
    engines: {node: 6.* || >= 7.*}
    dev: true

  /run-parallel/1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3
    dev: true

  /run-queue/1.0.3:
    resolution: {integrity: sha512-ntymy489o0/QQplUDnpYAYUsO50K9SBrIVaKCWDOJzYJts0f9WH9RFJkyagebkw5+y1oi00R7ynNW/d12GBumg==}
    dependencies:
      aproba: 1.2.0
    dev: false

  /safe-area-insets/1.4.1:
    resolution: {integrity: sha512-r/nRWTjFGhhm3w1Z6Kd/jY11srN+lHt2mNl1E/emQGW8ic7n3Avu4noibklfSM+Y34peNphHD/BSZecav0sXYQ==}
    dev: false

  /safe-buffer/5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  /safe-buffer/5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  /safe-regex-test/1.1.0:
    resolution: {integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1
    dev: false

  /safe-regex/1.1.0:
    resolution: {integrity: sha512-aJXcif4xnaNUzvUuC5gcb46oTS7zvg4jpMTnuqtrEPlR3vFr4pxtdTwaF1Qs3Enjn9HK+ZlwQui+a7z0SywIzg==}
    dependencies:
      ret: 0.1.15

  /safer-buffer/2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  /sane/4.1.0:
    resolution: {integrity: sha512-hhbzAgTIX8O7SHfp2c8/kREfEn4qO/9q8C9beyY6+tvZ87EpoZ3i1RIEvp27YBswnNbY9mWd6paKVmKbAgLfZA==}
    engines: {node: 6.* || 8.* || >= 10.*}
    deprecated: some dependency vulnerabilities fixed, support for node < 10 dropped, and newer ECMAScript syntax/features added
    hasBin: true
    dependencies:
      '@cnakazawa/watch': 1.0.4
      anymatch: 2.0.0
      capture-exit: 2.0.0
      exec-sh: 0.3.6
      execa: 1.0.0
      fb-watchman: 2.0.2
      micromatch: 3.1.10
      minimist: 1.2.8
      walker: 1.0.8
    dev: true

  /sass-loader/13.3.3_sass@1.88.0:
    resolution: {integrity: sha512-mt5YN2F1MOZr3d/wBRcZxeFgwgkH44wVc2zohO2YF6JiOMkiXe4BYRZpSu2sO1g71mo/j16txzUhsKZlqjVGzA==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      fibers: '>= 3.1.0'
      node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
      sass: ^1.3.0
      sass-embedded: '*'
      webpack: ^5.0.0
    peerDependenciesMeta:
      fibers:
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
    dependencies:
      neo-async: 2.6.2
      sass: 1.88.0
    dev: true

  /sass/1.88.0:
    resolution: {integrity: sha512-sF6TWQqjFvr4JILXzG4ucGOLELkESHL+I5QJhh7CNaE+Yge0SI+ehCatsXhJ7ymU1hAFcIS3/PBpjdIbXoyVbg==}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      chokidar: 4.0.3
      immutable: 5.1.2
      source-map-js: 1.2.1
    optionalDependencies:
      '@parcel/watcher': 2.5.1
    dev: true

  /sax/1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}
    dev: true

  /saxes/3.1.11:
    resolution: {integrity: sha512-Ydydq3zC+WYDJK1+gRxRapLIED9PWeSuuS41wqyoRmzvhhh9nc+QQrVMKJYzJFULazeGhzSV0QleN2wD3boh2g==}
    engines: {node: '>=8'}
    dependencies:
      xmlchars: 2.2.0
    dev: true

  /schema-utils/1.0.0:
    resolution: {integrity: sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==}
    engines: {node: '>= 4'}
    dependencies:
      ajv: 6.12.6
      ajv-errors: 1.0.1_ajv@6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: false

  /schema-utils/2.7.1:
    resolution: {integrity: sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==}
    engines: {node: '>= 8.9.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6

  /schema-utils/3.3.0:
    resolution: {integrity: sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: true

  /schema-utils/4.3.2:
    resolution: {integrity: sha512-Gn/JaSk/Mt9gYubxTtSn/QCV4em9mpAPiR1rqy/Ocu19u/G9J5WWdNoUT4SiV6mFC3y6cxyFcFwdzPM3FgxGAQ==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1
      ajv-keywords: 5.1.0_ajv@8.17.1
    dev: true

  /select-hose/2.0.0:
    resolution: {integrity: sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg==}
    dev: true

  /selfsigned/2.4.1:
    resolution: {integrity: sha512-th5B4L2U+eGLq1TVh7zNRGBapioSORUeymIydxgFpwww9d2qyKvtuPU2jJuHvYAwwqi2Y596QBL3eEqcPEYL8Q==}
    engines: {node: '>=10'}
    dependencies:
      '@types/node-forge': 1.3.11
      node-forge: 1.3.1
    dev: true

  /semver/5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true

  /semver/6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  /semver/7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  /send/0.19.0:
    resolution: {integrity: sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    dev: true

  /serialize-javascript/4.0.0:
    resolution: {integrity: sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw==}
    dependencies:
      randombytes: 2.1.0
    dev: false

  /serialize-javascript/6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==}
    dependencies:
      randombytes: 2.1.0
    dev: true

  /serve-index/1.9.1:
    resolution: {integrity: sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    dev: true

  /serve-static/1.16.2:
    resolution: {integrity: sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    dev: true

  /set-blocking/2.0.0:
    resolution: {integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==}
    dev: true

  /set-function-length/1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  /set-value/2.0.1:
    resolution: {integrity: sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0

  /setimmediate/1.0.5:
    resolution: {integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==}
    dev: false

  /setprototypeof/1.1.0:
    resolution: {integrity: sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==}

  /setprototypeof/1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  /sha.js/2.4.11:
    resolution: {integrity: sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==}
    hasBin: true
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /shallow-clone/3.0.1:
    resolution: {integrity: sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==}
    engines: {node: '>=8'}
    dependencies:
      kind-of: 6.0.3

  /shebang-command/1.2.0:
    resolution: {integrity: sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      shebang-regex: 1.0.0
    dev: true

  /shebang-command/2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: true

  /shebang-regex/1.0.0:
    resolution: {integrity: sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /shebang-regex/3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}
    dev: true

  /shell-exec/1.0.2:
    resolution: {integrity: sha512-jyVd+kU2X+mWKMmGhx4fpWbPsjvD53k9ivqetutVW/BQ+WIZoDoP4d8vUMGezV6saZsiNoW2f9GIhg9Dondohg==}
    dev: true

  /shell-quote/1.8.2:
    resolution: {integrity: sha512-AzqKpGKjrj7EM6rKVQEPpB288oCfnrEIuyoT9cyF4nmGa7V8Zk6f7RRqYisX8X9m+Q7bd632aZW4ky7EhbQztA==}
    engines: {node: '>= 0.4'}
    dev: true

  /shelljs/0.8.5:
    resolution: {integrity: sha512-TiwcRcrkhHvbrZbnRcFYMLl30Dfov3HKqzp5tO5b4pt6G/SezKcYhmDg15zXVBswHmctSAQKznqNW2LO5tTDow==}
    engines: {node: '>=4'}
    hasBin: true
    dependencies:
      glob: 7.2.3
      interpret: 1.4.0
      rechoir: 0.6.2
    dev: false

  /shellwords/0.1.1:
    resolution: {integrity: sha512-vFwSUfQvqybiICwZY5+DAWIPLKsWO31Q91JSKl3UYv+K5c2QRPzn0qzec6QPu1Qc9eHYItiP3NdJqNVqetYAww==}
    dev: true
    optional: true

  /side-channel-list/1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  /side-channel-map/1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  /side-channel-weakmap/1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  /side-channel/1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  /signal-exit/3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}
    dev: true

  /sirv/2.0.4:
    resolution: {integrity: sha512-94Bdh3cC2PKrbgSOUqTiGPWVZeSiXfKOVZNJniWoqrWrRkB1CJzBU3NEbiTsPcYy1lDsANA/THzS+9WBiy5nfQ==}
    engines: {node: '>= 10'}
    dependencies:
      '@polka/url': 1.0.0-next.29
      mrmime: 2.0.1
      totalist: 3.0.1
    dev: true

  /sisteransi/1.0.5:
    resolution: {integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==}
    dev: true

  /slash/3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}
    dev: true

  /slice-ansi/5.0.0:
    resolution: {integrity: sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0
    dev: true

  /snapdragon-node/2.1.1:
    resolution: {integrity: sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 1.0.0
      isobject: 3.0.1
      snapdragon-util: 3.0.1

  /snapdragon-util/3.0.1:
    resolution: {integrity: sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /snapdragon/0.8.2:
    resolution: {integrity: sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      base: 0.11.2
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      map-cache: 0.2.2
      source-map: 0.5.7
      source-map-resolve: 0.5.3
      use: 3.1.1

  /socket.io-adapter/1.1.2:
    resolution: {integrity: sha512-WzZRUj1kUjrTIrUKpZLEzFZ1OLj5FwLlAFQs9kuZJzJi5DKdU7FsWc36SNmA8iDOtwBQyT8FkrriRM8vXLYz8g==}
    dev: false

  /socket.io-client/2.5.0:
    resolution: {integrity: sha512-lOO9clmdgssDykiOmVQQitwBAF3I6mYcQAo7hQ7AM6Ny5X7fp8hIJ3HcQs3Rjz4SoggoxA1OgrQyY8EgTbcPYw==}
    dependencies:
      backo2: 1.0.2
      component-bind: 1.0.0
      component-emitter: 1.3.1
      debug: 3.1.0
      engine.io-client: 3.5.4
      has-binary2: 1.0.3
      indexof: 0.0.1
      parseqs: 0.0.6
      parseuri: 0.0.6
      socket.io-parser: 3.3.4
      to-array: 0.1.4
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: false

  /socket.io-parser/3.3.4:
    resolution: {integrity: sha512-z/pFQB3x+EZldRRzORYW1vwVO8m/3ILkswtnpoeU6Ve3cbMWkmHEWDAVJn4QJtchiiFTo5j7UG2QvwxvaA9vow==}
    dependencies:
      component-emitter: 1.3.1
      debug: 3.1.0
      isarray: 2.0.1
    dev: false

  /socket.io-parser/3.4.3:
    resolution: {integrity: sha512-1rE4dZN3kCI/E5wixd393hmbqa78vVpkKmnEJhLeWoS/C5hbFYAbcSfnWoaVH43u9ToUVtzKjguxEZq+1XZfCQ==}
    engines: {node: '>=10.0.0'}
    dependencies:
      component-emitter: 1.2.1
      debug: 4.1.1
      isarray: 2.0.1
    dev: false

  /socket.io/2.5.1:
    resolution: {integrity: sha512-eaTE4tBKRD6RFoetquMbxgvcpvoDtRyIlkIMI/SMK2bsKvbENTsDeeu4GJ/z9c90yOWxB7b/eC+yKLPbHnH6bA==}
    dependencies:
      debug: 4.1.1
      engine.io: 3.6.2
      has-binary2: 1.0.3
      socket.io-adapter: 1.1.2
      socket.io-client: 2.5.0
      socket.io-parser: 3.4.3
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: false

  /sockjs/0.3.24:
    resolution: {integrity: sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==}
    dependencies:
      faye-websocket: 0.11.4
      uuid: 8.3.2
      websocket-driver: 0.7.4
    dev: true

  /source-list-map/2.0.1:
    resolution: {integrity: sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==}
    dev: false

  /source-map-js/1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /source-map-resolve/0.5.3:
    resolution: {integrity: sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw==}
    deprecated: See https://github.com/lydell/source-map-resolve#deprecated
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.2
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0

  /source-map-support/0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  /source-map-url/0.4.1:
    resolution: {integrity: sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw==}
    deprecated: See https://github.com/lydell/source-map-url#deprecated

  /source-map/0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==}
    engines: {node: '>=0.10.0'}

  /source-map/0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  /source-map/0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==}
    engines: {node: '>= 8'}

  /spdx-correct/3.2.0:
    resolution: {integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==}
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.21
    dev: true

  /spdx-exceptions/2.5.0:
    resolution: {integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==}
    dev: true

  /spdx-expression-parse/3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==}
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.21
    dev: true

  /spdx-license-ids/3.0.21:
    resolution: {integrity: sha512-Bvg/8F5XephndSK3JffaRqdT+gyhfqIPwDHpX80tJrF8QQRYMo8sNMeaZ2Dp5+jhwKnUmIOyFFQfHRkjJm5nXg==}
    dev: true

  /spdy-transport/3.0.0:
    resolution: {integrity: sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==}
    dependencies:
      debug: 4.4.1
      detect-node: 2.1.0
      hpack.js: 2.1.6
      obuf: 1.1.2
      readable-stream: 3.6.2
      wbuf: 1.7.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /spdy/4.0.2:
    resolution: {integrity: sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      debug: 4.4.1
      handle-thing: 2.0.1
      http-deceiver: 1.2.7
      select-hose: 2.0.0
      spdy-transport: 3.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /split-on-first/1.1.0:
    resolution: {integrity: sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw==}
    engines: {node: '>=6'}
    dev: false

  /split-string/3.1.0:
    resolution: {integrity: sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2

  /sprintf-js/1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}
    dev: true

  /sprintf-js/1.1.3:
    resolution: {integrity: sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==}
    dev: true

  /sshpk/1.18.0:
    resolution: {integrity: sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5

  /ssri/6.0.2:
    resolution: {integrity: sha512-cepbSq/neFK7xB6A50KHN0xHDotYzq58wWCa5LeWqnPrHG8GzfEjO/4O8kpmcGW+oaxkvhEJCWgbgNk4/ZV93Q==}
    dependencies:
      figgy-pudding: 3.5.2
    dev: false

  /ssri/8.0.1:
    resolution: {integrity: sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /stable/0.1.8:
    resolution: {integrity: sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==}
    deprecated: 'Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility'
    dev: true

  /stack-utils/1.0.5:
    resolution: {integrity: sha512-KZiTzuV3CnSnSvgMRrARVCj+Ht7rMbauGDK0LdVFRGyenwdylpajAp4Q0i6SX8rEmbTpMMf6ryq2gb8pPq2WgQ==}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 2.0.0
    dev: true

  /stackframe/1.3.4:
    resolution: {integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==}
    dev: true

  /static-extend/0.1.2:
    resolution: {integrity: sha512-72E9+uLc27Mt718pMHt9VMNiAL4LMsmDbBva8mxWUCkT07fSzEGMYUCk0XWY6lp0j6RBAG4cJ3mWuZv2OE3s0g==}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 0.2.5
      object-copy: 0.1.0

  /statuses/1.5.0:
    resolution: {integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==}
    engines: {node: '>= 0.6'}

  /statuses/2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  /stealthy-require/1.1.1:
    resolution: {integrity: sha512-ZnWpYnYugiOVEY5GkcuJK1io5V8QmNYChG62gSit9pQVGErXtrKuPC55ITaVSukmMta5qpMU7vqLt2Lnni4f/g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /stream-browserify/2.0.2:
    resolution: {integrity: sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /stream-each/1.2.3:
    resolution: {integrity: sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw==}
    dependencies:
      end-of-stream: 1.4.4
      stream-shift: 1.0.3
    dev: false

  /stream-http/2.8.3:
    resolution: {integrity: sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw==}
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 2.3.8
      to-arraybuffer: 1.0.1
      xtend: 4.0.2
    dev: false

  /stream-shift/1.0.3:
    resolution: {integrity: sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==}
    dev: false

  /strict-uri-encode/2.0.0:
    resolution: {integrity: sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ==}
    engines: {node: '>=4'}
    dev: false

  /stricter-htmlparser2/3.9.6:
    resolution: {integrity: sha512-w/PqXYnjfpi49nb3xnsizORItMM53M/jHdCL8Cer0OqO3vRLsNDtRKoHY/IkIuGnH7/TEn23yvlZoQQLt7pdrw==}
    dependencies:
      domelementtype: 1.3.1
      domutils: 1.7.0
      entities: 1.1.2
      inherits: 2.0.4
      readable-stream: 2.3.8
      x-domhandler: 2.4.2
    dev: true

  /string-argv/0.3.2:
    resolution: {integrity: sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==}
    engines: {node: '>=0.6.19'}
    dev: true

  /string-length/3.1.0:
    resolution: {integrity: sha512-Ttp5YvkGm5v9Ijagtaz1BnN+k9ObpvS0eIBblPMp2YWL8FBmi9qblQ9fexc2k/CXFgrTIteU3jAw3payCnwSTA==}
    engines: {node: '>=8'}
    dependencies:
      astral-regex: 1.0.0
      strip-ansi: 5.2.0
    dev: true

  /string-width/2.1.1:
    resolution: {integrity: sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==}
    engines: {node: '>=4'}
    dependencies:
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 4.0.0
    dev: true

  /string-width/4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    dev: true

  /string-width/5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0
    dev: true

  /string_decoder/1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}
    dependencies:
      safe-buffer: 5.1.2

  /string_decoder/1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}
    dependencies:
      safe-buffer: 5.2.1

  /strip-ansi/4.0.0:
    resolution: {integrity: sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow==}
    engines: {node: '>=4'}
    dependencies:
      ansi-regex: 3.0.1
    dev: true

  /strip-ansi/5.2.0:
    resolution: {integrity: sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==}
    engines: {node: '>=6'}
    dependencies:
      ansi-regex: 4.1.1
    dev: true

  /strip-ansi/6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: true

  /strip-ansi/7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-regex: 6.1.0
    dev: true

  /strip-bom/4.0.0:
    resolution: {integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==}
    engines: {node: '>=8'}
    dev: true

  /strip-eof/1.0.0:
    resolution: {integrity: sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q==}
    engines: {node: '>=0.10.0'}
    dev: true

  /strip-final-newline/2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}
    dev: true

  /strip-final-newline/3.0.0:
    resolution: {integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==}
    engines: {node: '>=12'}
    dev: true

  /strip-json-comments/2.0.1:
    resolution: {integrity: sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /stylehacks/5.1.1_postcss@8.5.3:
    resolution: {integrity: sha512-sBpcd5Hx7G6seo7b1LkpttvTz7ikD0LlH5RmdcBNb6fFR0Fl7LQwHDFr300q4cwUqi+IYrFGmsIHieMBfnN/Bw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.24.5
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2
    dev: true

  /supports-color/5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0

  /supports-color/7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0

  /supports-color/8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-hyperlinks/2.3.0:
    resolution: {integrity: sha512-RpsAZlpWcDwOPQA22aCH4J0t7L8JmAvsCxfOSEwm7cQs3LshN36QaTkwd70DnBOXDWGssw2eUoc8CaRWT0XunA==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
      supports-color: 7.2.0
    dev: true

  /supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  /svg-tags/1.0.0:
    resolution: {integrity: sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==}
    dev: true

  /svgo/2.8.0:
    resolution: {integrity: sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 4.3.0
      css-tree: 1.1.3
      csso: 4.2.0
      picocolors: 1.1.1
      stable: 0.1.8
    dev: true

  /symbol-tree/3.2.4:
    resolution: {integrity: sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==}
    dev: true

  /tapable/1.1.3:
    resolution: {integrity: sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==}
    engines: {node: '>=6'}
    dev: false

  /tapable/2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}
    dev: true

  /terminal-link/2.1.1:
    resolution: {integrity: sha512-un0FmiRUQNr5PJqy9kP7c40F5BOfpGlYTrxonDChEZB7pzZxRNp/bt+ymiy9/npwXya9KH99nJ/GXFIiUkYGFQ==}
    engines: {node: '>=8'}
    dependencies:
      ansi-escapes: 4.3.2
      supports-hyperlinks: 2.3.0
    dev: true

  /terser-webpack-plugin/1.4.6_webpack@4.47.0:
    resolution: {integrity: sha512-2lBVf/VMVIddjSn3GqbT90GvIJ/eYXJkt8cTzU7NbjKqK8fwv18Ftr4PlbF46b/e88743iZFL5Dtr/rC4hjIeA==}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      cacache: 12.0.4
      find-cache-dir: 2.1.0
      is-wsl: 1.1.0
      schema-utils: 1.0.0
      serialize-javascript: 4.0.0
      source-map: 0.6.1
      terser: 4.8.1
      webpack: 4.47.0
      webpack-sources: 1.4.3
      worker-farm: 1.7.0
    dev: false

  /terser-webpack-plugin/5.3.14_webpack@5.99.8:
    resolution: {integrity: sha512-vkZjpUjb6OMS7dhV+tILUW6BhpDR7P2L/aQSAv+Uwk+m8KATX9EccViHTJR2qDtACKPIYndLGCyl3FMo+r2LMw==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      jest-worker: 27.5.1
      schema-utils: 4.3.2
      serialize-javascript: 6.0.2
      terser: 5.39.1
      webpack: 5.99.8
    dev: true

  /terser/4.8.1:
    resolution: {integrity: sha512-4GnLC0x667eJG0ewJTa6z/yXrbLGv80D9Ru6HIpCQmO+Q4PfEtBFi0ObSckqwL6VyQv/7ENJieXHo2ANmdQwgw==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      commander: 2.20.3
      source-map: 0.6.1
      source-map-support: 0.5.21
    dev: false

  /terser/5.39.1:
    resolution: {integrity: sha512-Mm6+uad0ZuDtcV8/4uOZQDQ8RuiC5Pu+iZRedJtF7yA/27sPL7d++In/AJKpWZlU3SYMPPkVfwetn6sgZ66pUA==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.14.1
      commander: 2.20.3
      source-map-support: 0.5.21
    dev: true

  /test-exclude/6.0.0:
    resolution: {integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==}
    engines: {node: '>=8'}
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2
    dev: true

  /thenify-all/1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}
    dependencies:
      thenify: 3.3.1
    dev: true

  /thenify/3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}
    dependencies:
      any-promise: 1.3.0
    dev: true

  /thread-loader/3.0.4_webpack@5.99.8:
    resolution: {integrity: sha512-ByaL2TPb+m6yArpqQUZvP+5S1mZtXsEP7nWKKlAUTm7fCml8kB5s1uI3+eHRP2bk5mVYfRSBI7FFf+tWEyLZwA==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.27.0 || ^5.0.0
    dependencies:
      json-parse-better-errors: 1.0.2
      loader-runner: 4.3.0
      loader-utils: 2.0.4
      neo-async: 2.6.2
      schema-utils: 3.3.0
      webpack: 5.99.8
    dev: true

  /throat/5.0.0:
    resolution: {integrity: sha512-fcwX4mndzpLQKBS1DVYhGAcYaYt7vsHNIvQV+WXMvnow5cgjPphq5CaayLaGsjRdSCKZFNGt7/GYAuXaNOiYCA==}
    dev: true

  /through2/2.0.5:
    resolution: {integrity: sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==}
    dependencies:
      readable-stream: 2.3.8
      xtend: 4.0.2
    dev: false

  /thunky/1.1.0:
    resolution: {integrity: sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==}
    dev: true

  /timers-browserify/2.0.12:
    resolution: {integrity: sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ==}
    engines: {node: '>=0.6.0'}
    dependencies:
      setimmediate: 1.0.5
    dev: false

  /timm/1.7.1:
    resolution: {integrity: sha512-IjZc9KIotudix8bMaBW6QvMuq64BrJWFs1+4V0lXwWGQZwH+LnX87doAYhem4caOEusRP9/g6jVDQmZ8XOk1nw==}
    dev: true

  /tiny-invariant/1.3.3:
    resolution: {integrity: sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==}
    dev: true

  /tinycolor2/1.6.0:
    resolution: {integrity: sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==}
    dev: true

  /tmpl/1.0.5:
    resolution: {integrity: sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==}
    dev: true

  /to-array/0.1.4:
    resolution: {integrity: sha512-LhVdShQD/4Mk4zXNroIQZJC+Ap3zgLcDuwEdcmLv9CCO73NWockQDwyUnW/m8VX/EElfL6FcYx7EeutN4HJA6A==}
    dev: false

  /to-arraybuffer/1.0.1:
    resolution: {integrity: sha512-okFlQcoGTi4LQBG/PgSYblw9VOyptsz2KJZqc6qtgGdes8VktzUQkj4BI2blit072iS8VODNcMA+tvnS9dnuMA==}
    dev: false

  /to-object-path/0.3.0:
    resolution: {integrity: sha512-9mWHdnGRuh3onocaHzukyvCZhzvr6tiflAy/JRFXcJX0TjgfWA9pk9t8CMbzmBE4Jfw58pXbkngtBtqYxzNEyg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /to-regex-range/2.1.1:
    resolution: {integrity: sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      repeat-string: 1.6.1

  /to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0

  /to-regex/3.0.2:
    resolution: {integrity: sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 2.0.2
      extend-shallow: 3.0.2
      regex-not: 1.0.2
      safe-regex: 1.1.0

  /toidentifier/1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  /totalist/3.0.1:
    resolution: {integrity: sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==}
    engines: {node: '>=6'}
    dev: true

  /tough-cookie/2.5.0:
    resolution: {integrity: sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==}
    engines: {node: '>=0.8'}
    dependencies:
      psl: 1.15.0
      punycode: 2.3.1

  /tough-cookie/3.0.1:
    resolution: {integrity: sha512-yQyJ0u4pZsv9D4clxO69OEjLWYw+jbgspjTue4lTQZLfV0c5l1VmK2y1JK8E9ahdpltPOaAThPcp5nKPUgSnsg==}
    engines: {node: '>=6'}
    dependencies:
      ip-regex: 2.1.0
      psl: 1.15.0
      punycode: 2.3.1
    dev: true

  /tr46/0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}
    dev: true

  /tr46/1.0.1:
    resolution: {integrity: sha512-dTpowEjclQ7Kgx5SdBkqRzVhERQXov8/l9Ft9dVM9fmg0W0KQSVaXX9T4i6twCPNtYiZM53lpSSUAwJbFPOHxA==}
    dependencies:
      punycode: 2.3.1
    dev: true

  /tslib/2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}
    dev: true

  /tsscmp/1.0.6:
    resolution: {integrity: sha512-LxhtAkPDTkVCMQjt2h6eBVY28KCjikZqZfMcC15YBeNjkgUpdCfBu5HoiOTDu86v6smE8yOjyEktJ8hlbANHQA==}
    engines: {node: '>=0.6.x'}
    dev: false

  /tty-browserify/0.0.0:
    resolution: {integrity: sha512-JVa5ijo+j/sOoHGjw0sxw734b1LhBkQ3bvUGNdxnVXDCX81Yx7TFgnZygxrIIWn23hbfTaMYLwRmAxFyDuFmIw==}
    dev: false

  /tunnel-agent/0.6.0:
    resolution: {integrity: sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==}
    dependencies:
      safe-buffer: 5.2.1

  /tweetnacl/0.14.5:
    resolution: {integrity: sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==}

  /type-check/0.3.2:
    resolution: {integrity: sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2

  /type-detect/4.0.8:
    resolution: {integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==}
    engines: {node: '>=4'}
    dev: true

  /type-fest/0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}
    dev: true

  /type-fest/0.6.0:
    resolution: {integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==}
    engines: {node: '>=8'}
    dev: true

  /type-fest/0.8.1:
    resolution: {integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==}
    engines: {node: '>=8'}
    dev: true

  /type-fest/1.4.0:
    resolution: {integrity: sha512-yGSza74xk0UG8k+pLh5oeoYirvIiWo5t0/o3zHHAO2tRDiZcxWP7fywNlXhqb6/r6sWvwi+RsyQMWhVLe4BVuA==}
    engines: {node: '>=10'}
    dev: true

  /type-is/1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  /typedarray-to-buffer/3.1.5:
    resolution: {integrity: sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==}
    dependencies:
      is-typedarray: 1.0.0
    dev: true

  /typedarray/0.0.6:
    resolution: {integrity: sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==}
    dev: false

  /undici-types/6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  /unicode-canonical-property-names-ecmascript/2.0.1:
    resolution: {integrity: sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg==}
    engines: {node: '>=4'}

  /unicode-match-property-ecmascript/2.0.0:
    resolution: {integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==}
    engines: {node: '>=4'}
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0

  /unicode-match-property-value-ecmascript/2.2.0:
    resolution: {integrity: sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg==}
    engines: {node: '>=4'}

  /unicode-property-aliases-ecmascript/2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}

  /union-value/1.0.1:
    resolution: {integrity: sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1

  /uniq/1.0.1:
    resolution: {integrity: sha512-Gw+zz50YNKPDKXs+9d+aKAjVwpjNwqzvNpLigIruT4HA9lMZNdMqs9x07kKHB/L9WRzqp4+DlTU5s4wG2esdoA==}
    dev: true

  /unique-filename/1.1.1:
    resolution: {integrity: sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==}
    dependencies:
      unique-slug: 2.0.2
    dev: false

  /unique-slug/2.0.2:
    resolution: {integrity: sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==}
    dependencies:
      imurmurhash: 0.1.4
    dev: false

  /universalify/0.1.2:
    resolution: {integrity: sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==}
    engines: {node: '>= 4.0.0'}

  /universalify/2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}
    dev: true

  /unpipe/1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  /unset-value/1.0.0:
    resolution: {integrity: sha512-PcA2tsuGSF9cnySLHTLSh2qrQiJ70mn+r+Glzxv2TWZblxsxCC52BDlZoPCsz7STd9pN7EZetkWZBAvk4cgZdQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      has-value: 0.3.1
      isobject: 3.0.1

  /upath/1.2.0:
    resolution: {integrity: sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg==}
    engines: {node: '>=4'}
    dev: false
    optional: true

  /update-browserslist-db/1.1.3_browserslist@4.24.5:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.24.5
      escalade: 3.2.0
      picocolors: 1.1.1

  /update-check/1.5.4:
    resolution: {integrity: sha512-5YHsflzHP4t1G+8WGPlvKbJEbAJGCgw+Em+dGR1KmBUbr1J36SJBqlHLjR7oob7sco5hWHGQVcr9B2poIVDDTQ==}
    dependencies:
      registry-auth-token: 3.3.2
      registry-url: 3.1.0
    dev: true

  /uri-js/4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.3.1

  /urijs/1.19.11:
    resolution: {integrity: sha512-HXgFDgDommxn5/bIv0cnQZsPhHDA90NPHD6+c/v21U5+Sx5hoP8+dP9IZXBU1gIfvdRfhG8cel9QNPeionfcCQ==}

  /urix/0.1.0:
    resolution: {integrity: sha512-Am1ousAhSLBeB9cG/7k7r2R0zj50uDRlZHPGbazid5s9rlF1F/QKYObEKSIunSjIOkJZqwRRLpvewjEkM7pSqg==}
    deprecated: Please see https://github.com/lydell/urix#deprecated

  /url-loader/2.3.0:
    resolution: {integrity: sha512-goSdg8VY+7nPZKUEChZSEtW5gjbS66USIGCeSJ1OVOJ7Yfuh/36YxCwMi5HVEJh6mqUYOoy3NJ0vlOMrWsSHog==}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      file-loader: '*'
      webpack: ^4.0.0
    peerDependenciesMeta:
      file-loader:
        optional: true
    dependencies:
      loader-utils: 1.4.2
      mime: 2.6.0
      schema-utils: 2.7.1
    dev: false

  /url/0.11.4:
    resolution: {integrity: sha512-oCwdVC7mTuWiPyjLUz/COz5TLk6wgp0RCsN+wHZ2Ekneac9w8uuV0njcbbie2ME+Vs+d6duwmYuR3HgQXs1fOg==}
    engines: {node: '>= 0.4'}
    dependencies:
      punycode: 1.4.1
      qs: 6.14.0
    dev: false

  /use/3.1.1:
    resolution: {integrity: sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ==}
    engines: {node: '>=0.10.0'}

  /utif/2.0.1:
    resolution: {integrity: sha512-Z/S1fNKCicQTf375lIP9G8Sa1H/phcysstNrrSdZKj1f9g58J4NMgb5IgiEZN9/nLMPDwF0W7hdOe9Qq2IYoLg==}
    dependencies:
      pako: 1.0.11
    dev: true

  /util-deprecate/1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  /util/0.10.4:
    resolution: {integrity: sha512-0Pm9hTQ3se5ll1XihRic3FDIku70C+iHUdT/W926rSgHV5QgXsYbKZN8MSC3tJtSkhuROzvsQjAaFENRXr+19A==}
    dependencies:
      inherits: 2.0.3
    dev: false

  /util/0.11.1:
    resolution: {integrity: sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ==}
    dependencies:
      inherits: 2.0.3
    dev: false

  /utila/0.4.0:
    resolution: {integrity: sha512-Z0DbgELS9/L/75wZbro8xAnT50pBVFQZ+hUEueGDU5FN51YSCYM+jdxsfCiHjwNP/4LCDD0i/graKpeBnOXKRA==}
    dev: true

  /utils-merge/1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}
    dev: true

  /uuid/3.4.0:
    resolution: {integrity: sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  /uuid/8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true
    dev: true

  /v8-to-istanbul/4.1.4:
    resolution: {integrity: sha512-Rw6vJHj1mbdK8edjR7+zuJrpDtKIgNdAvTSAcpYfgMIw+u2dPDntD3dgN4XQFLU2/fvFQdzj+EeSGfd/jnY5fQ==}
    engines: {node: 8.x.x || >=10.10.0}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
      convert-source-map: 1.9.0
      source-map: 0.7.4
    dev: true

  /validate-npm-package-license/3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1
    dev: true

  /vary/1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  /verror/1.10.0:
    resolution: {integrity: sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==}
    engines: {'0': node >=0.6.0}
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0

  /vm-browserify/1.1.2:
    resolution: {integrity: sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ==}
    dev: false

  /vue-hot-reload-api/2.3.4:
    resolution: {integrity: sha512-BXq3jwIagosjgNVae6tkHzzIk6a8MHFtzAdwhnV5VlvPTFxDCvIttgSiHWjdGoTJvXtmRu5HacExfdarRcFhog==}

  /vue-loader/15.11.1_jlzinulpsogv7ychuggq4lj26m:
    resolution: {integrity: sha512-0iw4VchYLePqJfJu9s62ACWUXeSqM30SQqlIftbYWM3C+jpPcEHKSPUZBLjSF9au4HTHQ/naF6OGnO3Q/qGR3Q==}
    peerDependencies:
      cache-loader: '*'
      css-loader: '*'
      prettier: '*'
      vue-template-compiler: '*'
      webpack: ^3.0.0 || ^4.1.0 || ^5.0.0-0
    peerDependenciesMeta:
      cache-loader:
        optional: true
      prettier:
        optional: true
      vue-template-compiler:
        optional: true
    dependencies:
      '@vue/component-compiler-utils': 3.3.0
      css-loader: 2.1.1
      hash-sum: 1.0.2
      loader-utils: 1.4.2
      prettier: 2.8.8
      vue-hot-reload-api: 2.3.4
      vue-style-loader: 4.1.3
      vue-template-compiler: 2.6.14
    dev: false

  /vue-loader/15.11.1_ykdgxedkhcf5eunzkwd2dxqjea:
    resolution: {integrity: sha512-0iw4VchYLePqJfJu9s62ACWUXeSqM30SQqlIftbYWM3C+jpPcEHKSPUZBLjSF9au4HTHQ/naF6OGnO3Q/qGR3Q==}
    peerDependencies:
      cache-loader: '*'
      css-loader: '*'
      prettier: '*'
      vue-template-compiler: '*'
      webpack: ^3.0.0 || ^4.1.0 || ^5.0.0-0
    peerDependenciesMeta:
      cache-loader:
        optional: true
      prettier:
        optional: true
      vue-template-compiler:
        optional: true
    dependencies:
      '@vue/component-compiler-utils': 3.3.0
      css-loader: 6.11.0_webpack@5.99.8
      hash-sum: 1.0.2
      loader-utils: 1.4.2
      prettier: 2.8.8
      vue-hot-reload-api: 2.3.4
      vue-style-loader: 4.1.3
      vue-template-compiler: 2.6.14
      webpack: 5.99.8
    dev: true

  /vue-loader/17.4.2_vue@2.6.14+webpack@5.99.8:
    resolution: {integrity: sha512-yTKOA4R/VN4jqjw4y5HrynFL8AK0Z3/Jt7eOJXEitsm0GMRHDBjCfCiuTiLP7OESvsZYo2pATCWhDqxC5ZrM6w==}
    peerDependencies:
      '@vue/compiler-sfc': '*'
      vue: '*'
      webpack: ^4.1.0 || ^5.0.0-0
    peerDependenciesMeta:
      '@vue/compiler-sfc':
        optional: true
      vue:
        optional: true
    dependencies:
      chalk: 4.1.2
      hash-sum: 2.0.0
      vue: 2.6.14
      watchpack: 2.4.2
      webpack: 5.99.8
    dev: true

  /vue-style-loader/4.1.3:
    resolution: {integrity: sha512-sFuh0xfbtpRlKfm39ss/ikqs9AbKCoXZBpHeVZ8Tx650o0k0q/YCM7FRvigtxpACezfq6af+a7JeqVTWvncqDg==}
    dependencies:
      hash-sum: 1.0.2
      loader-utils: 1.4.2

  /vue-template-compiler/2.6.14:
    resolution: {integrity: sha512-ODQS1SyMbjKoO1JBJZojSw6FE4qnh9rIpUZn2EUT86FKizx9uH5z6uXiIrm4/Nb/gwxTi/o17ZDEGWAXHvtC7g==}
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0

  /vue-template-es2015-compiler/1.9.1:
    resolution: {integrity: sha512-4gDntzrifFnCEvyoO8PqyJDmguXgVPxKiIxrBKjIowvL9l+N66196+72XVYR8BBf1Uv1Fgt3bGevJ+sEmxfZzw==}

  /vue/2.6.14:
    resolution: {integrity: sha512-x2284lgYvjOMj3Za7kqzRcUSxBboHqtgRE2zlos1qWaOye5yUmHn42LB1250NJBLRwEcdrB0JRwyPTEPhfQjiQ==}
    deprecated: Vue 2 has reached EOL and is no longer actively maintained. See https://v2.vuejs.org/eol/ for more details.
    dev: false

  /vuex/3.6.2_vue@2.6.14:
    resolution: {integrity: sha512-ETW44IqCgBpVomy520DT5jf8n0zoCac+sxWnn+hMe/CzaSejb/eVw2YToiXYX+Ex/AuHHia28vWTq4goAexFbw==}
    peerDependencies:
      vue: ^2.0.0
    dependencies:
      vue: 2.6.14
    dev: false

  /w3c-hr-time/1.0.2:
    resolution: {integrity: sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ==}
    deprecated: Use your platform's native performance.now() and performance.timeOrigin.
    dependencies:
      browser-process-hrtime: 1.0.0
    dev: true

  /w3c-xmlserializer/1.1.2:
    resolution: {integrity: sha512-p10l/ayESzrBMYWRID6xbuCKh2Fp77+sA0doRuGn4tTIMrrZVeqfpKjXHY+oDh3K4nLdPgNwMTVP6Vp4pvqbNg==}
    dependencies:
      domexception: 1.0.1
      webidl-conversions: 4.0.2
      xml-name-validator: 3.0.0
    dev: true

  /walker/1.0.8:
    resolution: {integrity: sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==}
    dependencies:
      makeerror: 1.0.12
    dev: true

  /watchpack-chokidar2/2.0.1:
    resolution: {integrity: sha512-nCFfBIPKr5Sh61s4LPpy1Wtfi0HE8isJ3d2Yb5/Ppw2P2B/3eVSEBjKfN0fmHJSK14+31KwMKmcrzs2GM4P0Ww==}
    requiresBuild: true
    dependencies:
      chokidar: 2.1.8
    dev: false
    optional: true

  /watchpack/1.7.5:
    resolution: {integrity: sha512-9P3MWk6SrKjHsGkLT2KHXdQ/9SNkyoJbabxnKOoJepsvJjJG8uYTR3yTPxPQvNDI3w4Nz1xnE0TLHK4RIVe/MQ==}
    dependencies:
      graceful-fs: 4.2.11
      neo-async: 2.6.2
    optionalDependencies:
      chokidar: 3.6.0
      watchpack-chokidar2: 2.0.1
    dev: false

  /watchpack/2.4.2:
    resolution: {integrity: sha512-TnbFSbcOCcDgjZ4piURLCbJ3nJhznVh9kw6F6iokjiFPl8ONxe9A6nMDVXDiNbrSfLILs6vB07F7wLBrwPYzJw==}
    engines: {node: '>=10.13.0'}
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
    dev: true

  /wbuf/1.7.3:
    resolution: {integrity: sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==}
    dependencies:
      minimalistic-assert: 1.0.1
    dev: true

  /wcwidth/1.0.1:
    resolution: {integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==}
    dependencies:
      defaults: 1.0.4
    dev: true

  /webidl-conversions/3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}
    dev: true

  /webidl-conversions/4.0.2:
    resolution: {integrity: sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg==}
    dev: true

  /webpack-bundle-analyzer/4.10.2:
    resolution: {integrity: sha512-vJptkMm9pk5si4Bv922ZbKLV8UTT4zib4FPgXMhgzUny0bfDDkLXAVQs3ly3fS4/TN9ROFtb0NFrm04UXFE/Vw==}
    engines: {node: '>= 10.13.0'}
    hasBin: true
    dependencies:
      '@discoveryjs/json-ext': 0.5.7
      acorn: 8.14.1
      acorn-walk: 8.3.4
      commander: 7.2.0
      debounce: 1.2.1
      escape-string-regexp: 4.0.0
      gzip-size: 6.0.0
      html-escaper: 2.0.2
      opener: 1.5.2
      picocolors: 1.1.1
      sirv: 2.0.4
      ws: 7.5.10
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: true

  /webpack-chain/6.5.1:
    resolution: {integrity: sha512-7doO/SRtLu8q5WM0s7vPKPWX580qhi0/yBHkOxNkv50f6qB76Zy9o2wRTrrPULqYTvQlVHuvbA8v+G5ayuUDsA==}
    engines: {node: '>=8'}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.
    dependencies:
      deepmerge: 1.5.2
      javascript-stringify: 2.1.0
    dev: true

  /webpack-dev-middleware/5.3.4_webpack@5.99.8:
    resolution: {integrity: sha512-BVdTqhhs+0IfoeAf7EoH5WE+exCmqGerHfDM0IL096Px60Tq2Mn9MAbnaGUe6HiMa41KMCYF19gyzZmBcq/o4Q==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      colorette: 2.0.20
      memfs: 3.5.3
      mime-types: 2.1.35
      range-parser: 1.2.1
      schema-utils: 4.3.2
      webpack: 5.99.8
    dev: true

  /webpack-dev-server/4.15.2_debug@4.4.1+webpack@5.99.8:
    resolution: {integrity: sha512-0XavAZbNJ5sDrCbkpWL8mia0o5WPOd2YGtxrEiZkBK9FjLppIUK2TgxK6qGD2P3hUXTJNNPVibrerKcx5WkR1g==}
    engines: {node: '>= 12.13.0'}
    hasBin: true
    peerDependencies:
      webpack: ^4.37.0 || ^5.0.0
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack:
        optional: true
      webpack-cli:
        optional: true
    dependencies:
      '@types/bonjour': 3.5.13
      '@types/connect-history-api-fallback': 1.5.4
      '@types/express': 4.17.21
      '@types/serve-index': 1.9.4
      '@types/serve-static': 1.15.7
      '@types/sockjs': 0.3.36
      '@types/ws': 8.18.1
      ansi-html-community: 0.0.8
      bonjour-service: 1.3.0
      chokidar: 3.6.0
      colorette: 2.0.20
      compression: 1.8.0
      connect-history-api-fallback: 2.0.0
      default-gateway: 6.0.3
      express: 4.21.2
      graceful-fs: 4.2.11
      html-entities: 2.6.0
      http-proxy-middleware: 2.0.9_g3bw2jtlgbqqf2rcku7vbigucu
      ipaddr.js: 2.2.0
      launch-editor: 2.10.0
      open: 8.4.2
      p-retry: 4.6.2
      rimraf: 3.0.2
      schema-utils: 4.3.2
      selfsigned: 2.4.1
      serve-index: 1.9.1
      sockjs: 0.3.24
      spdy: 4.0.2
      webpack: 5.99.8
      webpack-dev-middleware: 5.3.4_webpack@5.99.8
      ws: 8.18.2
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate
    dev: true

  /webpack-merge/4.2.2:
    resolution: {integrity: sha512-TUE1UGoTX2Cd42j3krGYqObZbOD+xF7u28WB7tfUordytSjbWTIjK/8V0amkBfTYN4/pB/GIDlJZZ657BGG19g==}
    dependencies:
      lodash: 4.17.21
    dev: true

  /webpack-merge/5.10.0:
    resolution: {integrity: sha512-+4zXKdx7UnO+1jaN4l2lHVD+mFvnlZQP/6ljaJVb4SZiwIKeUnrT5l0gkT8z+n4hKpC+jpOv6O9R+gLtag7pSA==}
    engines: {node: '>=10.0.0'}
    dependencies:
      clone-deep: 4.0.1
      flat: 5.0.2
      wildcard: 2.0.1
    dev: true

  /webpack-sources/1.4.3:
    resolution: {integrity: sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==}
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1
    dev: false

  /webpack-sources/3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    engines: {node: '>=10.13.0'}
    dev: true

  /webpack-virtual-modules/0.4.6:
    resolution: {integrity: sha512-5tyDlKLqPfMqjT3Q9TAqf2YqjwmnUleZwzJi1A5qXnlBCdj2AtOJ6wAWdglTIDOPgOiOrXeBeFcsQ8+aGQ6QbA==}
    dev: true

  /webpack/4.47.0:
    resolution: {integrity: sha512-td7fYwgLSrky3fI1EuU5cneU4+pbH6GgOfuKNS1tNPcfdGinGELAqsb/BP4nnvZyKSG2i/xFGU7+n2PvZA8HJQ==}
    engines: {node: '>=6.11.5'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
      webpack-command: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
      webpack-command:
        optional: true
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/wasm-edit': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      acorn: 6.4.2
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
      chrome-trace-event: 1.0.4
      enhanced-resolve: 4.5.0
      eslint-scope: 4.0.3
      json-parse-better-errors: 1.0.2
      loader-runner: 2.4.0
      loader-utils: 1.4.2
      memory-fs: 0.4.1
      micromatch: 3.1.10
      mkdirp: 0.5.6
      neo-async: 2.6.2
      node-libs-browser: 2.2.1
      schema-utils: 1.0.0
      tapable: 1.1.3
      terser-webpack-plugin: 1.4.6_webpack@4.47.0
      watchpack: 1.7.5
      webpack-sources: 1.4.3
    dev: false

  /webpack/5.99.8:
    resolution: {integrity: sha512-lQ3CPiSTpfOnrEGeXDwoq5hIGzSjmwD72GdfVzF7CQAI7t47rJG9eDWvcEkEn3CUQymAElVvDg3YNTlCYj+qUQ==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.7
      '@types/json-schema': 7.0.15
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/wasm-edit': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      acorn: 8.14.1
      browserslist: 4.24.5
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.18.1
      es-module-lexer: 1.7.0
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 4.3.2
      tapable: 2.2.1
      terser-webpack-plugin: 5.3.14_webpack@5.99.8
      watchpack: 2.4.2
      webpack-sources: 3.2.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js
    dev: true

  /websocket-driver/0.7.4:
    resolution: {integrity: sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==}
    engines: {node: '>=0.8.0'}
    dependencies:
      http-parser-js: 0.5.10
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4
    dev: true

  /websocket-extensions/0.1.4:
    resolution: {integrity: sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==}
    engines: {node: '>=0.8.0'}
    dev: true

  /whatwg-encoding/1.0.5:
    resolution: {integrity: sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw==}
    dependencies:
      iconv-lite: 0.4.24
    dev: true

  /whatwg-fetch/3.6.20:
    resolution: {integrity: sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==}
    dev: true

  /whatwg-mimetype/2.3.0:
    resolution: {integrity: sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g==}
    dev: true

  /whatwg-url/5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1
    dev: true

  /whatwg-url/7.1.0:
    resolution: {integrity: sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg==}
    dependencies:
      lodash.sortby: 4.7.0
      tr46: 1.0.1
      webidl-conversions: 4.0.2
    dev: true

  /which-module/2.0.1:
    resolution: {integrity: sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==}
    dev: true

  /which/1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==}
    hasBin: true
    dependencies:
      isexe: 2.0.0

  /which/2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /wildcard/2.0.1:
    resolution: {integrity: sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ==}
    dev: true

  /word-wrap/1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  /worker-farm/1.7.0:
    resolution: {integrity: sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw==}
    dependencies:
      errno: 0.1.8
    dev: false

  /wrap-ansi/3.0.1:
    resolution: {integrity: sha512-iXR3tDXpbnTpzjKSylUJRkLuOrEC7hwEB221cgn6wtF8wpmz28puFXAEfPT5zrjM3wahygB//VuWEr1vTkDcNQ==}
    engines: {node: '>=4'}
    dependencies:
      string-width: 2.1.1
      strip-ansi: 4.0.0
    dev: true

  /wrap-ansi/6.2.0:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi/7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi/8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0
    dev: true

  /wrap-loader/0.2.0:
    resolution: {integrity: sha512-Qdhdu7vr2H8dLE2sKySQznOBHXIHbKg7PZ5aqkeBOQHGqxLfcJw/ZlB40j67b1tks9OYqSBCHc+uHtGRCmQYlg==}
    dependencies:
      loader-utils: 1.4.2
    dev: true

  /wrappy/1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  /write-file-atomic/3.0.3:
    resolution: {integrity: sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==}
    dependencies:
      imurmurhash: 0.1.4
      is-typedarray: 1.0.0
      signal-exit: 3.0.7
      typedarray-to-buffer: 3.1.5
    dev: true

  /ws/7.5.10:
    resolution: {integrity: sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  /ws/8.18.2:
    resolution: {integrity: sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true
    dev: true

  /x-domhandler/2.4.2:
    resolution: {integrity: sha512-c+T0p5hsahezbHpahjNpSDKjMMwGwOnG6Iaz8zzSjEBlJM47hakZmNBLNpktZJjuRdopoUMPf7HzcPcTE1G0GQ==}
    dependencies:
      domelementtype: 1.3.1
    dev: true

  /xhr/2.6.0:
    resolution: {integrity: sha512-/eCGLb5rxjx5e3mF1A7s+pLlR6CGyqWN91fv1JgER5mVWg1MZmlhBvy9kjcsOdRk8RrIujotWyJamfyrp+WIcA==}
    dependencies:
      global: 4.4.0
      is-function: 1.0.2
      parse-headers: 2.0.6
      xtend: 4.0.2
    dev: true

  /xml-name-validator/3.0.0:
    resolution: {integrity: sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw==}
    dev: true

  /xml-parse-from-string/1.0.1:
    resolution: {integrity: sha512-ErcKwJTF54uRzzNMXq2X5sMIy88zJvfN2DmdoQvy7PAFJ+tPRU6ydWuOKNMyfmOjdyBQTFREi60s0Y0SyI0G0g==}
    dev: true

  /xml2js/0.5.0:
    resolution: {integrity: sha512-drPFnkQJik/O+uPKpqSgr22mpuFHqKdbS835iAQrUC73L2F5WkboIRd63ai/2Yg6I1jzifPFKH2NTK+cfglkIA==}
    engines: {node: '>=4.0.0'}
    dependencies:
      sax: 1.4.1
      xmlbuilder: 11.0.1
    dev: true

  /xmlbuilder/11.0.1:
    resolution: {integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==}
    engines: {node: '>=4.0'}
    dev: true

  /xmlchars/2.2.0:
    resolution: {integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==}
    dev: true

  /xmlhttprequest-ssl/1.6.3:
    resolution: {integrity: sha512-3XfeQE/wNkvrIktn2Kf0869fC0BN6UpydVasGIeSm2B1Llihf7/0UfZM+eCkOw3P7bP4+qPgqhm7ZoxuJtFU0Q==}
    engines: {node: '>=0.4.0'}
    dev: false

  /xregexp/4.0.0:
    resolution: {integrity: sha512-PHyM+sQouu7xspQQwELlGwwd05mXUFqwFYfqPO0cC7x4fxyHnnuetmQr6CjJiafIDoH4MogHb9dOoJzR/Y4rFg==}
    dev: true

  /xtend/4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  /y18n/4.0.3:
    resolution: {integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==}

  /y18n/5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}
    dev: true

  /yallist/2.1.2:
    resolution: {integrity: sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==}

  /yallist/3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  /yallist/4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}
    dev: true

  /yaml/1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}
    dev: true

  /yaml/2.3.1:
    resolution: {integrity: sha512-2eHWfjaoXgTBC2jNM1LRef62VQa0umtvRiDSk6HSzW7RvS5YtkabJrwYLLEKWBc8a5U2PTSCs+dJjUTJdlHsWQ==}
    engines: {node: '>= 14'}
    dev: true

  /yargs-parser/18.1.3:
    resolution: {integrity: sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==}
    engines: {node: '>=6'}
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0
    dev: true

  /yargs-parser/20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}
    dev: true

  /yargs/15.4.1:
    resolution: {integrity: sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==}
    engines: {node: '>=8'}
    dependencies:
      cliui: 6.0.0
      decamelize: 1.2.0
      find-up: 4.1.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 4.2.3
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 18.1.3
    dev: true

  /yargs/16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==}
    engines: {node: '>=10'}
    dependencies:
      cliui: 7.0.4
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9
    dev: true

  /yeast/0.1.2:
    resolution: {integrity: sha512-8HFIh676uyGYP6wP13R/j6OJ/1HwJ46snpvzE7aHAN3Ryqh2yX6Xox2B4CUmTwwOIzlG3Bs7ocsP5dZH/R1Qbg==}
    dev: false

  /ylru/1.4.0:
    resolution: {integrity: sha512-2OQsPNEmBCvXuFlIni/a+Rn+R2pHW9INm0BxXJ4hVDA8TirqMj+J/Rp9ItLatT/5pZqWwefVrTQcHpixsxnVlA==}
    engines: {node: '>= 4.0.0'}
    dev: false
