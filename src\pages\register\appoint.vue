<template>
	<view>
		<view class="container">
			<headerBAr title="预约挂号"></headerBAr>
			<view class="top-40">
				<view class="page-title">
					<view class="page-title-tab">选择院区</view>
					<view class="page-title-tab blue" @click="gotoPanorama">全景导览 ></view>
				</view>
				<view class="hosList">
					<view class="hosItem" v-for="(item, index) in hosList" @click="depatrTab(item)" :key="index">
						<view class="hosItem-title">{{ item.hos_name }}</view>
						<view class="hosItem-info">
							<view class="hosItem-address">{{ item.hospital_addr }}</view>
							<view class="hosItem-tolink">预约</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<uni-popup ref="tipPopup" type="center" :is-mask-click="false" :animation="false">
			<view class="popup-content">
				<view class="popup-title">提示</view>
				<view class="popup-scroll" >
					<!-- <scroll-view class="scroll-view-sclrol"
					:show-scrollbar="true" scroll-y @scrolltolower="handleScrolltolower"> -->
					<view class="popup-text">
						<!-- <text style="font-size: 16px; font-weight: 600;">请下拉到底阅读后按确定</text> -->
						<text>尊敬的各位患者及家属：</text>
						<text>我院门诊开展实名制非急诊全预约的诊疗模式，到院就诊请注意:</text>
						<text>1. 目前我院前兴院区、书林院区均设有发热门诊，如首诊体温≥37.3℃、呼吸道感染等症状“急诊患者排外”，请预约发热门诊就诊；</text>
						<text>2. 每个患者最多二位家属陪同，正确佩戴口罩，不聚集，自觉执行（一患一室）；</text>
						<text>3. 号源更新时间：每日24：00，可提前5天预约；</text>
						<text style="color: red;">4. 请带患者严格按照预约时段到相应科室分诊台签到候诊，系统仅支持提前30分钟内取号，迟到30分钟后系统将号源自动后延；</text>
						<text style="color: red;">5. 无法如期就诊者，请在预约就诊时间前在原预约渠道进行取消退号；</text>
						<text>6. 超过预约时间1小时视为爽约，多次爽约会影响其就诊信誉；</text>
						<text>7. 同一患者一天所有预约渠道可预约同一科室2个号，不同的临床科室同一天预约就诊，预约时间需间隔1小时。</text>
					</view>
					<!-- </scroll-view> -->
				</view>
				<view class="popup-btns">
					<view class="popup-btn primary" @click="closeTipPopup()">确认</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import {
		gethosList
	} from '@/api/waiting'
	import headerBAr from '@/pages/component/header_bar/header_bar.vue'
	import {
		reportCmPV
	} from '@/to/cloudMonitorHelper';

	export default {
		components: {
			headerBAr,
		},
		data() {
			return {
				navIndex: 0,
				hosList: [],
				isCanClose: false,
			}
		},
		methods: {
			gotoPanorama() {
				// #ifdef MP-ALIPAY
				uni.navigateTo({
					url: '/pages/panorama/panorama'
				})
				// #endif
				// #ifdef H5
				window.location.href = 'https://vr.justeasy.cn/view/2377594-1617357677.html?from=timeline&isappinstalled='
				// #endif
			},
			depatrTab(panel, id) {
				switch (panel.dep_id) {
					case 'A000158':
						uni.showModal({
							title: '提示',
							content: '尊敬的家长，感谢您选择昆明市儿童医院罕见病疑难病多学科联合门诊：愿您有一个愉快的就诊过程。1. 多学科联合门诊诊疗模式（MDT）是由至少三名取得高级职称三年以上的医师组成的专家团队为患者制定诊疗方案，主要针对病情复杂、诊断困难或诊疗涉及多个学科的患者。2.诊疗时间为每周一下午，请至少提前五天预约，预约挂号成功后将有医务人员和您电话联系，进行正式诊疗前的预诊，完成病史采集、资料准备及专家安排等工作。3.收费标准：①院内专家：每例诊疗三名专家收费400元，每增加一名专家加收100元。②省内外专家收费标准按照相关规定执行。衷心祝愿宝宝早日康复罕见病疑难病多学科联合门诊',
							success: function(res) {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/register/doctorList?hos_code=' +
											panel.hos_code +
											'&dep_id=' +
											panel.dep_id,
									})
								}
							},
						})
						break
					case 123456:
						uni.showModal({
							title: '提示',
							content: '尊敬的家长：1. 发热患儿首诊需到发热门诊就诊。2. 请遵守“一人一陪护”制度，进入诊区请佩戴口罩，出示健康码、行程码、测体温，完成流行病学史调查。3. 请按预约时间就诊，积极配合工作人员进行分诊，迟到30分钟，系统将自动取消预约号。4. 就诊过程中保持安静请勿大声喧哗。5. 特需诊查费：300元/人次。特需门诊',
							success: function(res) {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/register/doctorList?hos_code=' +
											panel.hos_code +
											'&dep_id=' +
											panel.dep_id,
									})
								}
							},
						})
						break
					case 'A000152':
						uni.showModal({
							title: '提示',
							content: '尊敬的家长，儿童保健科预防接种室服务范围如下,请详读：1、接种年龄：出生1月龄的新生儿 --18周岁以内的儿童2、接种区域：无区域、户籍、国籍限制3、接种疫苗：目前除卡介苗及儿童新冠疫苗以外，所有免疫规划疫苗和非免疫规划疫苗都可接种',
							success: function(res) {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/register/doctorList?hos_code=' +
											panel.hos_code +
											'&dep_id=' +
											panel.dep_id,
									})
								}
							},
						})
						break
					default:
						uni.navigateTo({
							url: '/pages/register/departlist?hos_code=' + panel.hos_code,
						})
						break
				}
			},
			openTipPopup() {
				let lastTime = uni.getStorageSync('lastTime')
				let nowTime = new Date().getTime()
				console.log('lastTime', lastTime)
				if (lastTime && (nowTime - lastTime < 60 * 60 * 1000)) {
					return
				} else {
					uni.setStorageSync('lastTime', nowTime)
					this.$refs.tipPopup.open()
				}
				// console.log(this.$refs.tipPopup)
				// this.$refs.tipPopup.open()
			},
			closeTipPopup() {
				this.$refs.tipPopup.close()
				// if(this.isCanClose){
				// 	this.$refs.tipPopup.close()
				// 	this.isCanClose = false
				// } else {
				// 	uni.showToast({
				// 		title: '请下拉到底阅读后按确定',
				// 		icon: 'none',
				// 		duration: 2000
				// 	})
				// }
			},
			// handleScrolltolower(e) {
			// 	console.log('handleScroll', e)
			// 	setTimeout(() => {
			// 		this.isCanClose = true
			// 	}, 100)
			// },
		},
		mounted: function() {
			
			console.log('onMounted')
			this.openTipPopup()
		},
		created() {
			reportCmPV({
				title: '预约挂号'
			});
			gethosList().then((res) => {
				// res.data.data[1].dep_id = 123456
				this.hosList = res.data.data.filter(item => item.hos_code !== '871006')
				// var data1 = {
				//   hos_code: '871002',
				//   dep_id: 'A000158',
				//   hos_name: '罕见病疑难病多学科联合门诊(前兴院区)',
				//   hospital_addr: '昆明市西山区前兴路288号',
				// }
				// this.hosList.splice(0, 0, data1)
				// var data2 = {
				//   hos_code: '871003',
				//   dep_id: 'A000152',
				//   hos_name: '昆明市儿童医院(书林-预防接种)',
				//   hospital_addr: '昆明市西山区书林街28号',
				// }
				// this.hosList.splice(5, 0, data2)
			})
		},
	}
</script>
<style lang="scss" scoped>
	.top-40 {
		top: 40px;
	}

	.page-title {
		padding: 10px 15px;
		position: relative;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;
		background-color: #edd1d8;
		justify-content: space-around;
	}

	.page-title-tab {
		flex: 1;
		text-align: center;
		font-size: 1em;
		.blue {
			color: blue;
		}
	}

	.hosList {
		padding: 0 15px;
		padding-top: 1px;
	}

	.hosItem {
		border-radius: 10px;
		border: 1px solid #e5e5e5;
		margin-top: 10px;
		position: relative;
		padding: 10px 15px;
	}

	.hosItem-title {
		margin-right: 0.2em;
		line-height: 2.5em;
		font-size: 1.125em;
		color: rgb(238, 102, 138);
		border-bottom: 1px solid #e5e5e5;
		margin-bottom: 10px;
	}

	.hosItem-tolink {
		color: rgb(238, 102, 138);
		border: 1px solid rgb(238, 102, 138);
		display: inline-block;
		padding: 0 1.32em;
		line-height: 2.3;
		font-size: 13px;
		border-radius: 5px;
	}

	.hosItem-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.hosItem-address {
		line-height: 2.5em;
		font-size: 1.125em;
		color: #666;
	}

	.popup-content{
		margin: 0 16px !important;
		background-color: #FFF;
		border-radius: 32rpx;
		overflow: hidden;
		// width: 80%;

		.popup-title{
			padding: 1.3em 1.6em 0.5em;
			font-size: 30rpx;
			line-height: 1.3;
			word-wrap: break-word;
			word-break: break-all;
			font-weight: 600;
			color: #333;
			text-align: left;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			

			&::after{
				content: '';
				position: absolute;
				display: block;
				width: 100%;
				height: 1rpx;
				bottom: 0;
				left: 0;
				background-color: #eee;
			}
		}

		.popup-scroll{
			// height: 600rpx;
			// scroll-view{
			// 	height: 100%;
			// }
		}
		.popup-text{
			display: flex;
			flex-direction: column;
			padding: 24rpx;
			font-size: 28rpx;
			line-height: 1.8em;
			overflow: scroll;
		}

		.popup-btns{
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 96rpx;
			border-top: 1rpx solid #eee;

			.popup-btn{
				flex: 1;
				text-align: center;
				line-height: 96rpx;
				font-size: 36rpx;
				color: #5f646e;
			}

			.primary{
				color: #fff;
				background-color: rgb(238,102,138);
				margin-bottom: -.1em;
			}

			.disabled{
				color: #fff;
				background-color: #DDDDDD;
			}
		}
	}
</style>