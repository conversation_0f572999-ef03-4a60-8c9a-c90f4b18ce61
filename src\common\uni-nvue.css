#ifndef APP-PLUS-NVUE
page {
  min-height: 100%;
  height: auto;
}
#endif 

/* 解决头条小程序字体图标不显示问题，因为头条运行时自动插入了span标签，且有全局字体 */
/* #ifdef MP-TOUTIAO */
/* text :not(view) {
    font-family: uniicons;
} */
/* #endif */

.uni-icon {
  font-family: uniicons;
  font-weight: normal;
}

.uni-container {
  padding: 15px;
  background-color: #f8f8f8;
}

.uni-header-logo {
  /* #ifdef H5 */
  display: flex;
  /* #endif */
  padding: 15px 15px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 10rpx;
}

.uni-header-image {
  width: 80px;
  height: 80px;
}

.uni-hello-text {
  margin-bottom: 20px;
}

.hello-text {
  color: #7a7e83;
  font-size: 14px;
  line-height: 20px;
}

.hello-link {
  color: #7a7e83;
  font-size: 14px;
  line-height: 20px;
}

.uni-panel {
  margin-bottom: 12px;
}

.uni-panel-h {
  /* #ifdef H5 */
  display: flex;
  /* #endif */
  background-color: #ffffff;
  flex-direction: row !important;
  /* justify-content: space-between !important; */
  align-items: center !important;
  padding: 12px;
  /* #ifdef H5 */
  cursor: pointer;
  /* #endif */
}
/*
.uni-panel-h:active {
    background-color: #f8f8f8;
}
 */
.uni-panel-h-on {
  background-color: #f0f0f0;
}

.uni-panel-text {
  flex: 1;
  color: #000000;
  font-size: 14px;
  font-weight: normal;
}

.uni-panel-icon {
  margin-left: 15px;
  color: #999999;
  font-size: 14px;
  font-weight: normal;
  transform: rotate(0deg);
  transition-duration: 0s;
  transition-property: transform;
}

.uni-panel-icon-on {
  transform: rotate(180deg);
}

.uni-navigate-item {
  /* #ifdef H5 */
  display: flex;
  /* #endif */
  flex-direction: row;
  align-items: center;
  background-color: #ffffff;
  border-top-style: solid;
  border-top-color: #f0f0f0;
  border-top-width: 1px;
  padding: 12px;
  /* #ifdef H5 */
  cursor: pointer;
  /* #endif */
}

.uni-navigate-item:active {
  background-color: #f8f8f8;
}

.uni-navigate-text {
  flex: 1;
  color: #000000;
  font-size: 14px;
  font-weight: normal;
}

.uni-navigate-icon {
  margin-left: 15px;
  color: #999999;
  font-size: 14px;
  font-weight: normal;
}
