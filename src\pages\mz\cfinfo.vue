<template>
  <view class="container">
    <view class="header">
      <view class="title">处方信息</view>
    </view>
    <view class="body">
      <view class="appoint_list" v-if="item.patient_name">
        <view class="appoint_list-info" >
          <view class="appoint_list-cent">
            <view class="appoint_list-cent-item" v-if="item.patient_name">
              <view class="patient_info">就诊人</view>
              <view class="patient_infoText">{{ new Array(item.patient_name.length).join('*') + item.patient_name.substr(-1)}}</view>
            </view>
            <view class="appoint_list-cent-item" v-if="item.doc_name">
              <view class="patient_info">开单医生</view>
              <view class="patient_infoText">{{ item.doc_name }}</view>
            </view>
            <view class="appoint_list-cent-item" v-if="item.bill_time">
              <view class="patient_info">开单时间</view>
              <view class="patient_infoText">{{ item.bill_time }}</view>
            </view>
          </view>
        </view>
      </view>

      <view class="detail-title-content" v-if="item.cf_list">
        <span class="detail-title" >处方详细信息</span>
      </view>

      <view class="cf-content-detail">
        <view class="item-detail" v-for="(it, index) in item.cf_list" :key="index">
          <uni-list>
            <uni-list-item title="药品名称" :rightText="it.name"/>
            <uni-list-item title="规格" :rightText="it.spec"/>
            <uni-list-item title="数量" :rightText="it.amount"/>
            <uni-list-item title="用量" :rightText="it.dosage_amount"/>
            <uni-list-item title="用量单位" :rightText="it.dosage_unit"/>
            <uni-list-item title="用法" :rightText="it.supply_name"/>
            <uni-list-item title="频次" :rightText="it.freq"/>
            <uni-list-item title="说明" :rightText="Array.isArray(it.struction) ?  it.struction.join('-') : it.struction"/>
          </uni-list>
        </view>
      </view>

    </view>
    <view class="footer"> </view>
  </view>
</template>
<script>
// import { defineComponent } from '@vue/composition-api'
import { cfDetailMz } from '@/api/ucenter.js'
export default {
  setup() {},
  onLoad(option) {
    //option为object类型，会序列化上个页⾯传递的参数
    // console.log(option.id)
    this.getDetail(option.id)
  },
  data() {
    return {
      item:{}
    }
  },
  methods: {
   async getDetail(id){
    const userInfo = uni.getStorageSync('userInfo')
      const { statusCode, data } = await cfDetailMz(null, {
        userid: userInfo.user_id,
        openid: userInfo.openid,
        id,
      })
      if (statusCode != 200) {
        uni.showToast({
          title: '请求网络失败',
          duration: 2000,
          icon: 'none',
        })
        return
      }
      if (data.code != 1) {
        uni.showToast({
          title: '暂无数据',
          duration: 2000,
          icon: 'none',
        })
        return
      }
      this.item = data.data

   }
  },
}
</script>
<style lang="scss" scoped>
.container{
  height: 100%;
  overflow: auto;
}
.content {
  overflow: auto;
  height: 100%;
}

.header {
  .title {
    line-height: 45px;
    text-align: center;
    color: #3d4145;
    background-color: #edd1d8;
    font-size: 17px;
  }
}


.body {
  .appoint_list {
    border-radius: 10px;
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
    margin: 30rpx;
  }
  .appoint_list-info {
    padding: 10px 15px;
  }
  .appoint_list-cent-item {
    display: flex;
    display: -webkit-flex;
    -webkit-align-items: center;
    justify-content: space-between;
    font-size: 0.9em;
    line-height: 2.5;
  }

  .detail-title-content {
    width: 65%;
    margin: 1em auto 0;
    line-height: 1em;
    font-size: 14px;
    text-align: center;
    border-top: 2px solid #ffffff;
  }
  .detail-title {
    position: relative;
    top: -0.9em;
    padding: 0 0.55em;
    color: #000;
  }

  .cf-content-detail{
    overflow: auto;
    height: 100%;
    padding: 15px;
    .item-detail{
      margin-bottom:15px ;
    }
  }


}
</style>
