# 使用 Node.js 18 作为基础镜像
FROM node:18-alpine

# 安装必要的系统依赖
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++ \
    wget

# 设置工作目录
WORKDIR /app

# 设置 npm 镜像源（提高下载速度）
RUN npm config set registry https://registry.npmmirror.com

# 安装 pnpm
RUN npm install -g pnpm

# 复制 package.json 和 lock 文件
COPY package*.json ./
COPY pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install

# 暴露端口
EXPOSE 8080

# 设置环境变量
ENV NODE_ENV=development
ENV UNI_PLATFORM=h5
ENV HOST=0.0.0.0
ENV PORT=8080

# 启动命令
CMD ["pnpm", "run", "dev:h5"]
