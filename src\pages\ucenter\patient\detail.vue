<template>
  <view class="patient_scroll">
    <view class="appoint_list">
      <view class="appoint_list-info">
        <view class="appoint_list-top">
          <!-- <view class="fl patient_name">{{new Array(patientDetaile.patient_name).join('*') + patientDetaile.patient_name.substr(-1)}}</view> -->
          <view class="fl patient_name">{{patientDetaile.patient_name}}</view>
          <view class="fl patient_type"
            >[{{ patientDetaile.patient_type == 0 ? '儿童' : '成人' }}]</view
          >
          <view v-if="!patientDetaile.tx_health_card" class="fr fc-ee668a changeInfo" @click="goto('/pages/ucenter/patient/editinfo?id=' + patientDetaile.id)">修改信息</view>
        </view>
        <view class="appoint_list-cent">
          <view class="appoint_list-cent-item">
            <view class="patient_info">出生日期</view>
            <view class="patient_infoText">{{ patientDetaile.patient_birthday }}</view>
          </view>
          <view class="appoint_list-cent-item">
            <view class="patient_info">手机号</view>
            <view class="patient_infoText" v-if="patientDetaile.patient_phone"
              >{{ patientDetaile.patient_phone.substr(0, 3) }}******{{ patientDetaile.patient_phone.substr(9) }}</view
            >
          </view>
          <view class="appoint_list-cent-item" >
            <view class="patient_info">身份证号</view>
            <view class="patient_infoText" v-if="patientDetaile.patient_idcard"
              >{{ patientDetaile.patient_idcard.substr(0, 1) }}*************{{
                  patientDetaile.patient_idcard.substr(patientDetaile.patient_idcard.length-1,1)
                }}</view
            >
          </view>
          <view class="appoint_list-cent-item">
            <view class="patient_info">ID号</view>
            <view class="patient_infoText">{{ patientDetaile.jz_card }}</view>
          </view>
        </view>
      </view>
      <view>
        <view class="head-nav">
          <view :class="navIndex == 1 ? 'activite' : ''" @click="checkIndex(1)">就诊卡</view>
          <view :class="navIndex == 0 ? 'activite' : ''" @click="checkIndex(0)">电子健康卡</view>
        </view>
        <view class="content" v-if="navIndex == 0">
          <view v-if="patientDetaile.tx_health_card" class="healthcard" 
            @click="goto('/pages/ucenter/patient/healthcard?id=' + patientDetaile.id+ '&type=2')">
            <view class="card-title">云南省卫生健康委员会</view>
            <view class="card-info">
              <view>{{ patientDetaile.patient_name }}</view>
              <view v-if="patientDetaile.patient_idcard"
                >{{ patientDetaile.patient_idcard.substr(0, 3) }}****{{
                  patientDetaile.patient_idcard.substr(14)
                }}</view
              >
            </view>
            <view class="card-img" >
              <image
                style="width: 100%; height: 100%"
                v-if="qrHealthCard.img"
                :src="'data:image/jpg;base64,' + qrHealthCard.img"
              ></image>
            </view>
          </view>

          <view v-else class="no-healthcard">
              <view class="btn-tip-box" >
                  <view class="create-btn" @click="createHealthCard">+申领电子健康卡</view>
                  <text class="tip">*未满182周天的新生儿可暂不用申领</text>
              </view>
          </view>
        </view>
        <view
          class="content content-img"
          v-if="navIndex == 1"
          style="padding: 15px; margin: 0 auto; text-align: center; display: flex"
        >
          <view style="justify-content: center; width: 160px; margin: 0 auto">
            <u-qrcode
              ref="qrcode"
              canvas-id="qrcode"
              size="160"
              :value="patientDetaile.jz_card"
            ></u-qrcode>
          </view>
        </view>
      </view>

      <view class="appoint_list-but">
        <view class="dp-f-1 appoint_but" @click="unBind(patientDetaile.id)">解绑</view>
        <view class="dp-f-1 appoint_but fc-ee668a" @click="setDefault(patientDetaile.id)"
          >默认就诊人</view
        >
      </view>
      <!-- <view class="appoint_list-but">
        <view class="dp-f-1 appoint_but fc-ee668a" @click="inform">新冠检测告知书</view>
        <view class="dp-f-1 appoint_but" @click="promise">新冠承诺书</view>
      </view> -->
    </view>
  </view>
</template>

<script>
  import { getMyPatientDetail, getQrCode, setDefaultPatient, unBindPatient } from '@/api/waiting.js'
  import { reqGetHealthCode } from '@/api/wechat.js'
  // import uQRCode from "u-qrcode";
  export default {
    //   components: { "u-qrcode": uQRCode },
    data() {
      return {
        navIndex: 1,
        patientDetaile: { 
          jz_card: '',
          patient_name: '',
         },
        qrHealthCard: {},
        size: 180,
      }
    },
    onLoad: function (option) {
      //option为object类型，会序列化上个页⾯传递的参数
      this.id = option.id
      this.getDetail(option.id)
    },
    methods: {
      
    goto(url) {
      console.log(url)
      uni.navigateTo({
        url,
      })
    },
      unBind(pat_id) {
        let that = this
        uni.showModal({
          title: '危险操作',
          content: '您确定要解绑该就诊人吗？',
          success: async function (res) {
            if (res.confirm) {
              /* uni.setStorageSync('userInfo', {
                id: 3,
                openid: 'o9U-Ft4X8xUVM3Sg9WqJX6H-qZhk',
                userid: 3,
              }) */
              const userInfo = uni.getStorageSync('userInfo')

              const { statusCode, data } = await unBindPatient(null, {
                pat_id: pat_id,
                userid: userInfo.user_id,
                openid: userInfo.openid,
              })
              if (statusCode != 200) {
                uni.showToast({
                  title: '网络超时',
                  duration: 2000,
                  icon: 'none',
                })
                return
              }
              if (data.code == 1) {
                let url = '/pages/ucenter/patient/patient'
                uni.navigateTo({
                  url,
                })
              } else {
                uni.showToast({
                  title: '操作失败',
                  duration: 2000,
                  icon: 'none',
                })
                return
              }
            } else if (res.cancel) {
              return
            }
          },
        })
      },
      inform() {
        if (this.patientDetaile.sign) {
          uni.showToast({
            title: '已经做过签名，且签名在有效期内',
            duration: 2000,
            icon: 'none',
          })
        } else {
          uni.navigateTo({
            url: '/pages/ucenter/covd_inform?id=' + this.id,
          })
        }
      },
      promise() {
        if (this.patientDetaile.confirm) {
          uni.showToast({
            title: '已经做过签名，且签名在有效期内',
            duration: 2000,
            icon: 'none',
          })
        } else {
          uni.navigateTo({
            url: '/pages/ucenter/covd_promise?id=' + this.id,
          })
        }
      },
      async setDefault(pat_id) {
        /* uni.setStorageSync('userInfo', {
          id: 3,
          openid: 'o9U-Ft4X8xUVM3Sg9WqJX6H-qZhk',
          userid: 3,
        }) */
        const userInfo = uni.getStorageSync('userInfo')

        const { statusCode, data } = await setDefaultPatient(null, {
          pat_id: pat_id,
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '网络超时',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        if (data.code == 1) {
          uni.showToast({
            title: '设置成功',
            duration: 2000,
            icon: 'none',
          })
          return
        } else {
          uni.showToast({
            title: '设置失败',
            duration: 2000,
            icon: 'none',
          })
          return
        }
      },
      async getDetail(pat_id) {
        /* uni.setStorageSync('userInfo', {
          id: 3,
          openid: 'o9U-Ft4X8xUVM3Sg9WqJX6H-qZhk',
          userid: 3,
        }) */
        const userInfo = uni.getStorageSync('userInfo')

        const { statusCode, data } = await getMyPatientDetail(null, {
          patientid: pat_id,
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '暂无数据',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        this.patientDetaile = data.data
        if (this.patientDetaile.tx_health_card && this.patientDetaile.tx_health_card.length > 5) {
          // #ifdef MP-ALIPAY
          this.getHQrCode(this.patientDetaile.tx_health_card)
          // #endif
          // #ifdef H5
          this.getHealthCode()
          // #endif
        } else {
          uni.showModal({
            title: '申领健康卡',
            content:
              '该就诊人还没有电子健康卡，请先申领电子健康卡，若无法线上申领的，请去医院患者服务中心进行现场办理！',
            success: (res) => {
              if (res.confirm) {
                // #ifdef MP-ALIPAY
                let url = '/pages/ucenter/patient/healthcard?id=' + pat_id
                uni.navigateTo({
                  url,
                })
                // #endif
                // #ifdef H5
                this.createHealthCard()
                // #endif
              } else {
                //console.log('取消申领')
              }
            },
          })
        }
      },
      createHealthCard() {
        let domain = window.location.host
        let apiAddr = '//' + location.host + '/api/';
        let redirect_uri = document.location.protocol + apiAddr + "Txhealthcard/cacheWechatCode?from=wx&env=test&patid=" + this.id + "&ui=ui";
        if (domain == "ertong.ynhdkc.com" || domain == "tertong.ynhdkc.com" ) {
            redirect_uri = document.location.protocol + apiAddr + "Txhealthcard/cacheWechatCode?from=wx&patid=" + this.id + "&ui=ui";
        }
        location.href = "https://health.tengmed.com/open/getUserCode?apiVersion=2&redirect_uri=" + encodeURIComponent(redirect_uri);
      },
      async getHQrCode(healthcard) {
        const { statusCode, data } = await getQrCode(null, {
          health_card: healthcard,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '获取电子健康卡失败',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        this.qrHealthCard = data.data
      },
      getHealthCode(){
        reqGetHealthCode({
          health_card: this.patientDetaile.tx_health_card,
          id_card: this.patientDetaile.patient_idcard,
          idcard_type: this.patientDetaile.idcard_type
        }).then((res) => {
          let rt = res.data
          if (rt.code == 1) {
            this.qrHealthCard = rt.data
          } else {
            uni.showToast({
              icon: 'none',
              title: rt.msg,
              duration: 2000,
            })
          }
        })
      },
      checkIndex(index) {
        this.navIndex = index
      },
    },
  }
</script>

<style lang="scss" scoped>
  .head-nav {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    z-index: 500;
    top: 0;
    width: 100%;
    background-color: #fafafa;
  }
  .head-nav > view {
    position: relative;
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    padding: 7px 0;
    text-align: center;
    font-size: 15px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    border-right: 1px solid #cccccc;
    color: #888;
  }
  .head-nav > view:last-child {
    border-right: none;
  }

  .head-nav > view.activite {
    background-color: white;
    border-bottom: 2px solid rgb(238, 102, 138);
    color: rgb(238, 102, 138);
  }
  .content {
    /* background: #008000; */
    height: 100%;
    padding: 0 15px;
  }
  .patient_scroll {
    padding: 15px;
  }
  .appoint_list {
    border-radius: 10px;
    margin-top: 10px;
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
  }
  .appoint_list-info {
    padding: 10px 15px;
  }
  .appoint_list-cent-item {
    display: flex;
    display: -webkit-flex;
    -webkit-align-items: center;
    justify-content: space-between;
    font-size: 0.9em;
    line-height: 2.5;
  }
  .appoint_list-top {
    border-bottom: 1px solid #e5e5e5;
    overflow: hidden;
    padding: 10px 0;
  }
  .patient_name {
    margin-right: 0.2em;
    line-height: 40px;
    font-size: 18px;
    color: #666;
  }
  .patient_type {
    color: #666;
    font-size: 0.8125em;
    margin-top: 14px;
    margin-right: 1em;
  }
  .changeInfo {
    margin-top: 8px;
  }
  .patient_info {
    color: #666;
    font-size: 0.8125em;
    font-weight: 400;
  }
  .patient_infoText {
    text-align: right;
    color: #999999;
  }
  .fl {
    float: left;
  }
  .fr {
    float: right;
  }
  .dp-f-1 {
    flex: 1;
  }
  .appoint_list-but {
    line-height: 50px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    border-top: 1px solid #d5d5d6;
  }
  .appoint_but {
    text-align: center;
    border-left: 1px solid #d5d5d6;
    color: #999999;
    font-size: 16px;
  }
  .appoint_list-but .dp-f-1:first-child {
    border: none;
  }
  .fc-ee668a {
    color: rgb(238, 102, 138);
  }
  .healthcard {
    width: 630rpx;
    margin: 0 auto;
    background: url('/ui/static/home/<USER>') no-repeat;
    height: 440rpx;
    background-size: contain;
    position: relative;
    background-position: center;

    .card-title {
      position: absolute;
      top: 80rpx;
      left: 30rpx;
      font-size: 20rpx;
    }

    .card-info {
      position: absolute;
      bottom: 100rpx;
      left: 40rpx;
    }
    .card-img {
      position: absolute;
      right: 40rpx;
      bottom: 100rpx;
      width: 150rpx;
      height: 150rpx;
      border: 1px solid #000;
    }
  }

  .no-healthcard{
    box-sizing: border-box;
    height: 100%;
    padding-bottom: 10px;
    padding-top: 20px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;

    .btn-tip-box{
      padding: 15px;
      display: flex;
      align-items: center;
      flex-direction: column;

      .create-btn{
        width: 100%;
        height: 50px;
        background-color: #ee668a;
        color: #fff;
        text-align: center;
        line-height: 50px;
        font-size: 16px;
        border-radius: 5px;
      }

      .tip{
        color: #666;
        margin-top: 20px;
      }
    }
  }
  .u-qrcode {
    margin: 0 auto;
  }
</style>
