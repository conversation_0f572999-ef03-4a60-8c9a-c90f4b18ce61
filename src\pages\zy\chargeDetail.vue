<template>
    <view class="charge-detail-page">
        <view class="header"> </view>
        <view class="body">
            <view class="charge-detail-info">
                <view class="charge-detail-info-bd">
                    <view class="charge-detail-info-item">
                        <text class="charge-detail-info-label">订单号</text>
                        <text class="charge-detail-info-value">{{ chargeDetail.order_no }}</text>
                    </view>
                    <view class="charge-detail-info-item">
                        <text class="charge-detail-info-label">住院号</text>
                        <text class="charge-detail-info-value">{{ chargeDetail.inpatientno }}</text>
                    </view>
                    <view class="charge-detail-info-item">
                        <text class="charge-detail-info-label">患者ID号</text>
                        <text class="charge-detail-info-value">{{ chargeDetail.jz_card }}</text>
                    </view>
                    <view class="charge-detail-info-item">
                        <text class="charge-detail-info-label">充值金额</text>
                        <text class="charge-detail-info-value">{{ chargeDetail.real_pay_money }}</text>
                    </view>
                </view>
            </view>
            <view v-if="chargeDetail.res_msg" class="bottom-tips">
                <span style="font-size:13px;color: rgb(238,102,138);">{{ chargeDetail.res_msg }}</span>
            </view>
        </view>

        <button type="primary" class="my-button" @click="goList"> 返回住院列表 </button>
    </view>
</template>

<script>
import { getChargePayDetail } from '@/api/ucenter.js'

export default {
    setup() { },
    onLoad(query) {
        this.orderId = query.id || '';
        this.getChargeDetail();
    },
    onShow(options) {

    },
    data() {
        return {
            orderId: '',
            chargeDetail: {},
        }
    },
    methods: {
        goList() {
            uni.navigateTo({ url: '/pages/zy/index' })
        },
        getChargeDetail() {
            getChargePayDetail({ id: this.orderId }).then(res => {
                if (res.data.code == 1) {
                    this.chargeDetail = res.data.data;
                } else {
                   uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000,
                    })
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.charge-detail-page {
    padding: 24px;
    .charge-detail-info {
        border-radius: 10px;
        border: 1px solid #e5e5e5;

        &-bd {
            padding: 5px 10px;
            font-size: .9em;
            text-align: right;
            color: #999999;
            line-height: 2;
        }

        &-item {
            overflow: hidden;
        }

        &-value {
            display: block;
            overflow: hidden;
            word-break: normal;
            word-wrap: break-word;
        }

        &-label {
            float: left;
            margin-right: .2em;
            line-height: 2.5em;
            font-size: 1.125em;
            color: #666;
            text-align: justify;
            text-align-last: justify;
        }
    }

    .bottom-tips {
        margin-top: 10px;
        position: relative;
        overflow: hidden;
    }

    .my-button {
        margin-top: 16px;
      color: #fff;
      background-color: rgb(238, 102, 138);
    }
}
</style>