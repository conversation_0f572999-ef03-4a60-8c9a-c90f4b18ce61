<template>
  <view class="">
    <headerBAr title="个人中心"></headerBAr>
    <view class="code-box">
      <text>就诊码</text>
      <view @click="codePopup" class="code-content" style="">
        <view style="justify-content: center; width: 60px; margin: 0 auto">
          <u-qrcode  v-if="item.jz_card"
            ref="qrcode"
            canvas-id="qrcode"
            size="60"
            :value="item.jz_card"
          ></u-qrcode>
        </view>
        <text>轻触展开</text>
      </view>
    </view>
    <view class="appoint_header">{{ item.appoint_status_desc }}</view>
    <view class="appoint_list">
      <view class="appoint_list-info">
        <view class="appoint_list-cent">
          <view class="appoint_list-cent-item">
            <view class="patient_info">订单号</view>
            <view class="patient_infoText">{{ item.order_no }}</view>
          </view>
          <view class="appoint_list-cent-item">
            <view class="patient_info">就诊人</view>
            <view class="patient_infoText">{{  item.patient_name }}</view>
          </view>
          <view class="appoint_list-cent-item">
            <view class="patient_info">患者ID号</view>
            <view class="patient_infoText">{{ item.jz_card }}</view>
          </view>
          <view class="appoint_list-cent-item">
            <view class="patient_info">就诊日期</view>
            <view class="patient_infoText"
              >{{ item.sch_date }}</view
            >
          </view>
          <view class="appoint_list-cent-item">
            <view class="patient_info">就诊时间</view>
            <view class="patient_infoText">{{ item.start_time }} - {{ item.end_time }}</view>
          </view>
          <view class="appoint_list-cent-item">
            <view class="patient_info">就诊医生</view>
            <view class="patient_infoText">{{ item.doc_name }}</view>
          </view>
          <view class="appoint_list-cent-item">
            <view class="patient_info">就诊院区</view>
            <view class="patient_infoText">{{ item.hos_name }}</view>
          </view>
          <view class="appoint_list-cent-item">
            <view class="patient_info">就诊科室</view>
            <view class="patient_infoText">{{ item.dep_name }}</view>
          </view>
          <!-- <view class="appoint_list-cent-item">
            <view class="patient_info">预约时间</view>
            <view class="patient_infoText">{{ item.appoint_time }}</view>
          </view> -->
          <view class="appoint_list-cent-item">
            <view class="patient_info">签到地址</view>
            <view class="patient_infoText">{{ item.msg_content }}</view>
          </view>
          <view v-if="item.cancel_time" class="appoint_list-cent-item">
            <view class="patient_info">取消时间</view>
            <view class="patient_infoText">{{ item.cancel_time }}</view>
          </view>
          <view  v-if="item.cancel_msg" class="appoint_list-cent-item">
            <view class="patient_info">取消原因</view>
            <view class="patient_infoText">{{ item.cancel_msg }}</view>
          </view>
          <view class="appoint_list-cent-item notify">
            温馨提示：请按照预约时段携带患者提前30分钟相应分诊台签到候诊,迟到30分钟后系统将号源自动后延。如超过预约时间1小时视为爽约,多次爽约会影响其就诊信誉，因各种原因预约不能就诊者,请在预约就诊时间前在原预约渠道进行取消退号。预约时间不代表准点就诊,具体就诊时间以实际到院就诊情况为准。如患者有发热、咳嗽、腹泻、黄疸和皮疹等症状请先到门诊一楼预检分诊处,进行预检分诊。
          </view>
        </view>
      </view>
    </view>
    <!-- <view class="appoint-warn" v-if="item.msg_content">{{ item.msg_content }}</view> -->
    <view class="uni-padding-wrap uni-common-mt" v-if="item.cancel_enable">
      <button type="primary" @click="cancel">取消订单</button>
    </view>
     <view class="uni-padding-wrap uni-common-mt" v-if="item.pre_outpatient_enable">
      <button type="primary" @click="openPreOutPatient">智能预问诊</button>
    </view>
     <view class="uni-padding-wrap uni-common-mt">
      <button type="primary" @click="backLast">返回预约列表</button>
    </view>

    <uni-popup ref="codePopup" type="center" :animation="true">
      <view class="code-popup">
        <view class="title">就诊码</view>
        <view class="code-con">
          <u-qrcode v-if="item.jz_card"
            ref="qrcode1"
            canvas-id="qrcode1"
            size="270"
            :value="item.jz_card"
          ></u-qrcode>
        </view>
        <view class="tip" @click="closeCodePopup">关闭</view>
      </view>
    </uni-popup>

    <uni-popup ref="cancelMsgPopup" type="dialog">
      <uni-popup-dialog mode="input" title="取消原因" placeholder="请输入取消原因" :duration="2000" :before-close="true" @close="closeMsg" @confirm="confirmMsg"></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
  import { getRecordDetail,cancelAppoint,getMsg } from '@/api/waiting.js'
  import headerBAr from '@/pages/component/header_bar/header_bar.vue'
import { reqCancelAppoint } from '@/api/wechat'
import dayjs from 'dayjs'
  const monitor = require('@/to/alipayLogger.js')


  export default {
    components: {
      headerBAr,
    },
    data() {
      return {
        oid: '',
        item: {
          patient_name: '',
        },
      }
    },
    // mounted:{},
    onLoad: function (option) {
      var loginRes = this.checkLogin();
			console.log(loginRes)
			if (!loginRes) {
				return false;
			}
      this.oid = option.id
      this.getDetail(option.id)
      monitor.api({api:"挂号记录查询",success:true,c1:"taSR_YL",time:200})
    },
    methods: {
      cancel() {
        let that = this
        uni.showModal({
            title: '提示',
            content: '您确定要取消预约吗？请谨慎操作！',
            success: function (res) {
              if (res.confirm) {
                // #ifdef MP-ALIPAY
                my.prompt({
                  title:"取消预约",
                  message:"请填写取消预约的原因",
                  success: (res) => {
                    if (res.ok) {
                      let query={
                        id: that.oid,
                        userid: uni.getStorageSync('userInfo').id,
                        openid: uni.getStorageSync('userInfo').openid,
                        cancel_msg: res.inputValue
                      }
                      cancelAppoint(query).then((res) => {
                        if (res.data.code == 1) {
                          let msgQuery = {
                            openid: uni.getStorageSync('userInfo').openid,
                            order_id: that.oid,
                            patient_name: uni.getStorageSync('userInfo').user_name,
                            accessToken: uni.getStorageSync('authorizationCode')||uni.getStorageSync('userInfo').access_token,
                          }
                          getMsg(msgQuery).then((ret) => {
                            if(ret.data.code == 1){
                              console.log(ret.data.data)
                            }
                          })
                          uni.showModal({
                            title:"提示",
                            content:res.data.msg,
                            success: function (res) {
                              if (res.confirm) {
                                that.getDetail(that.oid)
                              }
                            }
                          })
                        }else{
                          uni.showModal({
                            title:"提示",
                            content:res.data.msg
                          })
                        }
                      })
                    }
                  }
                })
                // #endif

                // #ifdef H5
                that.$refs.cancelMsgPopup.open()
                // #endif
              }
            },
          })
      },
      closeMsg() {
        this.$refs.cancelMsgPopup.close()
      },
      confirmMsg(value){
        console.log(value)
        if(!value){
          uni.showToast({
            title: '请输入取消原因',
            icon: 'none',
            duration: 2000,
          })
          return
        }
        let query={
          id: this.oid,
          userid: uni.getStorageSync('userInfo').id,
          openid: uni.getStorageSync('userInfo').openid,
          cancel_msg: value
        }

        reqCancelAppoint(query).then((res) => {
          if (res.data.code == 1) {
            this.getDetail(this.oid)
            this.$refs.cancelMsgPopup.close()
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000,
            })
          }
        })
      },
      async getDetail(id) {
        /* uni.setStorageSync('userInfo', {
          id: 3,
          openid: 'o9U-Ft4X8xUVM3Sg9WqJX6H-qZhk',
          userid: 3,
        }) */
        const userInfo = uni.getStorageSync('userInfo')

        const { statusCode, data } = await getRecordDetail(null, {
          id: id,
          userid: userInfo.user_id,
          openid: userInfo.openid,
        })
        if (statusCode != 200) {
          uni.showToast({
            title: '暂无数据',
            duration: 2000,
            icon: 'none',
          })
          return
        }
        this.item = data.data
        if (this.item.pre_outpatient == 0) {
          this.item.pre_outpatient_enable = 1;
        }
        if (this.item.cancel_time) {
          this.item.cancel_time = dayjs(this.item.cancel_time * 1000).format('YYYY-MM-DD HH:mm')
        }
      },
      codePopup() {
        this.$refs.codePopup.open()
      },
      closeCodePopup() {
        this.$refs.codePopup.close()
      },
      openPreOutPatient() {
        uni.showModal({
            title: '提醒',
            content: '您即将使用智能预问诊服务，预问诊过程中的内容均由AI生成，仅供参考。如有疑问请咨询医生。',
            cancelText: '取消',
            confirmText: '确定',
            success:  (res) => {
              if (res.confirm) {
                window.location.href = "https://ertong.ynhdkc.com/ai-agent/pre-outpatient/?appointId=" + this.item.order_id;
              } else if (res.cancel) {
              }
            },
          })
      },
      backLast() {
        uni.redirectTo({
           url: '/pages/ucenter/appoint/appoint'
        });
      },
    },
  }
</script>

<style lang="scss" scoped>
  .uni-padding-wrap {
    padding: 6px;
    z-index: 10;
    position: relative;

    >button{
      background-color: rgb(238, 102, 138);
    }
  }

  .code-box{
    padding: 15px;
    display: flex;
    align-items: center;
    background-color: #FFEEEE;
    margin-bottom: 4px;
    gap: 100px;

    .code-content{
      display: flex;
      align-items: center;
      flex-direction: column;
      background-color: #FFEEEE;

      >text{
        font-size: 12px;
        color: rgb(238, 102, 138);
        margin-top: 5px;
      }
    }
  }

  .code-popup{
    padding: 15px;
    display: flex;
    align-items: center;
    flex-direction: column;
    background-color: #FFF;
    width: 300px;
    margin: 0 auto;
    border-radius: 16px;
    gap: 10px;

    .title{
      color: rgb(238, 102, 138);
      font-size: 26px;
      font-weight: 600;
      margin-bottom: 10px;
    }

    .code-con{
      display: flex;
      align-items: center;
      flex-direction: column;
      background-color: #FFEEEE;
    }

    .tip{
      color: #666;
      width: 100%;
      font-size: 16px;
      padding-top: 10px;
      text-align: center;
    }
  }

  .appoint_header {
    line-height: 45px;
    padding-left: 15px;
    color: #3d4145;
    background-color: #edd1d8;
    font-size: 17px;
  }
  .appoint_list {
    border-radius: 10px;
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
    margin-bottom: 12px;
  }
  .appoint_list-info {
    padding: 10px 15px;
  }
  .appoint_list-cent-item {
    display: flex;
    display: -webkit-flex;
    -webkit-align-items: center;
    justify-content: space-between;
    font-size: 0.9em;
    line-height: 2.5;
  }
  .notify {
    color: rgb(238, 102, 138);
  }
  .patient_info {
    color: #666;
    font-size: 0.8125em;
    font-weight: 400;
  }
  .patient_infoText {
    text-align: right;
    color: #999999;
  }
  .appoint-warn {
    background-color: #ffffff;
    margin-top: 10px;
    position: relative;
    overflow: hidden;
    padding: 5px 15px;
    font-size: 13px;
    color: #ee668a;
  }
</style>
