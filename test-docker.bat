@echo off
echo 🐳 Docker 环境测试
echo.

echo 📋 检查 Docker 版本...
docker --version
echo.

echo 📋 检查 Docker Compose 版本...
docker-compose --version
echo.

echo 📋 检查当前运行的容器...
docker ps
echo.

echo 📋 检查 uni-app 开发容器状态...
docker ps | findstr "ertong-uni-app-dev"
if %errorlevel% equ 0 (
    echo ✅ 开发容器正在运行
    echo 📱 访问地址: http://localhost:8080/ui/
    echo.
    echo 📋 容器日志 (最后10行):
    docker logs ertong-uni-app-dev --tail 10
) else (
    echo ❌ 开发容器未运行
    echo 💡 启动命令: docker-run.bat dev
)

echo.
echo 📋 检查端口占用情况...
netstat -an | findstr ":8080"

echo.
echo 🎯 测试完成！
pause
