<template>
  <div class="page-index">
    <div class="hd">
      <div class="info">
        <div class="info-hd">付款给</div>
        <div class="info-bd">昆明市儿童医院医院</div>
      </div>
      <div class="info-icon">
        <img class="info-icon-img" src="../../assets/logo.png" alt="">
      </div>
      <div class="bg"></div>
    </div>
    <div class="bd">
      <div class="box">
        <div class="box-hd">
          <div class="box-hd-label">费用总额</div>
          <div class="box-hd-value">{{mi_order.total_fee}}元</div>
        </div>
        <div class="box-bd">
          <div class="box-item">
            <div class="box-bd-label">医保基金支付</div>
            <div class="box-bd-value">{{mi_order.fund_pay}}元</div>
          </div>
          <div class="box-item">
            <div class="box-bd-label">个人帐户支付</div>
            <div class="box-bd-value">{{mi_order.psn_acct_pay}}元</div>
          </div>
          <div class="box-item">
            <div class="box-bd-label">其他抵扣金额</div>
            <div class="box-bd-value">{{mi_order.cash_reduced_fee}}元</div>
          </div>
        </div>
        <div class="box-ft">
          <div class="box-ft-label">现金支付</div>
          <div class="box-ft-value">{{mi_order.own_pay_amt}}元</div>
        </div>
      </div>
      <div class="bd-append">
        <i class="bd-append-icon"></i>
        <div class="bd-append-text">医保移动支付</div>
      </div>
    </div>
    <div class="ft">
      <div class="pay">
        <div class="pay-label">您还需支付：</div>
        <div class="pay-value">¥{{mi_order.own_pay_amt}}</div>
      </div>
      <div class="btn" @click="ali_med_pay_order">去支付</div>
    </div>
  </div>
</template>

<script>
import {aliMedHisOrder, aliMedPayOrder} from "@/api/waiting";

export default {
  setup: {},
  onLoad(query) {
    this.ali_med_hos_order(query.order_id)
  },
  data() {
    return {
      mi_order: {},
      mz_order: {},
    }
  },
  methods: {
    myGetSystemInfo() {
      return new Promise((resolve, reject) => {
        my.getSystemInfo({
          success: resolve,
          fail: reject
        });
      });
    },
    myGetSetting() {
      return new Promise((resolve, reject) => {
        my.getSetting({
          success: resolve,
          fail: reject
        });
      });
    },
    myOpenSetting() {
      return new Promise((resolve, reject) => {
        my.openSetting({
          success: resolve,
          fail: reject
        });
      });
    },
    myAlert(content) {
      return new Promise((resolve, reject) => {
        my.alert({
          content,
          success: resolve,
          fail: reject
        });
      });
    },
    // 获取用户是否开启系统定位及授权支付宝使用定位
    async isLocationEnabled() {
      const systemInfo = await this.myGetSystemInfo();
      return !!(systemInfo.locationEnabled && systemInfo.locationAuthorized);
    },
    // 若用户未开启系统定位或未授权支付宝使用定位，则跳转至系统设置页
    async showAuthGuideIfNeeded() {
      if (!(await this.isLocationEnabled())) {
        my.showAuthGuide({
          authType: "LBS"
        });
        return false;
      }
      return true;
    },

    // 获取用户是否授权过当前小程序使用定位
    async isLocationMPAuthorized () {
      const settingInfo = await this.myGetSetting();
      return settingInfo.authSetting.location === undefined || settingInfo.authSetting.location;
    },

    // 若用户未授权当前小程序使用定位，则引导用户跳转至小程序设置页开启定位权限
    async requestLocationPermission() {
      await myAlert("如果用户之前拒绝授权当前小程序获取地理位置权限，将会弹出此弹窗，请根据需要替换文案。");
      const openSettingInfo = await this.myOpenSetting();
      return openSettingInfo.authSetting.location;
    },
    async authGuideLocation() {
      try {
        if (!(await this.showAuthGuideIfNeeded())) {
          return false;
        }
        if (await this.isLocationMPAuthorized()) {
          return true;
        }
        if (await this.requestLocationPermission()) {
          return true;
        }
        return false;
      } catch (error) {
        console.error(error);
        return false;
      }
    },
    ali_med_hos_order(order_id) {
      const userInfo = uni.getStorageSync('userInfo')
      console.log("开始进行支付宝医保支付医保下单流程")
      this.authGuideLocation().then(res => {
        if (res === true) {
          my.getLocation({
            type: 0, // 获取经纬度和省市区县数据
            success: (res) => {
              console.log(res)
              aliMedHisOrder({
                accessToken: uni.getStorageSync('mfrstreCode')||uni.getStorageSync('userInfo').access_token,
                user_id: userInfo.user_id,
                openid: userInfo.openid,
                order_id: order_id,
                lon: res.longitude,
                lat: res.latitude
              }).then((res) => {
                this.mi_order = res.data.data.miOrder
                this.mz_order = res.data.data.mzPayOrder
              }).catch((err) => {
                console.log(err)
              })
            },
            fail: (error) => {
              console.error('定位失败: ', JSON.stringify(error));
            },
          });
        }
      })
    },
    ali_med_pay_order() {
      const userInfo = uni.getStorageSync('userInfo')
      aliMedPayOrder({
        accessToken: uni.getStorageSync('mfrstreCode')||uni.getStorageSync('userInfo').access_token,
        user_id: userInfo.user_id,
        openid: userInfo.openid,
        order_id: this.order_id
      }).then((res) => {
        console.log(res)
        my.tradePay({
          orderStr: res.data.data,
          success: (result) => {
            uni.navigateTo({url: `/pages/mz/index?type=1`})
          },
          fail: (error) => {
            my.alert({content: '医保支付失败或已取消'})
            console.log(JSON.stringify(error))
            uni.navigateTo({url: `/pages/mz/index`})
          }
        });
      }).catch((err) => {
        console.log(err)
      })
    }
  }
}
</script>

<style scoped>
@import 'medins.css';
</style>
