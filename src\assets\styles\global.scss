.container {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.top-40 {
  margin-top: 40px;
}

/* #ifdef MP-ALIPAY */
.uni-data-tree-input {
  .input-value {
    justify-content: space-between;
  }
}

.uni-data-pickerview {
  .selected-area {
    flex: none !important;
    -webkit-flex: none !important;
  }
}

/* #endif */

.uni-modal{
  border-radius: 16px !important;
}
.uni-modal__btn_primary{
  background-color: rgb(238, 102, 138);
  color: #FFF !important;
}

.uni-popup-dialog{
  overflow: hidden;
}
.uni-dialog-button + .uni-dialog-button{
  background-color: rgb(238, 102, 138);
  color: #FFF!important;

  >text{
    color: #FFF!important;
  }
}

.uni-popup__wrapper.center{
  width: 100%;

  >view{
    margin: auto;
  }
}


.scroll-view-sclrol {
  ::v-deep ::-webkit-scrollbar {
    /* 滚动条整体样式 */
    display: block;
    width: 10rpx !important;
    height: 0 !important;
    -webkit-appearance: auto !important;
    background: transparent;
    overflow: auto !important;
    cursor: pointer;
    }
    
    ::v-deep ::-webkit-scrollbar-thumb {
    /* 滚动条里面的小方块 */
    border-radius: 10px !important;
    box-shadow: inset 0 0 5rpx rgb(238, 102, 138, 0.7) !important;
    background-color: rgba(238, 102, 138, 0.7) !important;
    }
    
    ::v-deep ::-webkit-scrollbar-track {
    /* 滚动条轨道 */
    // border-radius: 10rpx !important;
    // box-shadow: inset 0 0 5rpx rgba(0, 0, 0, 0.2) !important;
    // background-color: #ededed !important;
    }
}
  
