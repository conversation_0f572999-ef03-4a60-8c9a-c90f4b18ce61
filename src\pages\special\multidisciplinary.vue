<template>
	<view class="multidisciplinary-page">
		<headerBAr title="多学科联合门诊"></headerBAr>

		<view class="dep-list">
			<view class="dep-item" @click="goDoc(item)" v-for="(item, index) in specialList" :key="index">
				<view class="title">{{ item.department_name }}</view>
			</view>
		</view>

		<uni-popup ref="tipPopup" type="center" :animation="false">
			<view class="popup-content">
				<view class="popup-title">温馨提示</view>
				<view class="popup-scroll" >
					<scroll-view scroll-y>
					<view class="popup-text">
						<text>尊敬的家长，感谢您选择昆明市儿童医院多学科联合门诊：愿您有一个愉快的就诊过程。</text>
						<text>
							1.多学科联合门诊诊疗模式（MDT）是由至少三名取得高级职称三年以上的医师组成的专家团队为患者制定诊疗方案，
							<text style='color: #FF3F00;'>主要针对病情复杂、诊断困难或诊疗涉及多个学科的患者。</text>
						</text>
						<text>
							2.请至少提前五天预约，
							<text style='color: #FF3F00;'>预约挂号成功后将有医务人员和您电话联系，进行正式诊疗前的预诊，</text>
							完成病史采集、资料准备及专家安排等工作。
							<text style='color: #FF3F00;'>请注意接听电话，待医务人员和您确定就诊时间后，再按照约定时间到院就诊。</text>
						</text>
						<text>3.收费标准：①院内专家：每例诊疗三名专家收费400元，每增加一名专家加收100元。②省内外专家收费标准按照相关规定执行。</text>
						<text>衷心祝愿宝宝早日康复</text>
						<text style="text-align: right;">多学科联合门诊</text>
					</view>
					</scroll-view>
				</view>
				<view class="popup-btns">
					<view class="popup-btn" @click="closeTipPopup">取消</view>
					<view class="popup-btn primary" @click="closeTipPopup">确认</view>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="notifyPopup" type="center" :animation="false">
			<view class="popup-content">
				<view class="popup-title">温馨提示</view>
				<view class="popup-scroll" 
				style="max-height: 300px; min-height: 80px; overflow-y: scroll; height: auto;">
					<view class="popup-text">
						<view v-html="departmentNotify"></view>
					</view>
				</view>
				<view class="popup-btns">
					<view class="popup-btn" @click="closeNotifyPopup">取消</view>
					<view class="popup-btn primary" @click="confirmNotifyPopup">确认</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>

import headerBAr from '@/pages/component/header_bar/header_bar.vue'
import { reqSpecialDepList } from '@/api/wechat'
export default {
	components: {
		headerBAr,
	},
	data() {
		return {
			specialList: [],
			departmentNotify: '',
			itemInfo: {},
		}
	},
	methods: {
		goDoc(item) {
			this.itemInfo = item
			this.departmentNotify = item.department_notify
			if(this.departmentNotify){
				this.$refs.notifyPopup.open()
			}else{
				uni.navigateTo({
					url: '/pages/register/doctorList?hos_code=' + item.hospital_code + '&dep_id=' + item.department_code,
				})
			}
		},
		closeNotifyPopup(type) {
			this.$refs.notifyPopup.close();
		},
		confirmNotifyPopup() {
			this.$refs.notifyPopup.close();
			uni.navigateTo({
					url: '/pages/register/doctorList?hos_code=' + this.itemInfo.hospital_code 
					+ '&dep_id=' + this.itemInfo.department_code,
				})
		},
		getSpecialList() {
			reqSpecialDepList({
				type: 2
			}).then((res) => {
				console.log(res)
				this.specialList = res.data.data
			})
		},
		closeTipPopup(type) {
			this.$refs.tipPopup.close()
		}
	},
	onLoad() {
		console.log('多学科预约门诊')
		this.getSpecialList()
	},
	mounted() {
		this.$refs.tipPopup.open()
	},
}
</script>

<style lang="scss" scoped>
.multidisciplinary-page {
	min-height: 100vh;
    background-color: #FBF9FE;

	.dep-list {
		padding:60px 20px;

		.dep-item {
          border-radius: 8px;
          padding: 30px 20px;

              border: 1px solid #e5e5e5;
            color: rgb(238,102,138);
            font-size: 18px;

			.title{
			font-size: 18px;
			}
			// .btn{
			// padding: 2px 8px;
			// font-size: 14px;
			// border-radius: 8px;
			// color: #EE668A;
			// border: 1px #EE668A solid;
			// }
		
		}

		.dep-item + .dep-item {
			margin-top: 20px;
		}
	}

	.popup-content{
		margin: 0 auto;
		background-color: #FFF;
		border-radius: 32rpx;
		overflow: hidden;
		width: 80%;

		.popup-title{
			padding: 1.3em 1.6em 0.5em;
			font-size: 30rpx;
			line-height: 1.3;
			word-wrap: break-word;
			word-break: break-all;
			font-weight: 600;
			color: #333;
			text-align: left;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			

			&::after{
				content: '';
				position: absolute;
				display: block;
				width: 100%;
				height: 1rpx;
				bottom: 0;
				left: 0;
				background-color: #eee;
			}
		}

		.popup-scroll{
			height: 600rpx;
			scroll-view{
				height: 100%;
			}
		}
		.popup-text{
			display: flex;
			flex-direction: column;
			padding: 24rpx;
			font-size: 30rpx;
			line-height: 1.8em;
			overflow: scroll;
		}

		.popup-btns{
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 96rpx;
			border-top: 1rpx solid #eee;

			.popup-btn{
				flex: 1;
				text-align: center;
				line-height: 96rpx;
				font-size: 36rpx;
				color: #5f646e;
			}

			.primary{
				color: #fff;
				background-color: rgb(238,102,138);
				margin-bottom: -.1em;
			}

			.disabled{
				color: #fff;
				background-color: #D7D7D7;
			}
		}
	}
}
</style>