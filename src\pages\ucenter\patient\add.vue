<template>
  <view>
    <view class="appoint_header">家长/儿童建档</view>
    <view class="bind-form" style="padding: 15px">
      <uni-forms @modelValue="addPatientData">
        <uni-forms-item label="建档类型" required>
          <uni-data-checkbox
            v-model="addPatientData.patient_type"
            @change="changePatientType"
            :localdata="addtype"
          />
        </uni-forms-item>

        <uni-forms-item label="姓名" name="patient_name" required>
          <uni-easyinput
            type="text"
            v-model="addPatientData.patient_name"
            placeholder="请输入真实姓名"
          />
        </uni-forms-item>

        <uni-forms-item label="性别" required>
          <uni-data-checkbox v-model="addPatientData.patient_sex" :localdata="sexs" />
        </uni-forms-item>

        <uni-forms-item label="证件类型" required v-if="isHide">
          <uni-data-checkbox v-model="addPatientData.idcard_type" :localdata="idtypes" />
        </uni-forms-item>

        <uni-forms-item label="证件号" name="patient_idcard" >
          <uni-easyinput
            type="text"
            v-model="addPatientData.patient_idcard"
            placeholder="请输入对应证件号"
          />
        </uni-forms-item>

        <uni-forms-item label="出生日期" name="patient_birthday" required>
          <uni-datetime-picker
            type="date"
            :clear-icon="false"
            v-model="addPatientData.patient_birthday"
            @maskClick="maskClick"
          />
        </uni-forms-item>

        <uni-forms-item label="民族" name="patient_nation" required>
          <uni-data-picker
            placeholder="请选择民族"
            popup-title="请选择民族"
            :localdata="nation_info"
            v-model="addPatientData.patient_nation"
            @change="changeNation"
            @nodeclick="onnodeclickNation"
          ></uni-data-picker>
        </uni-forms-item>

        <uni-forms-item label="家庭地址" name="patient_city" required>
          <uni-data-picker
            placeholder="请选择地址"
            popup-title="请选择地址"
            :localdata="area_info"
            v-model="sel_area"
            @change="changeCity"
            @nodeclick="onnodeclickCity"
            :map="{ text: 'name', value: 'name' }"
          ></uni-data-picker>
        </uni-forms-item>
        <uni-forms-item label="详细地址" name="patient_address" required>
          <uni-easyinput
            type="text"
            v-model="addPatientData.patient_address"
            placeholder="家庭详细地址(如道路、小区、门牌号)"
          />
        </uni-forms-item>

        <uni-forms-item label="监护人关系" name="patient_relation" required v-if="isHide">
          <uni-data-picker
            placeholder="请选择"
            popup-title="请选择"
            :localdata="relations"
            v-model="addPatientData.patient_relation"
            @change="changePatientRelation"
            @nodeclick="onnodeclickPatientRelation"
          ></uni-data-picker>
        </uni-forms-item>

        <uni-forms-item label="监护人姓名" name="guarantee_name" required v-if="isHide">
          <uni-easyinput
            type="text"
            v-model="addPatientData.guarantee_name"
            placeholder="请填写监护人姓名"
          />
        </uni-forms-item>
        <uni-forms-item label="监护人身份证" name="guarantee_idcard" required v-if="isHide">
          <uni-easyinput
            type="text"
            v-model="addPatientData.guarantee_idcard"
            placeholder="请填写监护人身份证"
          />
        </uni-forms-item>
        <uni-forms-item label="联系电话" name="patient_phone" required>
          <uni-easyinput
            type="text"
            v-model="addPatientData.patient_phone"
            placeholder="请填写联系电话"
          />
        </uni-forms-item>
        <view class="form-txt-info">
          绑定个人信息后，获取医院提供健康信息及就诊相关信息查询服务。绑定时，医院需要通过您的实名认证信息查询医院健康卡帐户信息，您同意将您的姓名、身份证号、手机号提供给医院。本服务号提供的信息仅供参考，以医院实际提供的资料为准！同意本授权后，已代表您认真阅读并且接受医院服务号所有业务说明以及业务规则，对此产生争执以及纠纷医院免责。
        </view>
        <uni-forms-item>
          <uni-data-checkbox v-model="addPatientData.agree" :localdata="agrees" />
        </uni-forms-item>
        <button type="primary" class="my-button" @click="submit()"> 确定 </button>
      </uni-forms>
    </view>
  </view>
</template>
<script>
import { nationList } from './nation.js'
import { areaInfo } from './area.js'
import { createPatient } from '@/api/ucenter.js'
import { createUser } from '@/api/waiting'
import { reportCmPV } from '@/to/cloudMonitorHelper';
const monitor = require('@/to/alipayLogger.js')
export default {
  data() {
    return {
      isHide: true,
      nation_info: nationList,
      area_info: areaInfo,
      addtype: [
        { text: '儿童建档', value: 0 },
        { text: '家长建档', value: 1 },
      ],
      sexs: [
        { text: '男', value: 1 },
        { text: '女', value: 2 },
      ],
      agrees: [{ text: '阅读并同意授权', value: 'on' }],
      idtypes: [
        { text: '身份证', value: 0 },
        { text: '出生证明', value: 1 },
      ],
      relations: [
        // { text: '本人', value: '本人' },
        { text: '父亲', value: '父亲' },
        { text: '母亲', value: '母亲' },
        { text: '祖父', value: '祖父' },
        { text: '祖母', value: '祖母' },
        { text: '外祖父', value: '外祖父' },
        { text: '外祖母', value: '外祖母' },
        { text: '其他亲属', value: '其他亲属' },
      ],
      sel_area: '',
      addPatientData: {
        patient_type: 0,
        patient_name: '',
        // patient_name1: '',
        // patient_name1: '',
        patient_sex: 1,
        idcard_type: 0,
        patient_idcard: '',
        // patient_idcard1: '',
        patient_birthday: '',
        patient_nation: '',
        patient_city: '',
        patient_address: '',
        patient_relation: '',
        guarantee_name: '',
        guarantee_idcard: '',
        patient_phone: '',
        // patient_phone1: '',
        agree: '',
        userid: 0,
        openid: '',
        from: '',
      },
      userInfo:{}
    }
  },
  computed: {},
  onLoad(query) {
    reportCmPV({ title: '在线建档',query });
    var loginRes = this.checkLogin();

    if(query.from){
      this.from = query.from
    }
    console.log(loginRes)
		if(!loginRes){return false;}
  },
  methods: {
    changePatientType(e) {
      if (e.detail.value == 1) {

        this.isHide = false
        this.addPatientData.idcard_type = 0
        let _this = this
        // #ifdef MP-ALIPAY
        my.getAuthCode({
          scopes: 'auth_user',
          success: (res) => {
            let query = {
              code: res.authCode,
              grant_type: 'authorization_code',
            }
            createUser(query).then((res) => {
              uni.setStorageSync('userInfo', res.data.data.data)
              this.addPatientData.patient_name = res.data.data.data.user_name?res.data.data.data.user_name:null
              // this.addPatientData.patient_name1 = res.data.data.data.user_name?new Array(res.data.data.data.user_name.length).join('*') + res.data.data.data.user_name.substr(-1):null
              this.addPatientData.patient_idcard = res.data.data.data.cert_no?res.data.data.data.cert_no:null
              // this.addPatientData.patient_idcard1 = res.data.data.data.cert_no?res.data.data.data.cert_no.substr(0, 1) +'*************'+res.data.data.data.cert_no.substr(res.data.data.data.cert_no.length-1,1):null
              this.addPatientData.patient_phone = res.data.data.data.mobile?res.data.data.data.mobile:null
              // this.addPatientData.patient_phone1 = res.data.data.data.mobile?res.data.data.data.mobile.substr(0, 3) + '******' + res.data.data.data.mobile.substr(9):null
              var num = this.addPatientData.patient_idcard.charAt(16);

              if(num%2==0){
                //console.log('女');
                this.addPatientData.patient_sex = 2
              }else{
                //console.log('男');
                this.addPatientData.patient_sex = 1
              }
            })
          },
          fail: (resfail) => {
            // 用户取消授权
          },
        })
        // #endif
      } else {
        this.isHide = true;
        this.addPatientData.patient_name = ""
        this.addPatientData.patient_idcard = ""
        this.addPatientData.patient_phone = ""
      }
    },
    maskClick(e) {},
    changeNation(e) {},
    onnodeclickNation(e) {},
    changeCity(e) {
      let iArr = []
      e.detail.value.forEach((element) => {
        iArr.push(element.value)
      })
      this.addPatientData.patient_city = iArr.join(' ')
    },
    onnodeclickCity(e) {
      // console.log(e,222)
    },
    changePatientRelation() {},
    onnodeclickPatientRelation(e) {},

    submit() {
      // this.addPatientData.patient_phone1 = this.addPatientData.patient_phone
      // this.addPatientData.patient_name1 = this.addPatientData.patient_name
      // this.addPatientData.patient_idcard1 = this.addPatientData.patient_idcard
      if (!this.addPatientData.patient_name) {
        uni.showToast({
          icon: 'none',
          title: `姓名不能为空`,
        })
        return
      }
      if (this.addPatientData.patient_type == 1 &&!this.addPatientData.patient_idcard) {
        uni.showToast({
          icon: 'none',
          title: `家长建档证件号(身份证)不能为空`,
        })
        return
      }
      if (!this.addPatientData.patient_birthday) {
        uni.showToast({
          icon: 'none',
          title: `出生日期不能为空`,
        })
        return
      }
      if (!this.addPatientData.patient_nation) {
        uni.showToast({
          icon: 'none',
          title: `民族不能为空`,
        })
        return
      }
      if (!this.addPatientData.patient_city) {
        uni.showToast({
          icon: 'none',
          title: `家庭地址不能为空`,
        })
        return
      }
      if (!this.addPatientData.patient_address) {
        uni.showToast({
          icon: 'none',
          title: `详细地址不能为空`,
        })
        return
      }
      
      if (this.addPatientData.patient_type == 1) {
        this.addPatientData.patient_relation = '本人'
        this.addPatientData.guarantee_name = this.addPatientData.patient_name
        this.addPatientData.guarantee_idcard = this.addPatientData.patient_idcard
      }
      if (!this.addPatientData.patient_relation) {
        uni.showToast({
          icon: 'none',
          title: `监护人关系不能为空`,
        })
        return
      }
      if (!this.addPatientData.guarantee_name) {
        uni.showToast({
          icon: 'none',
          title: `监护人姓名不能为空`,
        })
        return
      }
      if (!this.addPatientData.guarantee_idcard) {
        uni.showToast({
          icon: 'none',
          title: `监护人身份证不能为空`,
        })
        return
      }
      if (!this.addPatientData.agree) {
        uni.showToast({
          icon: 'none',
          title: `必须同意授权`,
        })
        return
      }
      const userInfo = uni.getStorageSync('userInfo')
      if (!userInfo) {
        uni.showToast({
          icon: 'error',
          title: `未获取到用户信息，请重新登陆`,
        })
        return
      }
      this.addPatientData.openid = userInfo.openid
      this.addPatientData.userid = userInfo.user_id
      
      let patientAddress = this.addPatientData.patient_city + ' ' + this.addPatientData.patient_address
      createPatient({
        ...this.addPatientData,
        patient_address: patientAddress
      }).then((res) => {
        let rt = res.data
        if (rt.code == 1) {
          monitor.api({api:"在线建档",success:true,c1:"taSR_YL",time:200})
          uni.navigateBack({delta: 1})
        } else {
          uni.showToast({
            icon: 'none',
            title: rt.msg,
            duration: 3000,
          })
        }
      })
    },
  },
}
</script>

<style>
.appoint_header {
  line-height: 45px;
  text-align: center;
  color: #3d4145;
  background-color: #edd1d8;
  font-size: 17px;
}
.title {
  font-size: 17px;
  display: block;
  width: 105px;
  word-wrap: break-word;
  word-break: break-all;
  color: #ee668a;
  padding: 0;
  line-height: 1.47058824;
}
.form-txt-info {
  font-size: 13px;
  color: #666666;
}
.my-button {
  color: #fff;
  background-color: rgb(238, 102, 138);
  border: none;
}
</style>
