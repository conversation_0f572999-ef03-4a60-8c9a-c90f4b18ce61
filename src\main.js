import Vue from 'vue'
import App from './App'
import { createUser } from './api/waiting'
import { reqGetUserInfoWeChat, reqGetJsSign } from './api/wechat'
import { getUrlParam, delUrlParam } from './utils/utils'
import monitor from '/src/to/alipayLogger';
Vue.config.productionTip = false
// Vue.prototype.checkLogin = function (backpage, backtype) {
//   //定义一个全局函数
//   var userInfo = uni.getStorageSync('userInfo') //用户 id,
//   if (userInfo == '') {
//     //进行判断是否登录(登录成功以后会产生id,随机码,用户昵称,用户表情),如果没有值,则会返回登录界面
//     uni.navigateTo({ url: '../login/index?backpage=' + backpage + '&backtype=' + backtype })
//     return false
//   }
//   return [userInfo] //登录成功会返回这些登录成功的信息
// }
monitor.hookApp({
})

function wxAuth() {
  const data = {
    // 公众号appID
    appid: 'wx3df855bf9977df7b',
    // 授权后重定向的回调链接地址(需urlEncode对链接进行处理)
    redirect_uri: "https://ertong.ynhdkc.com/index/wechat/oauth_callback?weburl=" + encodeURIComponent(window.location.href),
    // 返回类型(默认code)
    response_type: 'code',
    // 应用授权作用域(根据自己需求来)
    scope: 'snsapi_userinfo',
    // 额外参数
    state: ''
  }

  let wxurl = 'https://open.weixin.qq.com/connect/oauth2/authorize';
  window.location.href = `${wxurl}?appid=${data.appid}&redirect_uri=${data.redirect_uri}&response_type=${data.response_type}&scope=${data.scope}&state=${data.state}#wechat_redirect`
}

Vue.prototype.checkLogin = function () {
  if (!uni.getStorageSync('userInfo')) {
    // #ifndef H5 
    // 支付宝小程序登录
    //如果没有登录,则会提示
    my.getAuthCode({
      scopes: ['nhsamp', 'auth_user', 'auth_base'],
      success: (res) => {
        let query = {
          code: res.authCode,
          grant_type: 'authorization_code',
        }
        console.log(createUser)
        createUser(query).then((res) => {
          uni.setStorageSync('userInfo', res.data.data.data)
        })
      },
      fail: (err) => {
        console.log(err)
        uni.showToast({
          title: '登录失败',
          icon: 'none',
        })
      },
    })
    // #endif
    // #ifdef H5
    // h5登录
    console.log('h5登录')
    const openid = getUrlParam('openid')
    console.log(openid)
    if (!openid) {
      console.log(openid)
      wxAuth()
    } else {
      reqGetUserInfoWeChat({ openid }).then((res) => {
        console.log(res)
        let obj = res.data.data
        // #ifdef H5 
        // 开始支付宝使用user_id, H5返回userid，统一使用user_id
        obj.user_id = obj.userid
        // #endif
        uni.setStorageSync('userInfo', res.data.data)
        window.location.href = delUrlParam(window.location.href, 'openid')
      })
    }
    // #endif
  }
  return uni.getStorageSync('userInfo')
}

Vue.prototype.configJsSign = function (url) {
  console.log("开始请求微信JS签名");
  reqGetJsSign({
    url: encodeURIComponent(url),
  }).then((res) => {
    console.log(res)
    // var shareData = $.extend(site.share_options, share_options);
    console.log("请求微信JS签名成功");
    console.log(res.data);
    console.log("开始微信配置");
    console.log(jWeixin);
    console.log(wx);
    jWeixin.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: res.data.data.appId, // 必填，公众号的唯一标识
        timestamp: res.data.data.timestamp, // 必填，生成签名的时间戳
        nonceStr: res.data.data.nonceStr, // 必填，生成签名的随机串
        signature: res.data.data.signature,// 必填，签名，见附录1
        jsApiList: ["getLocation","checkJsApi","chooseWXPay","closeWindow","openLocation","onMenuShareTimeline","onMenuShareAppMessage","onMenuShareQQ","onMenuShareWeibo","onMenuShareQZone","hideMenuItems"],
        openTagList: [ "wx-open-launch-weapp" ]
    });
    jWeixin.ready(function(){
    //    wx.onMenuShareAppMessage(shareData);
    //    wx.onMenuShareTimeline(shareData);
    //    wx.onMenuShareQQ(shareData);
    //    wx.onMenuShareWeibo(shareData);
    //    wx.onMenuShareQZone(shareData);
        jWeixin.hideMenuItems({
            menuList: ['menuItem:copyUrl','menuItem:openWithQQBrowser','menuItem:openWithSafari','menuItem:share:email','menuItem:share:brand'] // 要隐藏的菜单项，只能隐藏“传播类”和“保护类”按钮，所有menu项见附录3
        });
    //    wct.is_init = true;
    });
    jWeixin.error(function(res){
        console.log(res);
    });
  })
}
function loadVconsole() {
  if (/(localhost|192\.168|tertong)/.test(window.location.hostname)) {
    return import('vconsole').then(({ default: VConsole }) => {
      const vConsole = new VConsole()
    })
  }
  return Promise.resolve()
}
loadVconsole()
Vue.config.ignoredElements.push('wx-open-launch-weapp') 
App.mpType = 'app'

const app = new Vue({
  ...App,
})
app.$mount()
