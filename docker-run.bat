@echo off
setlocal enabledelayedexpansion

REM Docker 运行脚本 (Windows 版本)
REM 用法: docker-run.bat [dev|prod|stop|logs|clean]

set PROJECT_NAME=ertong-uni-app

if "%1"=="dev" (
    echo 🚀 启动开发环境...
    docker-compose -f docker-compose.dev.yml up --build -d
    echo ✅ 开发环境已启动
    echo 📱 访问地址: http://localhost:8080
    echo 📱 带路径访问: http://localhost:8080/ui/
    echo 📋 查看日志: docker-run.bat logs
    goto :end
)

if "%1"=="prod" (
    echo 🚀 启动生产环境...
    docker-compose -f docker-compose.prod.yml up --build -d
    echo ✅ 生产环境已启动
    echo 📱 访问地址: http://localhost
    echo 📱 带路径访问: http://localhost/ui/
    echo 📋 查看日志: docker-run.bat logs
    goto :end
)

if "%1"=="stop" (
    echo 🛑 停止所有容器...
    docker-compose -f docker-compose.dev.yml down 2>nul
    docker-compose -f docker-compose.prod.yml down 2>nul
    echo ✅ 所有容器已停止
    goto :end
)

if "%1"=="logs" (
    echo 📋 查看日志...
    docker ps | findstr "%PROJECT_NAME%-dev" >nul
    if !errorlevel! equ 0 (
        docker-compose -f docker-compose.dev.yml logs -f
    ) else (
        docker ps | findstr "%PROJECT_NAME%-prod" >nul
        if !errorlevel! equ 0 (
            docker-compose -f docker-compose.prod.yml logs -f
        ) else (
            echo ❌ 没有运行中的容器
        )
    )
    goto :end
)

if "%1"=="clean" (
    echo 🧹 清理 Docker 资源...
    docker-compose -f docker-compose.dev.yml down --rmi all --volumes --remove-orphans 2>nul
    docker-compose -f docker-compose.prod.yml down --rmi all --volumes --remove-orphans 2>nul
    docker system prune -f
    echo ✅ 清理完成
    goto :end
)

if "%1"=="restart" (
    echo 🔄 重启服务...
    call %0 stop
    timeout /t 2 /nobreak >nul
    call %0 dev
    goto :end
)

echo 用法: %0 [dev^|prod^|stop^|logs^|clean^|restart]
echo.
echo 命令说明:
echo   dev     - 启动开发环境 (端口 8080)
echo   prod    - 启动生产环境 (端口 80)
echo   stop    - 停止所有容器
echo   logs    - 查看容器日志
echo   clean   - 清理所有 Docker 资源
echo   restart - 重启开发环境
echo.
echo 示例:
echo   %0 dev    # 启动开发环境
echo   %0 logs   # 查看日志
echo   %0 stop   # 停止服务

:end
pause
