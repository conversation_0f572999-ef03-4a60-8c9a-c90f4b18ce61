<script>
  import monitor from '/src/to/alipayLogger';
  export default {
    onLaunch: function (options) {
      console.log(options)
      console.log('App Launch')
      // 公众号可能配置不同的页面入口，进入网页就判断授权状态，调用授权
      this.checkLogin();
    
      monitor.init({
        pid: "c2uh+uhk48aklqyniilyhw==",
        options: options,
        sample: 1,
        autoReportApi: true,
        autoReportPage: true,
        // Http请求返回数据中状态码字段名称
        code: ["xxx"],
        // Http返回数据中的error message字段名称
        msg: ["xxx"],
        miniVersion:'0.0.6'
      })
    },
    onShow: function () {
      console.log('App Show')  
    },
    onHide: function () {
      console.log('App Hide')
    },
  } 
</script>

<style lang="scss">
  /*每个页面公共css */
  @import './assets/styles/global.scss';
  @import './static/iconfont/iconfont.css';

</style>
