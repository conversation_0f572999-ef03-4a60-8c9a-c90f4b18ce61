<template>
    <view class="check-appoint-page">
        <headerBAr title="检查预约"></headerBAr>
        <div class="user-list" v-if="patientList.length">
            <div class="user-card" v-for="(user, index) in patientList" :key="index">
                <div class="user-name">{{ user.patient_name }}</div>
                <div class="info-item">
                    <span>出生日期</span>
                    <span>{{ user.patient_birthday }}</span>
                </div>
                <div class="info-item">
                    <span>手机号</span>
                    <span>{{ user.patient_phone }}</span>
                </div>
                <div class="info-item">
                    <span>ID号</span>
                    <span>{{ user.jz_card }}</span>
                </div>
                <div class="follow-btn" @click="openCheckAppoint(user)">
                    <text>检查预约</text>
                </div>
            </div>
        </div>
        <view class="no-data" v-else>
            <view class="no-data-content">
                <text class="no-data-content-tips">没有找到就诊人</text>
            </view>
        </view>
    </view>
</template>

<script>

import headerBAr from '@/pages/component/header_bar/header_bar.vue'
import { getMypatientList } from '@/api/waiting'
export default {
    components: {
        headerBAr,
    },
    data() {
        return {
            patientList: [],
        }
    },
    methods: {
        getPatientList() {
            const userInfo = uni.getStorageSync('userInfo')
            getMypatientList(null, {
                userid: userInfo.user_id,
                openid: userInfo.openid,
            }).then((res) => {
                console.log(res)
                this.patientList = res.data.data
            })
        },
        openCheckAppoint(item) {
            const timestamp = Date.now()
            window.location.href = `http://qyy.etyy.cn:8022/maqs/medicallist?hcode=&patientid=${item.jz_card}&openid=&clienttype=4&cardno=${item.jz_card}&idcar=${item.patient_idcard}&timestamp=${timestamp}&sign=`
        }
    },
    onLoad() {
        this.getPatientList()
    },
}
</script>

<style lang="scss" scoped>
.check-appoint-page {
    background-color: #F8F8F8;
    min-height: 100vh;

    .user-list {
        padding: 50px 15px;

        .user-card {
            border: 1px solid #eee;
            border-radius: 10px;
            padding:0 20px;
            margin-bottom: 20px;
            background-color: #FFFFFF;
        }

        .user-name {
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
            padding: 14px 0;
            border-bottom: 1px dashed #ccc;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            color: #666;
        }

        .follow-btn {
            text-align: center;
            margin-top: 15px;
            border-top: 1px dashed #ccc;
            padding: 14px;

            >text{
                color: #ff4466;
                text-decoration: none;
                font-weight: bold;
            }
        }

    }

    .no-data {
        padding-top: 60px;

        &-content {
            border-top: 1px solid #E5E5E5;
            width: 65%;
            margin: 1.5em auto;
            line-height: 1.6em;
            font-size: 14px;
            text-align: center;

            &-tips {
                position: relative;
                top: -0.9em;
                padding: 0 .55em;
                background-color: #FFFFFF;
                color: #999999;
                display: inline-block;
                vertical-align: middle;
            }
        }
    }
}
</style>