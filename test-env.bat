@echo off
echo Testing environment...
echo Node.js version:
node --version
echo.
echo NPM version:
"C:\Program Files\nodejs\npm.cmd" --version
echo.
echo Vue CLI Service exists:
if exist "node_modules\.bin\vue-cli-service.cmd" (
    echo YES - vue-cli-service.cmd found
) else (
    echo NO - vue-cli-service.cmd not found
)
echo.
echo Uni plugin exists:
if exist "node_modules\@dcloudio\vue-cli-plugin-uni" (
    echo YES - uni plugin found
) else (
    echo NO - uni plugin not found
)
echo.
echo Testing simple vue-cli-service command:
node_modules\.bin\vue-cli-service.cmd --version
pause
