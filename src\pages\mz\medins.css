.page-index {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}
.hd {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  color: #fff;
  padding: 32px 20px 20px 20px;
  box-sizing: border-box;
}
.bg {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 170px;
  background-color: #3B71E8;
  border-radius: 0 0 30px 30px;
  z-index: 0;
}
.info {
  position: relative;
  z-index: 1;
  color: #fff;
}
.info-hd {
  font-size: 14px;
  line-height: 20px;
  color: rgba(255, 255, 255, 0.6);
}
.info-bd {
  font-size: 18px;
  line-height: 25px;
  margin-top: 3px;
  font-weight: bold;
}
.info-icon {
  position: relative;
  z-index: 1;
  width: 40px;
  height: 40px;
}
.info-icon-img {
  display: block;
  width: 100%;
  height: 100%;
}
.bd {
  flex: 1;
  position: relative;
  z-index: 1;
}
.box {
  border-radius: 8px;
  background-color: #fff;
  margin: 0 20px;
  padding: 0 20px;
  padding-bottom: 17px;
  box-shadow: 0 3px 10px #E6E6E6;
}
.box-hd {
  position: relative;
  display: flex;
  justify-content: space-between;
  padding: 24px 0 18px;
  color: #606266;
}
.box-hd:before {
  content: '';
  position: absolute;
  left: -8px;
  bottom: 0;
  right: -8px;
  border-bottom: 1px dashed #D8D8D8;
  transform: scaleY(0.5);
}
.box-hd-label {
  font-weight: bold;
}
.box-hd-value {
  font-weight: bold;
}
.box-bd {
  padding-top: 19px;
}
.box-item {
  display: flex;
  justify-content: space-between;
  color: #909399;
  font-size: 14px;
  line-height: 20px;
  padding-bottom: 10px;
}
.box-item:last-child {
  padding-bottom: 0;
}
.box-ft {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
  padding-bottom: 17px;
  font-size: 16px;
  color: #3B71E8;
}
.box-ft-label {
  font-weight: bold;
}
.box-ft-value {
  font-weight: bold;
}
.box-append {
  position: relative;
  font-size: 14px;
  line-height: 20px;
  color: #606266;
  text-align: center;
  padding: 20px 0 3px;
}
.box-append:before {
  content: '';
  position: absolute;
  left: -8px;
  top: 0;
  right: -8px;
  border-top: 1px dashed #D8D8D8;
  transform: scaleY(0.5);
}
.bd-append {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 32px;
  margin-bottom: 32px;
}
.bd-append-icon {
  display: block;
  width: 40px;
  height: 12px;
  /*background: url('../../../assets/medical-logo.png') center center no-repeat;*/
  background-size: 100% 100%;
  margin-right: 8px;
}
.bd-append-text {
  font-size: 14px;
  line-height: 20px;
  color: #909399;
  font-weight: bold;
}
.ft {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 14px;
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: calc(14px + constant(safe-area-inset-bottom));
  padding-bottom: calc(14px + env(safe-area-inset-bottom));
}
.pay {
  display: flex;
}
.pay-label {
  font-size: 16px;
  line-height: 24px;
  color:rgba(0, 0, 0, 0.6);
  margin-right: 5px;
}
.pay-value {
  font-size: 22px;
  line-height: 24px;
  color: #3B71E8;
  font-weight: bold;
}
.btn {
  font-size: 18px;
  font-weight: bold;
  line-height: 24px;
  color: #fff;
  background-color: #0D7CFF;
  padding: 10px 23px;
  border-radius: 24px;
}
.ft:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.2);
  transform: scaleY(0.5);
}




