<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>本地网页</title>
    <style type="text/css">
      .btn {
        display: block;
        margin: 20px auto;
        padding: 5px;
        background-color: #007aff;
        border: 0;
        color: #ffffff;
        height: 40px;
        width: 200px;
      }

      .btn-red {
        background-color: #dd524d;
      }

      .btn-yellow {
        background-color: #f0ad4e;
      }

      .desc {
        padding: 10px;
        color: #999999;
      }
    </style>
  </head>
  <body>
    <p class="desc"
      >web-view 组件加载本地 html 示例，仅在 App 环境下生效。点击下列按钮，跳转至其它页面。</p
    >
    <div class="btn-list">
      <button class="btn" type="button" data-action="navigateTo">navigateTo</button>
      <button class="btn" type="button" data-action="redirectTo">redirectTo</button>
      <button class="btn" type="button" data-action="navigateBack">navigateBack</button>
      <button class="btn" type="button" data-action="reLaunch">reLaunch</button>
      <button class="btn" type="button" data-action="switchTab">switchTab</button>
    </div>
    <p class="desc">网页向应用发送消息。注意：小程序端应用会在此页面后退时接收到消息。</p>
    <div class="btn-list">
      <button class="btn btn-red" type="button" id="postMessage">postMessage</button>
    </div>
    <!-- uni 的 SDK -->
    <script
      type="text/javascript"
      src="https://unpkg.com/@dcloudio/uni-webview-js@0.0.1/index.js"
    ></script>
    <script type="text/javascript">
      document.addEventListener('UniAppJSBridgeReady', function () {
        document.querySelector('.btn-list').addEventListener('click', function (evt) {
          var target = evt.target
          if (target.tagName === 'BUTTON') {
            var action = target.getAttribute('data-action')
            switch (action) {
              case 'switchTab':
                uni.switchTab({
                  url: '/pages/tabBar/API/API',
                })
                break
              case 'reLaunch':
                uni.reLaunch({
                  url: '/pages/tabBar/API/API',
                })
                break
              case 'navigateBack':
                uni.navigateBack({
                  delta: 1,
                })
                break
              default:
                uni[action]({
                  url: '/pages/component/button/button',
                })
                break
            }
          }
        })
        document.querySelector('#postMessage').addEventListener('click', function () {
          uni.postMessage({
            data: {
              action: 'message',
            },
          })
        })
      })
    </script>
  </body>
</html>
